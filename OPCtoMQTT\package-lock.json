{"name": "OPCtoMQTT", "lockfileVersion": 3, "requires": true, "packages": {"": {"dependencies": {"dotenv": "^16.5.0", "luxon": "^3.4.4", "mqtt": "^5.3.5", "node-fetch": "^3.3.2", "node-opcua": "^2.153.0"}}, "node_modules/@babel/runtime": {"version": "7.23.9", "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.23.9.tgz", "integrity": "sha512-0CX6F+BI2s9dkUqr08KFrAIZgNFj75rdBU/DjCyYLIaV/quFjkk6T+EJ2LkZHyZTbEV4L5p97mNkUsHl2wLFAw==", "dependencies": {"regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@leichtgewicht/ip-codec": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/@leichtgewicht/ip-codec/-/ip-codec-2.0.5.tgz", "integrity": "sha512-Vo+PSpZG2/fmgmiNzYK9qWRh8h/CHrwD0mo1h1DzL4yzHNSfWYujGTYsWGreD000gcgmZ7K4Ys6Tx9TxtsKdDw==", "license": "MIT"}, "node_modules/@peculiar/asn1-cms": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-cms/-/asn1-cms-2.3.15.tgz", "integrity": "sha512-B+DoudF+TCrxoJSTjjcY8Mmu+lbv8e7pXGWrhNp2/EGJp9EEcpzjBCar7puU57sGifyzaRVM03oD5L7t7PghQg==", "license": "MIT", "dependencies": {"@peculiar/asn1-schema": "^2.3.15", "@peculiar/asn1-x509": "^2.3.15", "@peculiar/asn1-x509-attr": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-csr": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-csr/-/asn1-csr-2.3.15.tgz", "integrity": "sha512-caxAOrvw2hUZpxzhz8Kp8iBYKsHbGXZPl2KYRMIPvAfFateRebS3136+orUpcVwHRmpXWX2kzpb6COlIrqCumA==", "license": "MIT", "dependencies": {"@peculiar/asn1-schema": "^2.3.15", "@peculiar/asn1-x509": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-ecc": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-ecc/-/asn1-ecc-2.3.15.tgz", "integrity": "sha512-/HtR91dvgog7z/WhCVdxZJ/jitJuIu8iTqiyWVgRE9Ac5imt2sT/E4obqIVGKQw7PIy+X6i8lVBoT6wC73XUgA==", "license": "MIT", "dependencies": {"@peculiar/asn1-schema": "^2.3.15", "@peculiar/asn1-x509": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-pfx": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-pfx/-/asn1-pfx-2.3.15.tgz", "integrity": "sha512-E3kzQe3J2xV9DP6SJS4X6/N1e4cYa2xOAK46VtvpaRk8jlheNri8v0rBezKFVPB1rz/jW8npO+u1xOvpATFMWg==", "license": "MIT", "dependencies": {"@peculiar/asn1-cms": "^2.3.15", "@peculiar/asn1-pkcs8": "^2.3.15", "@peculiar/asn1-rsa": "^2.3.15", "@peculiar/asn1-schema": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-pkcs8": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-pkcs8/-/asn1-pkcs8-2.3.15.tgz", "integrity": "sha512-/PuQj2BIAw1/v76DV1LUOA6YOqh/UvptKLJHtec/DQwruXOCFlUo7k6llegn8N5BTeZTWMwz5EXruBw0Q10TMg==", "license": "MIT", "dependencies": {"@peculiar/asn1-schema": "^2.3.15", "@peculiar/asn1-x509": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-pkcs9": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-pkcs9/-/asn1-pkcs9-2.3.15.tgz", "integrity": "sha512-yiZo/1EGvU1KiQUrbcnaPGWc0C7ElMMskWn7+kHsCFm+/9fU0+V1D/3a5oG0Jpy96iaXggQpA9tzdhnYDgjyFg==", "license": "MIT", "dependencies": {"@peculiar/asn1-cms": "^2.3.15", "@peculiar/asn1-pfx": "^2.3.15", "@peculiar/asn1-pkcs8": "^2.3.15", "@peculiar/asn1-schema": "^2.3.15", "@peculiar/asn1-x509": "^2.3.15", "@peculiar/asn1-x509-attr": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-rsa": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-rsa/-/asn1-rsa-2.3.15.tgz", "integrity": "sha512-p6hsanvPhexRtYSOHihLvUUgrJ8y0FtOM97N5UEpC+VifFYyZa0iZ5cXjTkZoDwxJ/TTJ1IJo3HVTB2JJTpXvg==", "license": "MIT", "dependencies": {"@peculiar/asn1-schema": "^2.3.15", "@peculiar/asn1-x509": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-schema": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-schema/-/asn1-schema-2.3.15.tgz", "integrity": "sha512-QPeD8UA8axQREpgR5UTAfu2mqQmm97oUqahDtNdBcfj3qAnoXzFdQW+aNf/tD2WVXF8Fhmftxoj0eMIT++gX2w==", "license": "MIT", "dependencies": {"asn1js": "^3.0.5", "pvtsutils": "^1.3.6", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-x509": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-x509/-/asn1-x509-2.3.15.tgz", "integrity": "sha512-0dK5xqTqSLaxv1FHXIcd4Q/BZNuopg+u1l23hT9rOmQ1g4dNtw0g/RnEi+TboB0gOwGtrWn269v27cMgchFIIg==", "license": "MIT", "dependencies": {"@peculiar/asn1-schema": "^2.3.15", "asn1js": "^3.0.5", "pvtsutils": "^1.3.6", "tslib": "^2.8.1"}}, "node_modules/@peculiar/asn1-x509-attr": {"version": "2.3.15", "resolved": "https://registry.npmjs.org/@peculiar/asn1-x509-attr/-/asn1-x509-attr-2.3.15.tgz", "integrity": "sha512-TWJVJhqc+IS4MTEML3l6W1b0sMowVqdsnI4dnojg96LvTuP8dga9f76fjP07MUuss60uSyT2ckoti/2qHXA10A==", "license": "MIT", "dependencies": {"@peculiar/asn1-schema": "^2.3.15", "@peculiar/asn1-x509": "^2.3.15", "asn1js": "^3.0.5", "tslib": "^2.8.1"}}, "node_modules/@peculiar/json-schema": {"version": "1.1.12", "resolved": "https://registry.npmjs.org/@peculiar/json-schema/-/json-schema-1.1.12.tgz", "integrity": "sha512-coUfuoMeIB7B8/NMekxaDzLhaYmp0HZNPEjYRm9goRou8UZIC3z21s0sL9AWoCw4EG876QyO3kYrc61WNF9B/w==", "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@peculiar/webcrypto": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@peculiar/webcrypto/-/webcrypto-1.5.0.tgz", "integrity": "sha512-BRs5XUAwiyCDQMsVA9IDvDa7UBR9gAvPHgugOeGng3YN6vJ9JYonyDc0lNczErgtCWtucjR5N7VtaonboD/ezg==", "license": "MIT", "dependencies": {"@peculiar/asn1-schema": "^2.3.8", "@peculiar/json-schema": "^1.1.12", "pvtsutils": "^1.3.5", "tslib": "^2.6.2", "webcrypto-core": "^1.8.0"}, "engines": {"node": ">=10.12.0"}}, "node_modules/@peculiar/x509": {"version": "1.12.3", "resolved": "https://registry.npmjs.org/@peculiar/x509/-/x509-1.12.3.tgz", "integrity": "sha512-+Mzq+W7cNEKfkNZzyLl6A6ffqc3r21HGZUezgfKxpZrkORfOqgRXnS80Zu0IV6a9Ue9QBJeKD7kN0iWfc3bhRQ==", "license": "MIT", "dependencies": {"@peculiar/asn1-cms": "^2.3.13", "@peculiar/asn1-csr": "^2.3.13", "@peculiar/asn1-ecc": "^2.3.14", "@peculiar/asn1-pkcs9": "^2.3.13", "@peculiar/asn1-rsa": "^2.3.13", "@peculiar/asn1-schema": "^2.3.13", "@peculiar/asn1-x509": "^2.3.13", "pvtsutils": "^1.3.5", "reflect-metadata": "^0.2.2", "tslib": "^2.7.0", "tsyringe": "^4.8.0"}}, "node_modules/@ster5/global-mutex": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@ster5/global-mutex/-/global-mutex-2.0.0.tgz", "integrity": "sha512-nlp5BM4E7ybkGt6ouZsohSnliWtXgRoUWHMl8uzi64gKwZSONsssEstfBGnQ0OpdQlE0HBP0qq9RDxP0JTW57w==", "license": "MIT", "dependencies": {"@types/proper-lockfile": "^4.1.2", "proper-lockfile": "^4.1.2"}}, "node_modules/@types/asn1": {"version": "0.2.4", "resolved": "https://registry.npmjs.org/@types/asn1/-/asn1-0.2.4.tgz", "integrity": "sha512-V91DSJ2l0h0gRhVP4oBfBzRBN9lAbPUkGDMCnwedqPKX2d84aAMc9CulOvxdw1f7DfEYx99afab+Rsm3e52jhA==", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/async": {"version": "3.2.24", "resolved": "https://registry.npmjs.org/@types/async/-/async-3.2.24.tgz", "integrity": "sha512-8iHVLHsCCOBKjCF2KwFe0p9Z3rfM9mL+sSP8btyR5vTjJRAqpBYD28/ZLgXPf0pjG1VxOvtCV/BgXkQbpSe8Hw==", "license": "MIT"}, "node_modules/@types/dns-packet": {"version": "5.6.5", "resolved": "https://registry.npmjs.org/@types/dns-packet/-/dns-packet-5.6.5.tgz", "integrity": "sha512-qXOC7XLOEe43ehtWJCMnQXvgcIpv6rPmQ1jXT98Ad8A3TB1Ue50jsCbSSSyuazScEuZ/Q026vHbrOTVkmwA+7Q==", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/jsrsasign": {"version": "10.5.15", "resolved": "https://registry.npmjs.org/@types/jsrsasign/-/jsrsasign-10.5.15.tgz", "integrity": "sha512-3stUTaSRtN09PPzVWR6aySD9gNnuymz+WviNHoTb85dKu+BjaV4uBbWWGykBBJkfwPtcNZVfTn2lbX00U+yhpQ==", "license": "MIT"}, "node_modules/@types/lodash": {"version": "4.17.16", "resolved": "https://registry.npmjs.org/@types/lodash/-/lodash-4.17.16.tgz", "integrity": "sha512-HX7Em5NYQAXKW+1T+FiuG27NGwzJfCX3s1GjOa7ujxZa52kjJLOr4FUxT+giF6Tgxv1e+/czV/iTtBw27WTU9g==", "license": "MIT"}, "node_modules/@types/long": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/@types/long/-/long-4.0.2.tgz", "integrity": "sha512-MqTGEo5bj5t157U6fA/BiDynNkn0YknVdh48CMPkTSpFTVmvao5UQmm7uEF6xBEo7qIMAlY/JSleYaE6VOdpaA==", "license": "MIT"}, "node_modules/@types/mkdirp": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@types/mkdirp/-/mkdirp-1.0.2.tgz", "integrity": "sha512-o0K1tSO0Dx5X6xlU5F1D6625FawhC3dU3iqr25lluNv/+/QIVH8RLNEiVokgIZo+mz+87w/3Mkg/VvQS+J51fQ==", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/multicast-dns": {"version": "7.2.4", "resolved": "https://registry.npmjs.org/@types/multicast-dns/-/multicast-dns-7.2.4.tgz", "integrity": "sha512-ib5K4cIDR4Ro5SR3Sx/LROkMDa0BHz0OPaCBL/OSPDsAXEGZ3/KQeS6poBKYVN7BfjXDL9lWNwzyHVgt/wkyCw==", "license": "MIT", "dependencies": {"@types/dns-packet": "*", "@types/node": "*"}}, "node_modules/@types/node": {"version": "20.11.19", "resolved": "https://registry.npmjs.org/@types/node/-/node-20.11.19.tgz", "integrity": "sha512-7xMnVEcZFu0DikYjWOlRq7NTPETrm7teqUT2WkQjrTIkEgUyyGdWsj/Zg8bEJt5TNklzbPD1X3fqfsHw3SpapQ==", "dependencies": {"undici-types": "~5.26.4"}}, "node_modules/@types/proper-lockfile": {"version": "4.1.4", "resolved": "https://registry.npmjs.org/@types/proper-lockfile/-/proper-lockfile-4.1.4.tgz", "integrity": "sha512-uo2ABllncSqg9F1D4nugVl9v93RmjxF6LJzQLMLDdPaXCUIDPeOJ21Gbqi43xNKzBi/WQ0Q0dICqufzQbMjipQ==", "license": "MIT", "dependencies": {"@types/retry": "*"}}, "node_modules/@types/readable-stream": {"version": "4.0.10", "resolved": "https://registry.npmjs.org/@types/readable-stream/-/readable-stream-4.0.10.tgz", "integrity": "sha512-AbUKBjcC8SHmImNi4yK2bbjogQlkFSg7shZCcicxPQapniOlajG8GCc39lvXzCWX4lLRRs7DM3VAeSlqmEVZUA==", "dependencies": {"@types/node": "*", "safe-buffer": "~5.1.1"}}, "node_modules/@types/retry": {"version": "0.12.5", "resolved": "https://registry.npmjs.org/@types/retry/-/retry-0.12.5.tgz", "integrity": "sha512-3xSjTp3v03X/lSQLkczaN9UIEwJMoMCA1+Nb5HfbJEQWogdeQIyVtTvxPXDQjZ5zws8rFQfVfRdz03ARihPJgw==", "license": "MIT"}, "node_modules/@types/semver": {"version": "7.7.0", "resolved": "https://registry.npmjs.org/@types/semver/-/semver-7.7.0.tgz", "integrity": "sha512-k107IF4+Xr7UHjwDc7Cfd6PRQfbdkiRabXGRjo07b4WyPahFBZCZ1sE+BNxYIJPPg73UkfOsVOLwqVc/6ETrIA==", "license": "MIT"}, "node_modules/@types/sshpk": {"version": "1.17.4", "resolved": "https://registry.npmjs.org/@types/sshpk/-/sshpk-1.17.4.tgz", "integrity": "sha512-5gI/7eJn6wmkuIuFY8JZJ1g5b30H9K5U5vKrvOuYu+hoZLb2xcVEgxhYZ2Vhbs0w/ACyzyfkJq0hQtBfSCugjw==", "license": "MIT", "dependencies": {"@types/asn1": "*", "@types/node": "*"}}, "node_modules/@types/ws": {"version": "8.5.10", "resolved": "https://registry.npmjs.org/@types/ws/-/ws-8.5.10.tgz", "integrity": "sha512-vmQSUcfalpIq0R9q7uTo2lXs6eGIpt9wtnLdMv9LVpIjCA/+ufZRozlVoVelIYixx1ugCBKDhn89vnsEGOCx9A==", "dependencies": {"@types/node": "*"}}, "node_modules/abort-controller": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz", "integrity": "sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==", "dependencies": {"event-target-shim": "^5.0.0"}, "engines": {"node": ">=6.5"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/array-flatten": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/array-flatten/-/array-flatten-2.1.2.tgz", "integrity": "sha512-hNfzcOV8W4NdualtqBFPyVO+54DSJuZGY9qT4pRroB6S9e3iiido2ISIC5h9R2sPJ8H3FHCIiEnsv1lPXO3KtQ==", "license": "MIT"}, "node_modules/asn1": {"version": "0.2.6", "resolved": "https://registry.npmjs.org/asn1/-/asn1-0.2.6.tgz", "integrity": "sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==", "license": "MIT", "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/asn1js": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/asn1js/-/asn1js-3.0.6.tgz", "integrity": "sha512-UOCGPYbl0tv8+006qks/dTgV9ajs97X2p0FAbyS2iyCRrmLSRolDaHdp+v/CLgnzHc3fVB+CwYiUmei7ndFcgA==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"pvtsutils": "^1.3.6", "pvutils": "^1.1.3", "tslib": "^2.8.1"}, "engines": {"node": ">=12.0.0"}}, "node_modules/assert": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/assert/-/assert-2.1.0.tgz", "integrity": "sha512-eLHpSK/Y4nhMJ07gDaAzoX/XAKS8PSaojml3M0DM4JpV1LAi5JOJ/p6H/XWrl8L+DzVEvVCW1z3vWAaB9oTsQw==", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "is-nan": "^1.3.2", "object-is": "^1.1.5", "object.assign": "^4.1.4", "util": "^0.12.5"}}, "node_modules/assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/async": {"version": "3.2.6", "resolved": "https://registry.npmjs.org/async/-/async-3.2.6.tgz", "integrity": "sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==", "license": "MIT"}, "node_modules/available-typed-arrays": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz", "integrity": "sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==", "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/backoff": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/backoff/-/backoff-2.5.0.tgz", "integrity": "sha512-wC5ihrnUXmR2douXmXLCe5O3zg3GKIyvRi/hi58a/XyRxVI+3/yM0PYueQOZXPXQ9pxBislYkw+sF9b7C/RuMA==", "license": "MIT", "dependencies": {"precond": "0.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==", "license": "MIT"}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/bcrypt-pbkdf": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/bl": {"version": "6.0.11", "resolved": "https://registry.npmjs.org/bl/-/bl-6.0.11.tgz", "integrity": "sha512-Ok/NWrEA0mlEEbWzckkZVLq6Nv1m2xZ+i9Jq5hZ9Ph/YEcP5dExqls9wUzpluhQRPzdeT8oZNOXAytta6YN8pQ==", "dependencies": {"@types/readable-stream": "^4.0.0", "buffer": "^6.0.3", "inherits": "^2.0.4", "readable-stream": "^4.2.0"}}, "node_modules/brace-expansion": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz", "integrity": "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/buffer": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/buffer/-/buffer-6.0.3.tgz", "integrity": "sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.2.1"}}, "node_modules/buffer-crc32": {"version": "0.2.13", "resolved": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "integrity": "sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==", "license": "MIT", "engines": {"node": "*"}}, "node_modules/buffer-from": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ=="}, "node_modules/byline": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/byline/-/byline-5.0.0.tgz", "integrity": "sha512-s6webAy+R4SR8XVuJWt2V2rGvhnrhxN+9S15GNuTK3wKPOXFF6RNc+8ug2XhH+2s4f+uudG4kUVYmYOQWL2g0Q==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/call-bind": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz", "integrity": "sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz", "integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chokidar": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/chokidar/-/chokidar-4.0.3.tgz", "integrity": "sha512-Qgzu8kfBvo+cA4962jnP1KkS6Dop5NS6g7R5LFYJr4b8Ub94PPQXUksCw9PvXoeXPRRddRNC5C1JQUR2SMGtnA==", "license": "MIT", "dependencies": {"readdirp": "^4.0.1"}, "engines": {"node": ">= 14.16.0"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/cliui": {"version": "8.0.1", "resolved": "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz", "integrity": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "license": "MIT"}, "node_modules/commist": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/commist/-/commist-3.2.0.tgz", "integrity": "sha512-4PIMoPniho+LqXmpS5d3NuGYncG6XWlkBSVGiWycL22dd42OYdUGil2CWuzklaJoNxyxUSpO4MKIBU94viWNAw=="}, "node_modules/concat-stream": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/concat-stream/-/concat-stream-2.0.0.tgz", "integrity": "sha512-MWufYdFw53ccGjCA+Ol7XJYpAlW6/prSMzuPOTRnJGcGzuhLn4Scrz7qf6o8bROZ514ltazcIFJZevcfbo0x7A==", "engines": ["node >= 6.0"], "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.0.2", "typedarray": "^0.0.6"}}, "node_modules/concat-stream/node_modules/readable-stream": {"version": "3.6.2", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/dashdash": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/data-uri-to-buffer": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/data-uri-to-buffer/-/data-uri-to-buffer-4.0.1.tgz", "integrity": "sha512-0R9ikRb668HB7QDxT1vkpuUBtqc53YyAwMwGeUFKRojY/NWKvdZ+9UYtRfGmhqNbRkTSVpMbmyhXipFFv2cb/A==", "license": "MIT", "engines": {"node": ">= 12"}}, "node_modules/debug": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/define-data-property": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz", "integrity": "sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-properties": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz", "integrity": "sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==", "license": "MIT", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/dequeue": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/dequeue/-/dequeue-1.0.5.tgz", "integrity": "sha512-2FIVJZTaWhUj0Y2uKmDAasTP6ZwFWRjkRc01MYN5jFm96iIzkYyNzGADfJ13C5W7CTN7XO9mBYDcVB68eNybBA==", "engines": {"node": "*"}}, "node_modules/dns-equal": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/dns-equal/-/dns-equal-1.0.0.tgz", "integrity": "sha512-z+paD6YUQsk+AbGCEM4PrOXSss5gd66QfcVBFTKR/HpFL9jCqikS94HYwKww6fQyO7IxrIIyUu+g0Ka9tUS2Cg==", "license": "MIT"}, "node_modules/dns-packet": {"version": "5.6.1", "resolved": "https://registry.npmjs.org/dns-packet/-/dns-packet-5.6.1.tgz", "integrity": "sha512-l4gcSouhcgIKRvyy99RNVOgxXiicE+2jZoNmaNmZ6JXiGajBOJAesk1OBlJuM5k2c+eudGdLxDqXuPCKIj6kpw==", "license": "MIT", "dependencies": {"@leichtgewicht/ip-codec": "^2.0.1"}, "engines": {"node": ">=6"}}, "node_modules/dotenv": {"version": "16.5.0", "resolved": "https://registry.npmjs.org/dotenv/-/dotenv-16.5.0.tgz", "integrity": "sha512-m/C+AwOAr9/W1UOIZUo232ejMNnJAJtYQjUbHoNTBNTJSvqzzDh7vnrei3o3r3m9blf6ZoDkvcw0VmozNRFJxg==", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/ecc-jsbn": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "integrity": "sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==", "license": "MIT", "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "license": "MIT"}, "node_modules/env-paths": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/env-paths/-/env-paths-2.2.1.tgz", "integrity": "sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/event-target-shim": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz", "integrity": "sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==", "engines": {"node": ">=6"}}, "node_modules/events": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/events/-/events-3.3.0.tgz", "integrity": "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==", "engines": {"node": ">=0.8.x"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==", "license": "MIT"}, "node_modules/fast-unique-numbers": {"version": "9.0.0", "resolved": "https://registry.npmjs.org/fast-unique-numbers/-/fast-unique-numbers-9.0.0.tgz", "integrity": "sha512-lgIjiflW23W7qgagregmo5FFzM+m4/dWaDUVneRi2AV7o2k5npggeEX7srSKlYfJU9fKXvQV2Gzk3272fJT65w==", "dependencies": {"@babel/runtime": "^7.23.9", "tslib": "^2.6.2"}, "engines": {"node": ">=18.2.0"}}, "node_modules/fetch-blob": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/fetch-blob/-/fetch-blob-3.2.0.tgz", "integrity": "sha512-7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "paypal", "url": "https://paypal.me/jimmywarting"}], "license": "MIT", "dependencies": {"node-domexception": "^1.0.0", "web-streams-polyfill": "^3.0.3"}, "engines": {"node": "^12.20 || >= 14.13"}}, "node_modules/for-each": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/for-each/-/for-each-0.3.5.tgz", "integrity": "sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg==", "license": "MIT", "dependencies": {"is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/formdata-polyfill": {"version": "4.0.10", "resolved": "https://registry.npmjs.org/formdata-polyfill/-/formdata-polyfill-4.0.10.tgz", "integrity": "sha512-buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g==", "license": "MIT", "dependencies": {"fetch-blob": "^3.1.2"}, "engines": {"node": ">=12.20.0"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==", "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/getpass": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/getpass/-/getpass-0.1.7.tgz", "integrity": "sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==", "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/glob": {"version": "9.3.5", "resolved": "https://registry.npmjs.org/glob/-/glob-9.3.5.tgz", "integrity": "sha512-e1LleDykUz2Iu+MTYdkSsuWX8lvAjAcs0Xef0lNIu0S2wOAzuTxCJtcd9S3cijlwYF18EsU3rzb8jPVobxDh9Q==", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "minimatch": "^8.0.2", "minipass": "^4.2.4", "path-scurry": "^1.6.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==", "license": "ISC"}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz", "integrity": "sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "integrity": "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/help-me": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/help-me/-/help-me-5.0.0.tgz", "integrity": "sha512-7xgomUX6ADmcYzFik0HzAxh/73YlKR9bmFzf51CZwR+b6YtzU2m0u49hQCqV6SvlqIqsaxovfwdvbnsw3b/zpg=="}, "node_modules/hexy": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/hexy/-/hexy-0.3.5.tgz", "integrity": "sha512-UCP7TIZPXz5kxYJnNOym+9xaenxCLor/JyhKieo8y8/bJWunGh9xbhy3YrgYJUQ87WwfXGm05X330DszOfINZw==", "license": "MIT", "bin": {"hexy": "bin/hexy_cmd.js"}, "engines": {"node": ">=10.4"}}, "node_modules/humanize": {"version": "0.0.9", "resolved": "https://registry.npmjs.org/humanize/-/humanize-0.0.9.tgz", "integrity": "sha512-bvZZ7vXpr1RKoImjuQ45hJb5OvE2oJafHysiD/AL3nkqTZH2hFCjQ3YZfCd63FefDitbJze/ispUPP0gfDsT2Q==", "engines": {"node": "*"}}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="}, "node_modules/is-arguments": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/is-arguments/-/is-arguments-1.2.0.tgz", "integrity": "sha512-7bVbi0huj/wrIAOzb8U1aszg9kdi3KN/CyU19CTI7tAoZYEZoL9yCDXpbXN+uPsuWnP02cyug1gleqq+TU+YCA==", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-callable": {"version": "1.2.7", "resolved": "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz", "integrity": "sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-generator-function": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.1.0.tgz", "integrity": "sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==", "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-proto": "^1.0.0", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-nan": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/is-nan/-/is-nan-1.3.2.tgz", "integrity": "sha512-E+zBKpQ2t6MEo1VsonYmluk9NxGrbzpeeLC2xIViuO2EjU2xsXsBPwTr3Ykv9l08UYEVEdWeRZNouaZqF6RN0w==", "license": "MIT", "dependencies": {"call-bind": "^1.0.0", "define-properties": "^1.1.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-regex": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/is-regex/-/is-regex-1.2.1.tgz", "integrity": "sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typed-array": {"version": "1.1.15", "resolved": "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.15.tgz", "integrity": "sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==", "license": "MIT", "dependencies": {"which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/js-sdsl": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/js-sdsl/-/js-sdsl-4.3.0.tgz", "integrity": "sha512-mifzlm2+5nZ+lEcLJMoBK0/IH/bDg8XnJfd/Wq6IP+xoCjLZsTOnV2QpxlVbX9bMnkl5PdEjNtBJ9Cj1NjifhQ==", "funding": {"type": "opencollective", "url": "https://opencollective.com/js-sdsl"}}, "node_modules/jsbn": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg==", "license": "MIT"}, "node_modules/jsrsasign": {"version": "11.1.0", "resolved": "https://registry.npmjs.org/jsrsasign/-/jsrsasign-11.1.0.tgz", "integrity": "sha512-Ov74K9GihaK9/9WncTe1mPmvrO7Py665TUfUKvraXBpu+xcTWitrtuOwcjf4KMU9maPaYn0OuaWy0HOzy/GBXg==", "license": "MIT", "funding": {"url": "https://github.com/kjur/jsrsasign#donations"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==", "license": "MIT"}, "node_modules/long": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/long/-/long-4.0.0.tgz", "integrity": "sha512-XsP+KhQif4bjX1kbuSiySJFNAehNxgLb6hPRGJ9QsUr8ajHkuXGdrHmFUTUUXhDwVX2R5bY4JNZEwbUiMhV+MA==", "license": "Apache-2.0"}, "node_modules/lru-cache": {"version": "10.2.0", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.2.0.tgz", "integrity": "sha512-2bIM8x+VAf6JT4bKAljS1qUWgMsqZRPGJS6FSahIMPVvctcNhyVp7AJu7quxOW9jwkryBReKZY5tY5JYv2n/7Q==", "engines": {"node": "14 || >=16.14"}}, "node_modules/luxon": {"version": "3.4.4", "resolved": "https://registry.npmjs.org/luxon/-/luxon-3.4.4.tgz", "integrity": "sha512-zobTr7akeGHnv7eBOXcRgMeCP6+uyYsczwmeRCauvpvaAltgNyTbLH/+VaEAPUeWBT+1GuNmz4wC/6jtQzbbVA==", "engines": {"node": ">=12"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/minimatch": {"version": "8.0.4", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-8.0.4.tgz", "integrity": "sha512-W0Wvr9HyFXZRGIDgCicunpQ299OKXs9RgZfaukz4qAW/pJhcpUfupc9c+OObPOFueNy8VSrZgEmDtk6Kh4WzDA==", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/minimist": {"version": "1.2.6", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.6.tgz", "integrity": "sha512-Jsjnk4bw3YJqYzbdyBiNsPWHPfO++UGG749Cxs6peCu5Xg4nrena6OVxOYxrQTqww0Jmwt+Ref8rggumkTLz9Q==", "license": "MIT"}, "node_modules/minipass": {"version": "4.2.8", "resolved": "https://registry.npmjs.org/minipass/-/minipass-4.2.8.tgz", "integrity": "sha512-fNzuVyifolSLFL4NzpF+wEF4qrgqaaKX0haXPQEdQ7NKAN+WecoKMHV09YcuL/DHxrUsYQOK3MiuDf7Ip2OXfQ==", "license": "ISC", "engines": {"node": ">=8"}}, "node_modules/mkdirp": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==", "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/mqtt": {"version": "5.3.5", "resolved": "https://registry.npmjs.org/mqtt/-/mqtt-5.3.5.tgz", "integrity": "sha512-xd7qt/LEM721U6yHQcqjlaAKXL1Fsqf/MXq6C2WPi/6OXK2jdSzL1eZ7ZUgjea7IY2yFLRWD5LNdT1mL0arPoA==", "dependencies": {"@types/readable-stream": "^4.0.5", "@types/ws": "^8.5.9", "commist": "^3.2.0", "concat-stream": "^2.0.0", "debug": "^4.3.4", "help-me": "^5.0.0", "lru-cache": "^10.0.1", "minimist": "^1.2.8", "mqtt": "^5.2.0", "mqtt-packet": "^9.0.0", "number-allocator": "^1.0.14", "readable-stream": "^4.4.2", "reinterval": "^1.1.0", "rfdc": "^1.3.0", "split2": "^4.2.0", "worker-timers": "^7.0.78", "ws": "^8.14.2"}, "bin": {"mqtt": "build/bin/mqtt.js", "mqtt_pub": "build/bin/pub.js", "mqtt_sub": "build/bin/sub.js"}, "engines": {"node": ">=16.0.0"}}, "node_modules/mqtt-packet": {"version": "9.0.0", "resolved": "https://registry.npmjs.org/mqtt-packet/-/mqtt-packet-9.0.0.tgz", "integrity": "sha512-8v+HkX+fwbodsWAZIZTI074XIoxVBOmPeggQuDFCGg1SqNcC+uoRMWu7J6QlJPqIUIJXmjNYYHxBBLr1Y/Df4w==", "dependencies": {"bl": "^6.0.8", "debug": "^4.3.4", "process-nextick-args": "^2.0.1"}}, "node_modules/mqtt/node_modules/minimist": {"version": "1.2.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "node_modules/multicast-dns": {"version": "7.2.5", "resolved": "https://registry.npmjs.org/multicast-dns/-/multicast-dns-7.2.5.tgz", "integrity": "sha512-2eznPJP8z2BFLX50tf0LuODrpINqP1RVIm/CObbTcBRITQgmC/TjcREF1NeTBzIcR5XO/ukWo+YHOjBbFwIupg==", "license": "MIT", "dependencies": {"dns-packet": "^5.2.2", "thunky": "^1.0.2"}, "bin": {"multicast-dns": "cli.js"}}, "node_modules/node-domexception": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz", "integrity": "sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==", "deprecated": "Use your platform's native DOMException instead", "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "github", "url": "https://paypal.me/jimmywarting"}], "license": "MIT", "engines": {"node": ">=10.5.0"}}, "node_modules/node-fetch": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-3.3.2.tgz", "integrity": "sha512-dRB78srN/l6gqWulah9SrxeYnxeddIG30+GOqK/9OlLVyLg3HPnr6SqOWTWOXKRwC2eGYCkZ59NNuSgvSrpgOA==", "license": "MIT", "dependencies": {"data-uri-to-buffer": "^4.0.0", "fetch-blob": "^3.1.4", "formdata-polyfill": "^4.0.10"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/node-fetch"}}, "node_modules/node-opcua": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua/-/node-opcua-2.153.0.tgz", "integrity": "sha512-lj9KnTOmx9KpOvawkTVyDhdg/cCNGucMAe+nWFxKUlN64E5eXG3yXHyKci2OF62FcDGEyGcSvTgDrf2ZaGbOXA==", "license": "MIT", "dependencies": {"@types/semver": "^7.7.0", "chalk": "4.1.2", "node-opcua-address-space": "2.153.0", "node-opcua-address-space-for-conformance-testing": "2.153.0", "node-opcua-aggregates": "2.153.0", "node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-certificate-manager": "2.153.0", "node-opcua-client": "2.153.0", "node-opcua-client-proxy": "2.153.0", "node-opcua-common": "2.153.0", "node-opcua-constants": "2.139.0", "node-opcua-crypto": "4.16.0", "node-opcua-data-access": "2.153.0", "node-opcua-data-model": "2.153.0", "node-opcua-data-value": "2.153.0", "node-opcua-debug": "2.153.0", "node-opcua-enum": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-hostname": "2.139.0", "node-opcua-nodeid": "2.153.0", "node-opcua-nodesets": "2.139.0", "node-opcua-numeric-range": "2.153.0", "node-opcua-packet-analyzer": "2.153.0", "node-opcua-secure-channel": "2.153.0", "node-opcua-server": "2.153.0", "node-opcua-server-discovery": "2.153.0", "node-opcua-service-browse": "2.153.0", "node-opcua-service-call": "2.153.0", "node-opcua-service-discovery": "2.153.0", "node-opcua-service-endpoints": "2.153.0", "node-opcua-service-filter": "2.153.0", "node-opcua-service-history": "2.153.0", "node-opcua-service-node-management": "2.153.0", "node-opcua-service-query": "2.153.0", "node-opcua-service-read": "2.153.0", "node-opcua-service-register-node": "2.153.0", "node-opcua-service-secure-channel": "2.153.0", "node-opcua-service-session": "2.153.0", "node-opcua-service-subscription": "2.153.0", "node-opcua-service-translate-browse-path": "2.153.0", "node-opcua-service-write": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-transport": "2.153.0", "node-opcua-types": "2.153.0", "node-opcua-utils": "2.153.0", "node-opcua-variant": "2.153.0", "node-opcua-vendor-diagnostic": "2.153.0", "semver": "^7.7.1"}, "engines": {"node": ">=8.10"}, "funding": {"url": "https://github.com/sponsors/erossignon"}}, "node_modules/node-opcua-address-space": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-address-space/-/node-opcua-address-space-2.153.0.tgz", "integrity": "sha512-vAaAXtOqw2vCmEAUZv24ldW9wCpPF3lrejsKs16XgAJAClB1ro8XVEWSiYeiTbMFsbc1X1WSC4AFxp9ia5TfEg==", "license": "MIT", "dependencies": {"@types/lodash": "4.17.16", "@types/semver": "^7.7.0", "chalk": "4.1.2", "dequeue": "^1.0.5", "lodash": "4.17.21", "node-opcua-address-space-base": "2.153.0", "node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-client-dynamic-extension-object": "2.153.0", "node-opcua-constants": "2.139.0", "node-opcua-crypto": "4.16.0", "node-opcua-data-access": "2.153.0", "node-opcua-data-model": "2.153.0", "node-opcua-data-value": "2.153.0", "node-opcua-date-time": "2.153.0", "node-opcua-debug": "2.153.0", "node-opcua-enum": "2.153.0", "node-opcua-extension-object": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-nodeid": "2.153.0", "node-opcua-nodeset-ua": "2.153.0", "node-opcua-numeric-range": "2.153.0", "node-opcua-object-registry": "2.153.0", "node-opcua-pseudo-session": "2.153.0", "node-opcua-schemas": "2.153.0", "node-opcua-service-browse": "2.153.0", "node-opcua-service-call": "2.153.0", "node-opcua-service-history": "2.153.0", "node-opcua-service-translate-browse-path": "2.153.0", "node-opcua-service-write": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-types": "2.153.0", "node-opcua-utils": "2.153.0", "node-opcua-variant": "2.153.0", "node-opcua-xml2json": "2.153.0", "semver": "^7.7.1", "thenify-ex": "4.4.0", "xml-writer": "^1.7.0"}, "engines": {"node": ">=6.10"}}, "node_modules/node-opcua-address-space-base": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-address-space-base/-/node-opcua-address-space-base-2.153.0.tgz", "integrity": "sha512-eEE143OXgTssD8zBQQLn9sKqzvOGaFAePtQ0ont4auUGPZWl6HVXdVvF3ssj9u2V67WeqD6h7W8d6IuKHMmkvg==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-constants": "2.139.0", "node-opcua-crypto": "4.16.0", "node-opcua-data-model": "2.153.0", "node-opcua-data-value": "2.153.0", "node-opcua-date-time": "2.153.0", "node-opcua-debug": "2.153.0", "node-opcua-extension-object": "2.153.0", "node-opcua-nodeid": "2.153.0", "node-opcua-numeric-range": "2.153.0", "node-opcua-schemas": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-types": "2.153.0", "node-opcua-variant": "2.153.0"}, "engines": {"node": ">=6.10"}}, "node_modules/node-opcua-address-space-for-conformance-testing": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-address-space-for-conformance-testing/-/node-opcua-address-space-for-conformance-testing-2.153.0.tgz", "integrity": "sha512-S+L1kwUnc9z5pZv306Du9rd1G8Wo7KN5pnGJ5jWYat/vlB611p8hAJAQLQ5qWbiZqrKy/xynMmsm/OiKhnZWKg==", "license": "MIT", "dependencies": {"node-opcua-address-space": "2.153.0", "node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-data-access": "2.153.0", "node-opcua-data-model": "2.153.0", "node-opcua-data-value": "2.153.0", "node-opcua-debug": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-nodeid": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-variant": "2.153.0"}}, "node_modules/node-opcua-aggregates": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-aggregates/-/node-opcua-aggregates-2.153.0.tgz", "integrity": "sha512-HBgVRci4ur8SxL9f/eb2BXpZr8cusD27r2dEKyp+HSyzYqK3W716UmX61dLzysT2MYY/RUVht3T5tPwxBhiACA==", "license": "MIT", "dependencies": {"node-opcua-address-space": "2.153.0", "node-opcua-assert": "2.139.0", "node-opcua-constants": "2.139.0", "node-opcua-data-model": "2.153.0", "node-opcua-data-value": "2.153.0", "node-opcua-nodeid": "2.153.0", "node-opcua-numeric-range": "2.153.0", "node-opcua-server": "2.153.0", "node-opcua-service-history": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-types": "2.153.0", "node-opcua-utils": "2.153.0", "node-opcua-variant": "2.153.0"}}, "node_modules/node-opcua-alarm-condition": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-alarm-condition/-/node-opcua-alarm-condition-2.153.0.tgz", "integrity": "sha512-NZEGtjaCNLUmj8opy5xq5HxMFJJ94wDCR05GfPzlFUG0RTIObITv5Xjnfv4vtD0LpCx2MJU0jFbEEiji5PU5Xw==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-constants": "2.139.0", "node-opcua-data-model": "2.153.0", "node-opcua-debug": "2.153.0", "node-opcua-nodeid": "2.153.0", "node-opcua-pseudo-session": "2.153.0", "node-opcua-service-browse": "2.153.0", "node-opcua-service-filter": "2.153.0", "node-opcua-service-read": "2.153.0", "node-opcua-service-subscription": "2.153.0", "node-opcua-service-translate-browse-path": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-types": "2.153.0", "node-opcua-utils": "2.153.0", "node-opcua-variant": "2.153.0", "thenify-ex": "4.4.0"}}, "node_modules/node-opcua-assert": {"version": "2.139.0", "resolved": "https://registry.npmjs.org/node-opcua-assert/-/node-opcua-assert-2.139.0.tgz", "integrity": "sha512-JdIE+FD+orAVxmUjP/naBNsU62ianW1zIg6Ebs/vgzu6ZezfTOdaky/ph02ydPG4nTafFOEvZ8eGeerWqaRY7g==", "license": "MIT", "dependencies": {"chalk": "4.1.2"}}, "node_modules/node-opcua-basic-types": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-basic-types/-/node-opcua-basic-types-2.153.0.tgz", "integrity": "sha512-Oh6TCcgiU1/XdS17Bi2m+zdMRWrO1cO/DBntacyaS/5p1YfgOFTxamqG0xwi8lBn7Ii+cAeg6TPvvWMzdl57Gg==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-buffer-utils": "2.153.0", "node-opcua-date-time": "2.153.0", "node-opcua-guid": "2.139.0", "node-opcua-nodeid": "2.153.0", "node-opcua-status-code": "2.153.0"}}, "node_modules/node-opcua-binary-stream": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-binary-stream/-/node-opcua-binary-stream-2.153.0.tgz", "integrity": "sha512-SIYMJUJtgLr1w1eJEELqt3EpUsQ2ON2jLi/kHYQM9Fz904NUJMgcgeV7s3Y76BDoQSmR74DgHPf/TfLgTwKXCg==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-buffer-utils": "2.153.0"}}, "node_modules/node-opcua-buffer-utils": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-buffer-utils/-/node-opcua-buffer-utils-2.153.0.tgz", "integrity": "sha512-xh8Af5LUKhvxjWHYDsEf9229Quk0eu311U1O+sgBFC1ak5jgI+LatdG52WSroy4o1ksmu/cgTzkFlNXVDQVs+g==", "license": "MIT"}, "node_modules/node-opcua-certificate-manager": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-certificate-manager/-/node-opcua-certificate-manager-2.153.0.tgz", "integrity": "sha512-bp2503MEhNeIfawowIDDGro4PW2bEWw58p3uhS4V4RwjCUk85Cv1f94KKgn6DLaQggkeN9WJYhpeRYmZwpVN3Q==", "license": "MIT", "dependencies": {"@types/mkdirp": "1.0.2", "env-paths": "2.2.1", "mkdirp": "1.0.4", "node-opcua-assert": "2.139.0", "node-opcua-crypto": "4.16.0", "node-opcua-debug": "2.153.0", "node-opcua-object-registry": "2.153.0", "node-opcua-pki": "5.3.0", "node-opcua-status-code": "2.153.0", "thenify-ex": "4.4.0"}}, "node_modules/node-opcua-chunkmanager": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-chunkmanager/-/node-opcua-chunkmanager-2.153.0.tgz", "integrity": "sha512-wPUyyvqeM7wZFgjvcSRPEWkbBIiVu+BeZWwAL4opNzeiJ164BUJIwwxz6sCWV2VoLb9wTXg9uZBB4nkZWaeE3Q==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-buffer-utils": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-packet-assembler": "2.153.0"}}, "node_modules/node-opcua-client": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-client/-/node-opcua-client-2.153.0.tgz", "integrity": "sha512-X22tBWVAjDufwVqbiHji+Rc4rjTE90MVlj81LknuaxVuctSuTE3xCEVblhzEqHV18EKL3OBzKwPmL/JYeFwNgQ==", "license": "MIT", "dependencies": {"@ster5/global-mutex": "^2.0.0", "@types/async": "^3.2.24", "async": "^3.2.6", "chalk": "4.1.2", "node-opcua-alarm-condition": "2.153.0", "node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-buffer-utils": "2.153.0", "node-opcua-certificate-manager": "2.153.0", "node-opcua-client-dynamic-extension-object": "2.153.0", "node-opcua-common": "2.153.0", "node-opcua-constants": "2.139.0", "node-opcua-crypto": "4.16.0", "node-opcua-data-model": "2.153.0", "node-opcua-data-value": "2.153.0", "node-opcua-date-time": "2.153.0", "node-opcua-debug": "2.153.0", "node-opcua-extension-object": "2.153.0", "node-opcua-hostname": "2.139.0", "node-opcua-nodeid": "2.153.0", "node-opcua-object-registry": "2.153.0", "node-opcua-pki": "5.3.0", "node-opcua-pseudo-session": "2.153.0", "node-opcua-schemas": "2.153.0", "node-opcua-secure-channel": "2.153.0", "node-opcua-service-browse": "2.153.0", "node-opcua-service-call": "2.153.0", "node-opcua-service-discovery": "2.153.0", "node-opcua-service-endpoints": "2.153.0", "node-opcua-service-filter": "2.153.0", "node-opcua-service-history": "2.153.0", "node-opcua-service-query": "2.153.0", "node-opcua-service-read": "2.153.0", "node-opcua-service-register-node": "2.153.0", "node-opcua-service-secure-channel": "2.153.0", "node-opcua-service-session": "2.153.0", "node-opcua-service-subscription": "2.153.0", "node-opcua-service-translate-browse-path": "2.153.0", "node-opcua-service-write": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-types": "2.153.0", "node-opcua-utils": "2.153.0", "node-opcua-variant": "2.153.0", "thenify-ex": "4.4.0"}}, "node_modules/node-opcua-client-dynamic-extension-object": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-client-dynamic-extension-object/-/node-opcua-client-dynamic-extension-object-2.153.0.tgz", "integrity": "sha512-xRZsx4AelkGmKCq/KMoas/rR7l0pquWUthJbT8EtQ6pl6rPBIv53kSJbnIRrHrHde6Caon/hUxRmUmfwUjb92g==", "license": "MIT", "dependencies": {"chalk": "4.1.2", "node-opcua-assert": "2.139.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-constants": "2.139.0", "node-opcua-data-model": "2.153.0", "node-opcua-data-value": "2.153.0", "node-opcua-debug": "2.153.0", "node-opcua-extension-object": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-nodeid": "2.153.0", "node-opcua-pseudo-session": "2.153.0", "node-opcua-schemas": "2.153.0", "node-opcua-service-browse": "2.153.0", "node-opcua-service-translate-browse-path": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-types": "2.153.0", "node-opcua-variant": "2.153.0"}}, "node_modules/node-opcua-client-proxy": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-client-proxy/-/node-opcua-client-proxy-2.153.0.tgz", "integrity": "sha512-z0CfMUTQIdCaB1fqTT8P/Q8lol0drJDgGdhFNZVYuuvoqp0pRmq1/50/B/Sb9Y7lA/MR1iKvWqFwQcZMpW61Cw==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-constants": "2.139.0", "node-opcua-data-model": "2.153.0", "node-opcua-data-value": "2.153.0", "node-opcua-debug": "2.153.0", "node-opcua-nodeid": "2.153.0", "node-opcua-pseudo-session": "2.153.0", "node-opcua-service-browse": "2.153.0", "node-opcua-service-call": "2.153.0", "node-opcua-service-read": "2.153.0", "node-opcua-service-subscription": "2.153.0", "node-opcua-service-write": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-utils": "2.153.0", "node-opcua-variant": "2.153.0"}}, "node_modules/node-opcua-common": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-common/-/node-opcua-common-2.153.0.tgz", "integrity": "sha512-bVtXgjVS+5mDwaNAaQGIz0mb7e6EmcYIb1xoWMNwJg93+y58o65uRe1Jwp/wGrAUOIwN9882N9fABbWCOZV9fA==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-crypto": "4.16.0", "node-opcua-types": "2.153.0"}}, "node_modules/node-opcua-constants": {"version": "2.139.0", "resolved": "https://registry.npmjs.org/node-opcua-constants/-/node-opcua-constants-2.139.0.tgz", "integrity": "sha512-C0L+LA2LTHixRFROpIRZ6FHu0Ha752R01xIhi8hUib5GVLGjL+6mdq2KQqlnFy3XPOAXH8z37p1j6EwwJm2HLw==", "license": "MIT"}, "node_modules/node-opcua-crypto": {"version": "4.16.0", "resolved": "https://registry.npmjs.org/node-opcua-crypto/-/node-opcua-crypto-4.16.0.tgz", "integrity": "sha512-r3dRfHc7invXqYt+pjHPEj+YnQDJqfeIikNjWhQSTT9gVWuBOkHDAwPd6QxxJoTHlSp+/mczOWNKmufZ8E0u6Q==", "license": "MIT", "dependencies": {"@peculiar/webcrypto": "^1.5.0", "@peculiar/x509": "^1.12.3", "@types/jsrsasign": "^10.5.15", "@types/sshpk": "^1.17.4", "assert": "^2.1.0", "chalk": "^4.1.2", "hexy": "0.3.5", "jsrsasign": "^11.1.0", "sshpk": "^1.18.0"}}, "node_modules/node-opcua-data-access": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-data-access/-/node-opcua-data-access-2.153.0.tgz", "integrity": "sha512-WWEBiL5OmT969ARcRcWZ0PLsXBgBDgX75+ffnvlCPOsFy5wFsZnLHJn0j8JqotrPF6gw6y/Qkeqz9tTFQ61oBQ==", "license": "MIT", "dependencies": {"node-opcua-data-model": "2.153.0", "node-opcua-types": "2.153.0"}}, "node_modules/node-opcua-data-model": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-data-model/-/node-opcua-data-model-2.153.0.tgz", "integrity": "sha512-xL5OfOGUuba/sFKzZdzASEjFkJphaD7/StPfpFqfML+2X7AeQhp7wfw+WCn5xi0UHfXjlSqQT5l/idbwOwGNNw==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-enum": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-nodeid": "2.153.0", "node-opcua-status-code": "2.153.0"}}, "node_modules/node-opcua-data-value": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-data-value/-/node-opcua-data-value-2.153.0.tgz", "integrity": "sha512-3XSuBTGu2HPWaI5cepqCy4VBwT7ewWkI1CmL/3AKfaZdFrTQo5ij2uyuuvjLARWaZhcPkM5vcFmVczM/NKpG7g==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-data-model": "2.153.0", "node-opcua-date-time": "2.153.0", "node-opcua-enum": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-variant": "2.153.0"}}, "node_modules/node-opcua-date-time": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-date-time/-/node-opcua-date-time-2.153.0.tgz", "integrity": "sha512-JJ<PERSON>+bNLT0LKM/Xkjz7FztCC55vKVd7/XE0rs4XUgoJonbAZ6s6NCNhl9dvOTed7CMyBExIM+Myy7D6kjgjz3A==", "license": "MIT", "dependencies": {"@types/long": "4.0.2", "long": "4.0.0", "node-opcua-assert": "2.139.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-utils": "2.153.0"}}, "node_modules/node-opcua-debug": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-debug/-/node-opcua-debug-2.153.0.tgz", "integrity": "sha512-GR7/fhyfJYb8dNb6QtpILYRZ2Hn26S/TCJI2TelF2Z2z1iBJr0nPpUMrfkcdPmLnCyavYP8wi0SLWBRk0bwUag==", "license": "MIT", "dependencies": {"chalk": "4.1.2", "hexy": "0.3.5", "node-opcua-assert": "2.139.0", "node-opcua-buffer-utils": "2.153.0"}}, "node_modules/node-opcua-enum": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-enum/-/node-opcua-enum-2.153.0.tgz", "integrity": "sha512-sAKLVzTP5JARxIAsh5QksWt8xpJIfio8Zs9KGhwau58kJZb2x0stpzMPRs6s9X9/VOyqXixfacW++lAFQk8wwA==", "license": "MIT"}, "node_modules/node-opcua-extension-object": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-extension-object/-/node-opcua-extension-object-2.153.0.tgz", "integrity": "sha512-RT/xM3bTCMT3vg4BhYgJoKrGZ2FtDRcvfkQcfFaivdJrBTs0IX41s4ar74yyH8z9u83QY7eUwhpWB4B1mUqkpQ==", "license": "MIT", "dependencies": {"chalk": "4.1.2", "node-opcua-basic-types": "2.153.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-debug": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-nodeid": "2.153.0"}}, "node_modules/node-opcua-factory": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-factory/-/node-opcua-factory-2.153.0.tgz", "integrity": "sha512-WxBFd/7omLQIIbKIF0WQ81IgVoHoXzweDwTTT/mshrjibJQY0jsYEqTuy+JbyzkYeHLVZD+sJSRDmB5Uo7PgoA==", "license": "MIT", "dependencies": {"chalk": "4.1.2", "node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-constants": "2.139.0", "node-opcua-debug": "2.153.0", "node-opcua-enum": "2.153.0", "node-opcua-guid": "2.139.0", "node-opcua-nodeid": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-utils": "2.153.0"}}, "node_modules/node-opcua-generator": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-generator/-/node-opcua-generator-2.153.0.tgz", "integrity": "sha512-6NMoba/NkuvBkMBKkHINb8iAamI0Px+Bpvfrw54M0MuKrHasMohdqdHEsXLFAbAPmIJz7mZNNc0+re8hwJPZxg==", "license": "MIT", "dependencies": {"chalk": "4.1.2", "node-opcua-assert": "2.139.0", "node-opcua-constants": "2.139.0", "node-opcua-debug": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-nodeid": "2.153.0", "node-opcua-schemas": "2.153.0", "node-opcua-utils": "2.153.0"}}, "node_modules/node-opcua-guid": {"version": "2.139.0", "resolved": "https://registry.npmjs.org/node-opcua-guid/-/node-opcua-guid-2.139.0.tgz", "integrity": "sha512-YQyU1SWeNYKMg5dw85OW9nG3umUVcVX3VSGO2wxl6oWuWVFgbwLbtz3+y9HVl4gc3LRVb9NPCVOWYmpXVxmPsA==", "license": "MIT"}, "node_modules/node-opcua-hostname": {"version": "2.139.0", "resolved": "https://registry.npmjs.org/node-opcua-hostname/-/node-opcua-hostname-2.139.0.tgz", "integrity": "sha512-hQ6LW6ccqqB8er+w89/mN23bhLy7VI9vOQ47tDMp+gWdWLE/oz/Uqwnk/x4/u1/3040wfEHxE4/vjWYPmvzIVA==", "license": "MIT"}, "node_modules/node-opcua-nodeid": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-nodeid/-/node-opcua-nodeid-2.153.0.tgz", "integrity": "sha512-rBV/0WrAATX4cJm71zhCcuMHAPoVnBgu0zNtFS61hOJOtBa4S/cORdmhPUmdhHJu7+eRsor90A5+fvamMlWPaw==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-constants": "2.139.0", "node-opcua-guid": "2.139.0"}}, "node_modules/node-opcua-nodeset-ua": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-nodeset-ua/-/node-opcua-nodeset-ua-2.153.0.tgz", "integrity": "sha512-n6H9EI9Xgft2dtTc0LK1dLKljYa80QRGJmm7/JgTtxCwSAor06d4gJZCi7ygfOC4FzIbTIXShKewcWbuuvsLhQ==", "license": "MIT", "dependencies": {"node-opcua-address-space-base": "2.153.0", "node-opcua-basic-types": "2.153.0", "node-opcua-data-access": "2.153.0", "node-opcua-data-model": "2.153.0", "node-opcua-data-value": "2.153.0", "node-opcua-extension-object": "2.153.0", "node-opcua-nodeid": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-variant": "2.153.0"}}, "node_modules/node-opcua-nodesets": {"version": "2.139.0", "resolved": "https://registry.npmjs.org/node-opcua-nodesets/-/node-opcua-nodesets-2.139.0.tgz", "integrity": "sha512-qD3jDPxuzQJ/2w1y/S/X8NzOEuWUVZCyIHp4LwRchy/e9fmuHvhawvRGkXUoudFxseeFCiRvM/OKK6LogFImlw==", "license": "MIT"}, "node_modules/node-opcua-numeric-range": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-numeric-range/-/node-opcua-numeric-range-2.153.0.tgz", "integrity": "sha512-eIrOvDe6Slfk85oYSvVumDiMvdaoZtuI/194zrIDnc+FS1hD5Q4/5XlrT1ZeEo4MXOQfmTN9Hi97V7vu1O2wdA==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-status-code": "2.153.0"}}, "node_modules/node-opcua-object-registry": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-object-registry/-/node-opcua-object-registry-2.153.0.tgz", "integrity": "sha512-pAGpVXtxToT29rWZjrcmQLPEd426oHGVkq2alMMvFfS1n95CuCv01f5cwlHTlsEPAVsnA9FpBq+ictyxLicm5A==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-debug": "2.153.0"}}, "node_modules/node-opcua-packet-analyzer": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-packet-analyzer/-/node-opcua-packet-analyzer-2.153.0.tgz", "integrity": "sha512-7YiIuyrhH6M55/YzKXH8cmTNJGlj6qBbpRAXrqRwjsesGHRpM1oiBLU5ntN8q7QUGEyNRHDmrDW8FMphlawR/w==", "license": "MIT", "dependencies": {"chalk": "4.1.2", "node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-debug": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-utils": "2.153.0"}}, "node_modules/node-opcua-packet-assembler": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-packet-assembler/-/node-opcua-packet-assembler-2.153.0.tgz", "integrity": "sha512-g000ETNUL9FHjhw/fpdvHEVJBDiIq3H9EBBj/1pUwH9rdZag+vBMNbnhMcc8G3QFoHdKqaVdDfTOC3schMdL1A==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-debug": "2.153.0"}}, "node_modules/node-opcua-pki": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/node-opcua-pki/-/node-opcua-pki-5.3.0.tgz", "integrity": "sha512-Ne+EzDdiRnpaICoU9WKflPPJK/NNmAE4bnbSd/bNfz+tgrwijmzKWr1ffEtnpTOJvoMJ3hJUaUoj2rT1RFnYcA==", "license": "MIT", "dependencies": {"@ster5/global-mutex": "^2.0.0", "byline": "^5.0.0", "chalk": "4.1.2", "chokidar": "4.0.3", "node-opcua-crypto": "4.16.0", "progress": "^2.0.3", "rimraf": "4.4.1", "wget-improved-2": "^3.3.0", "yargs": "17.7.2", "yauzl": "^3.2.0"}, "bin": {"pki": "bin/crypto_create_CA.js"}}, "node_modules/node-opcua-pseudo-session": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-pseudo-session/-/node-opcua-pseudo-session-2.153.0.tgz", "integrity": "sha512-lzX+b2c3bLkQGO6w70h2ELf9N1p76MikZ3QVFaUJIhOZIzzWOO+OY1kRhssuqvystSAHN8HsUw1hMUGvCi34YQ==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-constants": "2.139.0", "node-opcua-data-model": "2.153.0", "node-opcua-data-value": "2.153.0", "node-opcua-debug": "2.153.0", "node-opcua-nodeid": "2.153.0", "node-opcua-service-browse": "2.153.0", "node-opcua-service-call": "2.153.0", "node-opcua-service-filter": "2.153.0", "node-opcua-service-read": "2.153.0", "node-opcua-service-subscription": "2.153.0", "node-opcua-service-translate-browse-path": "2.153.0", "node-opcua-service-write": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-types": "2.153.0", "node-opcua-utils": "2.153.0", "node-opcua-variant": "2.153.0", "thenify-ex": "4.4.0"}}, "node_modules/node-opcua-schemas": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-schemas/-/node-opcua-schemas-2.153.0.tgz", "integrity": "sha512-AQrEiZkCYqORe3sKP8J70SgZvwn1mtHZNuFPYqZwp66tZIOlZ25jfrfv5tFbE4YTf1BJfT+3X/aNMSlurGzYcA==", "license": "MIT", "dependencies": {"chalk": "4.1.2", "node-opcua-assert": "2.139.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-debug": "2.153.0", "node-opcua-extension-object": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-nodeid": "2.153.0", "node-opcua-variant": "2.153.0", "node-opcua-xml2json": "2.153.0"}}, "node_modules/node-opcua-secure-channel": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-secure-channel/-/node-opcua-secure-channel-2.153.0.tgz", "integrity": "sha512-TUZ9YGQaZmMy3uO8xNN8JF+ntkdsOgB/G0SZd2Bug/Pju1cTx1jzZRnVitbvo9eEUR7mOmGwnNOuip+mHc2MLQ==", "license": "MIT", "dependencies": {"backoff": "^2.5.0", "chalk": "4.1.2", "node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-certificate-manager": "2.153.0", "node-opcua-chunkmanager": "2.153.0", "node-opcua-common": "2.153.0", "node-opcua-crypto": "4.16.0", "node-opcua-debug": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-nodeid": "2.153.0", "node-opcua-object-registry": "2.153.0", "node-opcua-packet-analyzer": "2.153.0", "node-opcua-service-endpoints": "2.153.0", "node-opcua-service-secure-channel": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-transport": "2.153.0", "node-opcua-types": "2.153.0", "node-opcua-utils": "2.153.0"}}, "node_modules/node-opcua-server": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-server/-/node-opcua-server-2.153.0.tgz", "integrity": "sha512-ejuVM7X/MrOqd9eRSJrQUD9tClGB4ustrNXyiahKXANbDiDFbssUv56s0Vr/IRIpMBqCktOuzTntpy1Ipyacpw==", "license": "MIT", "dependencies": {"@ster5/global-mutex": "^2.0.0", "async": "^3.2.6", "chalk": "4.1.2", "dequeue": "^1.0.5", "lodash": "4.17.21", "node-opcua-address-space": "2.153.0", "node-opcua-address-space-base": "2.153.0", "node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-certificate-manager": "2.153.0", "node-opcua-client": "2.153.0", "node-opcua-client-dynamic-extension-object": "2.153.0", "node-opcua-common": "2.153.0", "node-opcua-constants": "2.139.0", "node-opcua-crypto": "4.16.0", "node-opcua-data-model": "2.153.0", "node-opcua-data-value": "2.153.0", "node-opcua-date-time": "2.153.0", "node-opcua-debug": "2.153.0", "node-opcua-extension-object": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-hostname": "2.139.0", "node-opcua-nodeid": "2.153.0", "node-opcua-nodesets": "2.139.0", "node-opcua-numeric-range": "2.153.0", "node-opcua-object-registry": "2.153.0", "node-opcua-secure-channel": "2.153.0", "node-opcua-service-browse": "2.153.0", "node-opcua-service-call": "2.153.0", "node-opcua-service-discovery": "2.153.0", "node-opcua-service-endpoints": "2.153.0", "node-opcua-service-filter": "2.153.0", "node-opcua-service-history": "2.153.0", "node-opcua-service-node-management": "2.153.0", "node-opcua-service-query": "2.153.0", "node-opcua-service-read": "2.153.0", "node-opcua-service-register-node": "2.153.0", "node-opcua-service-secure-channel": "2.153.0", "node-opcua-service-session": "2.153.0", "node-opcua-service-subscription": "2.153.0", "node-opcua-service-translate-browse-path": "2.153.0", "node-opcua-service-write": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-transport": "2.153.0", "node-opcua-types": "2.153.0", "node-opcua-utils": "2.153.0", "node-opcua-variant": "2.153.0", "thenify-ex": "4.4.0"}}, "node_modules/node-opcua-server-discovery": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-server-discovery/-/node-opcua-server-discovery-2.153.0.tgz", "integrity": "sha512-oIgnscTN8yTt8colPAnErVf8oN/yp7N86GqpphzwH8siaFUAAZ1OYC12MVii0IKElSywrGso8xhnZKuyirYpIA==", "license": "MIT", "dependencies": {"chalk": "4.1.2", "env-paths": "2.2.1", "node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-certificate-manager": "2.153.0", "node-opcua-common": "2.153.0", "node-opcua-debug": "2.153.0", "node-opcua-hostname": "2.139.0", "node-opcua-object-registry": "2.153.0", "node-opcua-secure-channel": "2.153.0", "node-opcua-server": "2.153.0", "node-opcua-service-discovery": "2.153.0", "node-opcua-service-endpoints": "2.153.0", "node-opcua-status-code": "2.153.0", "sterfive-bonjour-service": "1.1.4", "thenify-ex": "4.4.0"}}, "node_modules/node-opcua-service-browse": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-service-browse/-/node-opcua-service-browse-2.153.0.tgz", "integrity": "sha512-VPBjETrGekKuwxgVAk4pgA5wxYz/x/eHaXNwIRPg7RWlCLAHXAB7GNqzVRIteMRzb2ku34q/sXxy5iG03F/P0A==", "license": "MIT", "dependencies": {"node-opcua-data-model": "2.153.0", "node-opcua-types": "2.153.0"}}, "node_modules/node-opcua-service-call": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-service-call/-/node-opcua-service-call-2.153.0.tgz", "integrity": "sha512-7WvGxeeBQO78mt18z217gE4LctAtk95OUu4zrCWjMadG/4SLSSeuvrJaFwDYp6CvaZxKD7o8e0sLCqJyQzXxgg==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-nodeid": "2.153.0", "node-opcua-types": "2.153.0", "node-opcua-variant": "2.153.0"}}, "node_modules/node-opcua-service-discovery": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-service-discovery/-/node-opcua-service-discovery-2.153.0.tgz", "integrity": "sha512-kw9QQdtgDVZ2PysW4yt0NvnHVi3T3jIrBw5jTCOCRt4tg3R1JMwgkDrnD7wsT+cnnZGKE7t//n4TovTACrb8aQ==", "license": "MIT", "dependencies": {"chalk": "4.1.2", "node-opcua-assert": "2.139.0", "node-opcua-debug": "2.153.0", "node-opcua-object-registry": "2.153.0", "node-opcua-types": "2.153.0", "sterfive-bonjour-service": "1.1.4"}}, "node_modules/node-opcua-service-endpoints": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-service-endpoints/-/node-opcua-service-endpoints-2.153.0.tgz", "integrity": "sha512-dbE2Awna9ApV9O5r0EPJ/94s0YZgGcvJRac8GJQMB19qXW+eiMnxibpZ0n6ydAlvUxihCOsX+5fGJGn4pfImNg==", "license": "MIT", "dependencies": {"node-opcua-types": "2.153.0"}}, "node_modules/node-opcua-service-filter": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-service-filter/-/node-opcua-service-filter-2.153.0.tgz", "integrity": "sha512-nJa9Gsk+qATBtT/Be6OiVCRayrkKAwOQt+vxgwH2RwKraq6hX31hB6BhkhjstNfxtVFN33EmoknOXok33KmZ4w==", "license": "MIT", "dependencies": {"node-opcua-address-space-base": "2.153.0", "node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-constants": "2.139.0", "node-opcua-data-model": "2.153.0", "node-opcua-data-value": "2.153.0", "node-opcua-debug": "2.153.0", "node-opcua-extension-object": "2.153.0", "node-opcua-nodeid": "2.153.0", "node-opcua-service-translate-browse-path": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-types": "2.153.0", "node-opcua-variant": "2.153.0"}}, "node_modules/node-opcua-service-history": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-service-history/-/node-opcua-service-history-2.153.0.tgz", "integrity": "sha512-q/l5+fEOd6jbOIBbaJV5CMR2c88yIPLnkZik88100ZWu0mJ09iqNVEd9VFcZXKIqenKjQzor3PHpJ9XlV0CqDg==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-data-value": "2.153.0", "node-opcua-types": "2.153.0"}}, "node_modules/node-opcua-service-node-management": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-service-node-management/-/node-opcua-service-node-management-2.153.0.tgz", "integrity": "sha512-+eU4vQ8NRxI9zWYFB+iy7zOi9dbRGORRWaByJH5zFPkIgbkM7EvIHZY2wwgRwzks76CSlXe8YWYyvyR5vKe4Gw==", "license": "MIT", "dependencies": {"node-opcua-types": "2.153.0"}}, "node_modules/node-opcua-service-query": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-service-query/-/node-opcua-service-query-2.153.0.tgz", "integrity": "sha512-P9v/aqK37lpwOO5B9+facJ5s9ag9k6Wi2VUgclczfr6H5qvMYw/SQCJFwtCavx40YTg1u4T1OTIRcGtRIWHljw==", "license": "MIT", "dependencies": {"node-opcua-types": "2.153.0"}}, "node_modules/node-opcua-service-read": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-service-read/-/node-opcua-service-read-2.153.0.tgz", "integrity": "sha512-C0aiHZZBKDlBXX9BQYfNg9TeT46JeOXdRDvZfn579DE0GqFglC8v42Rk88/9Uukiy9smFxLfGgRwELk5Yw5NUA==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-data-model": "2.153.0", "node-opcua-data-value": "2.153.0", "node-opcua-service-secure-channel": "2.153.0", "node-opcua-types": "2.153.0"}}, "node_modules/node-opcua-service-register-node": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-service-register-node/-/node-opcua-service-register-node-2.153.0.tgz", "integrity": "sha512-iESiKGiPcfkMlQ7wI8VQfmOYGi40ciziq5Ad4wVNFKtbjb7nATCV+00+yXdlmkRFt5VBIa1d6xvyu2x8hM3DHg==", "license": "MIT", "dependencies": {"node-opcua-types": "2.153.0"}}, "node_modules/node-opcua-service-secure-channel": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-service-secure-channel/-/node-opcua-service-secure-channel-2.153.0.tgz", "integrity": "sha512-4cOi2++AwwaOW853rioBMT0g/9yervd6eXo4JSWPOdEFi72uV18U6Bla4H7wO7BDP2JiKFhjo7Vk/IM9C/D14g==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-types": "2.153.0"}}, "node_modules/node-opcua-service-session": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-service-session/-/node-opcua-service-session-2.153.0.tgz", "integrity": "sha512-Djz0qZgW9B3Wu0vw5r8R3VTTNII4hMdraR5uGKtsl8ZB58c+AoIAPiVQt5fOBXLkvBV9bSnbQwI2vgsyH3NSFw==", "license": "MIT", "dependencies": {"node-opcua-factory": "2.153.0", "node-opcua-types": "2.153.0"}}, "node_modules/node-opcua-service-subscription": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-service-subscription/-/node-opcua-service-subscription-2.153.0.tgz", "integrity": "sha512-8PYA+4FTXuNs6k6bO6IEK8JybARfAy/5ZIjA9gsPIYOtop3rQq+fgzCcsUX2XxdB7FKR0oeMCuYXxYLG4qkWXw==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-types": "2.153.0", "node-opcua-variant": "2.153.0"}}, "node_modules/node-opcua-service-translate-browse-path": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-service-translate-browse-path/-/node-opcua-service-translate-browse-path-2.153.0.tgz", "integrity": "sha512-ZOt5JG+7jKsHoR8sPE96XctJtl53roSrooewq+UmMKos8WfeNqRvhYS2hrE4UOq+USAOr7lNojgeTRtu5J9azw==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-constants": "2.139.0", "node-opcua-data-model": "2.153.0", "node-opcua-nodeid": "2.153.0", "node-opcua-types": "2.153.0"}}, "node_modules/node-opcua-service-write": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-service-write/-/node-opcua-service-write-2.153.0.tgz", "integrity": "sha512-LnbU/pGIkUCpN/+v8FLwTS9zTawHYgsCsV26ByfjWDFYPVCsVoeIFvWlUY4gBwpBeKu6dL505ObtHn6gmfbZWA==", "license": "MIT", "dependencies": {"node-opcua-types": "2.153.0"}}, "node_modules/node-opcua-status-code": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-status-code/-/node-opcua-status-code-2.153.0.tgz", "integrity": "sha512-CsAmJt4fEOAK+W/ex2q3UwGvlPPKKw/KD3/ImWjMv8JC0hL/pYojH/QqBrybyO+tuzv1RHr8HkvbpZZ0ShG5UQ==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-binary-stream": "2.153.0"}}, "node_modules/node-opcua-transport": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-transport/-/node-opcua-transport-2.153.0.tgz", "integrity": "sha512-5IaNSmiB1xmU3vA6O8m3/SsALNAQpvkLXjgN9czxtmRmEQ/n2Tg22551rWJglx4EaoZNmHtCg2ilr8TXxBVZUg==", "license": "MIT", "dependencies": {"chalk": "4.1.2", "node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-buffer-utils": "2.153.0", "node-opcua-chunkmanager": "2.153.0", "node-opcua-debug": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-object-registry": "2.153.0", "node-opcua-packet-assembler": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-utils": "2.153.0"}}, "node_modules/node-opcua-types": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-types/-/node-opcua-types-2.153.0.tgz", "integrity": "sha512-XiXUHTg3Gy0uxk5vUIUV6cDZDZxb0ACMKAEGBp23RDTbdyugoft5fsQegG7CeKeb3C6wk8IhVPz+GiFwYTwNvQ==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-data-model": "2.153.0", "node-opcua-data-value": "2.153.0", "node-opcua-enum": "2.153.0", "node-opcua-extension-object": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-generator": "2.153.0", "node-opcua-nodeid": "2.153.0", "node-opcua-numeric-range": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-variant": "2.153.0"}}, "node_modules/node-opcua-utils": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-utils/-/node-opcua-utils-2.153.0.tgz", "integrity": "sha512-5H9b3d1wRou50Ozfa/L4iEBfuBUWSbwGTSrw13EWop354EZ+DC1DVx0ud5xNE0/7mLUezKplJTIFBEV2cKUexA==", "license": "MIT", "dependencies": {"chalk": "4.1.2", "node-opcua-assert": "2.139.0"}}, "node_modules/node-opcua-variant": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-variant/-/node-opcua-variant-2.153.0.tgz", "integrity": "sha512-N8SURWmcufzz9ZFLi/FBAsgRyMUqDVmjrz+SVEUzueESc/QLx94IhbWQneh/yNGjTN3Bu7ptRuDWFNoPG9dcig==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-basic-types": "2.153.0", "node-opcua-binary-stream": "2.153.0", "node-opcua-data-model": "2.153.0", "node-opcua-enum": "2.153.0", "node-opcua-factory": "2.153.0", "node-opcua-nodeid": "2.153.0", "node-opcua-utils": "2.153.0"}}, "node_modules/node-opcua-vendor-diagnostic": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-vendor-diagnostic/-/node-opcua-vendor-diagnostic-2.153.0.tgz", "integrity": "sha512-BoIbDFCMn0mSX3HJg/M/xKXr6BHxmYufbMW0BG8j2yiNCbVnnMvBv7mygj0k5QaqyNctJFBGrV8QoeVH4Iywwg==", "license": "MIT", "dependencies": {"humanize": "0.0.9", "node-opcua-address-space": "2.153.0", "node-opcua-assert": "2.139.0", "node-opcua-constants": "2.139.0", "node-opcua-debug": "2.153.0", "node-opcua-server": "2.153.0", "node-opcua-status-code": "2.153.0", "node-opcua-variant": "2.153.0"}}, "node_modules/node-opcua-xml2json": {"version": "2.153.0", "resolved": "https://registry.npmjs.org/node-opcua-xml2json/-/node-opcua-xml2json-2.153.0.tgz", "integrity": "sha512-JyMQdo+AYbbIHMWBa+OenMeYMYVhMbM1ahLcd0pKFZszKoEj+4v9Ft5AohaQJe1NE4BNo11pDn2sQuTwBgdldw==", "license": "MIT", "dependencies": {"node-opcua-assert": "2.139.0", "node-opcua-utils": "2.153.0", "xml-writer": "^1.7.0"}}, "node_modules/number-allocator": {"version": "1.0.14", "resolved": "https://registry.npmjs.org/number-allocator/-/number-allocator-1.0.14.tgz", "integrity": "sha512-OrL44UTVAvkKdOdRQZIJpLkAdjXGTRda052sN4sO77bKEzYYqWKMBjQvrJFzqygI99gL6Z4u2xctPW1tB8ErvA==", "dependencies": {"debug": "^4.3.1", "js-sdsl": "4.3.0"}}, "node_modules/object-is": {"version": "1.1.6", "resolved": "https://registry.npmjs.org/object-is/-/object-is-1.1.6.tgz", "integrity": "sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz", "integrity": "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object.assign": {"version": "4.1.7", "resolved": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.7.tgz", "integrity": "sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==", "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/path-scurry": {"version": "1.11.1", "resolved": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz", "integrity": "sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==", "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/path-scurry/node_modules/minipass": {"version": "7.1.2", "resolved": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "integrity": "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==", "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/pend": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz", "integrity": "sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg==", "license": "MIT"}, "node_modules/possible-typed-array-names": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz", "integrity": "sha512-/+5VFTchJDoVj3bhoqi6UeymcD00DAwb1nJwamzPvHEszJ4FpF6SNNbUbOS8yI56qHzdV8eK0qEfOSiodkTdxg==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/precond": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/precond/-/precond-0.2.3.tgz", "integrity": "sha512-QCYG84SgGyGzqJ/vlMsxeXd/pgL/I94ixdNFyh1PusWmTCyVfPJjZ1K1jvHtsbfnXQs2TSkEP2fR7QiMZAnKFQ==", "engines": {"node": ">= 0.6"}}, "node_modules/process": {"version": "0.11.10", "resolved": "https://registry.npmjs.org/process/-/process-0.11.10.tgz", "integrity": "sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==", "engines": {"node": ">= 0.6.0"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag=="}, "node_modules/progress": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/progress/-/progress-2.0.3.tgz", "integrity": "sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/proper-lockfile": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/proper-lockfile/-/proper-lockfile-4.1.2.tgz", "integrity": "sha512-TjNPblN4BwAWMXU8s9AEz4JmQxnD1NNL7bNOY/AKUzyamc379FWASUhc/K1pL2noVb+XmZKLL68cjzLsiOAMaA==", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.4", "retry": "^0.12.0", "signal-exit": "^3.0.2"}}, "node_modules/pvtsutils": {"version": "1.3.6", "resolved": "https://registry.npmjs.org/pvtsutils/-/pvtsutils-1.3.6.tgz", "integrity": "sha512-PLgQXQ6H2FWCaeRak8vvk1GW462lMxB5s3Jm673N82zI4vqtVUPuZdffdZbPDFRoU8kAhItWFtPCWiPpp4/EDg==", "license": "MIT", "dependencies": {"tslib": "^2.8.1"}}, "node_modules/pvutils": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/pvutils/-/pvutils-1.1.3.tgz", "integrity": "sha512-pMpnA0qRdFp32b1sJl1wOJNxZLQ2cbQx+k6tjNtZ8CpvVhNqEPRgivZ2WOUev2YMajecdH7ctUPDvEe87nariQ==", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/readable-stream": {"version": "4.5.2", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-4.5.2.tgz", "integrity": "sha512-yjavECdqeZ3GLXNgRXgeQEdz9fvDDkNKyHnbHRFtOr7/LcfgBcmct7t/ET+HaCTqfh06OzoAxrkN/IfjJBVe+g==", "dependencies": {"abort-controller": "^3.0.0", "buffer": "^6.0.3", "events": "^3.3.0", "process": "^0.11.10", "string_decoder": "^1.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/readdirp": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/readdirp/-/readdirp-4.1.2.tgz", "integrity": "sha512-GDhwkLfywWL2s6vEjyhri+eXmfH6j1L7JE27WhqLeYzoh/A3DBaYGEj2H/HFZCn/kMfim73FXxEJTw06WtxQwg==", "license": "MIT", "engines": {"node": ">= 14.18.0"}, "funding": {"type": "individual", "url": "https://paulmillr.com/funding/"}}, "node_modules/reflect-metadata": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/reflect-metadata/-/reflect-metadata-0.2.2.tgz", "integrity": "sha512-urBwgfrvVP/eAyXx4hluJivBKzuEbSQs9rKWCrCkbSxNv8mxPcUZKeuoF3Uy4mJl3Lwprp6yy5/39VWigZ4K6Q==", "license": "Apache-2.0"}, "node_modules/regenerator-runtime": {"version": "0.14.1", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz", "integrity": "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw=="}, "node_modules/reinterval": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/reinterval/-/reinterval-1.1.0.tgz", "integrity": "sha512-QIRet3SYrGp0HUHO88jVskiG6seqUGC5iAG7AwI/BV4ypGcuqk9Du6YQBUOUqm9c8pw1eyLoIaONifRua1lsEQ=="}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/retry": {"version": "0.12.0", "resolved": "https://registry.npmjs.org/retry/-/retry-0.12.0.tgz", "integrity": "sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/rfdc": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/rfdc/-/rfdc-1.3.1.tgz", "integrity": "sha512-r5a3l5HzYlIC68TpmYKlxWjmOP6wiPJ1vWv2HeLhNsRZMrCkxeqxiHlQ21oXmQ4F3SiryXBHhAD7JZqvOJjFmg=="}, "node_modules/rimraf": {"version": "4.4.1", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-4.4.1.tgz", "integrity": "sha512-Gk8NlF062+T9CqNGn6h4tls3k6T1+/nXdOcSZVikNVtlRdYpA7wRJJMoXmuvOnLW844rPjdQ7JgXCYM6PPC/og==", "license": "ISC", "dependencies": {"glob": "^9.2.0"}, "bin": {"rimraf": "dist/cjs/src/bin.js"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/safe-buffer": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g=="}, "node_modules/safe-regex-test": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.1.0.tgz", "integrity": "sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "license": "MIT"}, "node_modules/semver": {"version": "7.7.2", "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/set-function-length": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz", "integrity": "sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/signal-exit": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==", "license": "ISC"}, "node_modules/split2": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/split2/-/split2-4.2.0.tgz", "integrity": "sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg==", "engines": {"node": ">= 10.x"}}, "node_modules/sshpk": {"version": "1.18.0", "resolved": "https://registry.npmjs.org/sshpk/-/sshpk-1.18.0.tgz", "integrity": "sha512-2p2KJZTSqQ/I3+HX42EpYOa2l3f8Erv8MWKsy2I9uf4wA7yFIkXRffYdsx86y6z4vHtV8u7g+pPlr8/4ouAxsQ==", "license": "MIT", "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sterfive-bonjour-service": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/sterfive-bonjour-service/-/sterfive-bonjour-service-1.1.4.tgz", "integrity": "sha512-QqDpnBb3KLD6ytdY2KSxsynw1jJAvzfOloQt83GQNXO6CGf84ZY+37tpOEZo1FzgUkFiVsL7pYyg71olDppI/w==", "license": "MIT", "dependencies": {"@types/multicast-dns": "^7.2.1", "array-flatten": "^2.1.2", "dns-equal": "^1.0.0", "fast-deep-equal": "^3.1.3", "multicast-dns": "^7.2.4"}}, "node_modules/string_decoder": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string_decoder/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/thenify-ex": {"version": "4.4.0", "resolved": "https://registry.npmjs.org/thenify-ex/-/thenify-ex-4.4.0.tgz", "integrity": "sha512-YHA/2DlY1vWpqGLCc7BzdT9aTrqyHCbsX5J3zbW68LLeD4RPwd+2tMEWshMP+27ikuTnaeKbFDmjqc7fKWmDew==", "license": "MIT"}, "node_modules/thunky": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/thunky/-/thunky-1.1.0.tgz", "integrity": "sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA==", "license": "MIT"}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "license": "0BSD"}, "node_modules/tsyringe": {"version": "4.10.0", "resolved": "https://registry.npmjs.org/tsyringe/-/tsyringe-4.10.0.tgz", "integrity": "sha512-axr3IdNuVIxnaK5XGEUFTu3YmAQ6lllgrvqfEoR16g/HGnYY/6We4oWENtAnzK6/LpJ2ur9PAb80RBt7/U4ugw==", "license": "MIT", "dependencies": {"tslib": "^1.9.3"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/tsyringe/node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz", "integrity": "sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==", "license": "0BSD"}, "node_modules/tunnel": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/tunnel/-/tunnel-0.0.6.tgz", "integrity": "sha512-1h/Lnq9yajKY2PEbBadPXj3VxsDDu844OnaAo52UVmIzIvwwtBPIuNvkjuzBlTWpfJyUbG3ez0KSBibQkj4ojg==", "license": "MIT", "engines": {"node": ">=0.6.11 <=0.7.0 || >=0.7.3"}}, "node_modules/tweetnacl": {"version": "0.14.5", "resolved": "https://registry.npmjs.org/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA==", "license": "Unlicense"}, "node_modules/typedarray": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA=="}, "node_modules/undici-types": {"version": "5.26.5", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz", "integrity": "sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA=="}, "node_modules/util": {"version": "0.12.5", "resolved": "https://registry.npmjs.org/util/-/util-0.12.5.tgz", "integrity": "sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "is-arguments": "^1.0.4", "is-generator-function": "^1.0.7", "is-typed-array": "^1.1.3", "which-typed-array": "^1.1.2"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw=="}, "node_modules/web-streams-polyfill": {"version": "3.3.3", "resolved": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.3.3.tgz", "integrity": "sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw==", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/webcrypto-core": {"version": "1.8.1", "resolved": "https://registry.npmjs.org/webcrypto-core/-/webcrypto-core-1.8.1.tgz", "integrity": "sha512-P+x1MvlNCXlKbLSOY4cYrdreqPG5hbzkmawbcXLKN/mf6DZW0SdNNkZ+sjwsqVkI4A4Ko2sPZmkZtCKY58w83A==", "license": "MIT", "dependencies": {"@peculiar/asn1-schema": "^2.3.13", "@peculiar/json-schema": "^1.1.12", "asn1js": "^3.0.5", "pvtsutils": "^1.3.5", "tslib": "^2.7.0"}}, "node_modules/wget-improved-2": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/wget-improved-2/-/wget-improved-2-3.3.0.tgz", "integrity": "sha512-NSPde/8mUqgmznPhO7oB5gS8IVUlR7GOlY857IaAf3PkkHbx/6FwZxUhW+GRP1GQbZDnCMF5fPieWXFng8Z43A==", "license": "MIT", "dependencies": {"minimist": "1.2.6", "tunnel": "0.0.6"}, "bin": {"nwget": "bin/nwget"}, "engines": {"node": ">= 0.6.18"}}, "node_modules/which-typed-array": {"version": "1.1.19", "resolved": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.19.tgz", "integrity": "sha512-rEvr90Bck4WZt9HHFC4DJMsjvu7x+r6bImz0/BrbWb7A2djJ8hnZMrWnHo9F8ssv0OMErasDhftrfROTyqSDrw==", "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/worker-timers": {"version": "7.1.2", "resolved": "https://registry.npmjs.org/worker-timers/-/worker-timers-7.1.2.tgz", "integrity": "sha512-iqhXt5+Mc3u2nHj3G/w/E9pXqhlueniA2NlyelB/MQSHQuuW2fmmZGkveAv6yi4SSZvrpbveBBlqPSZ0MDCLww==", "dependencies": {"@babel/runtime": "^7.23.9", "tslib": "^2.6.2", "worker-timers-broker": "^6.1.2", "worker-timers-worker": "^7.0.66"}}, "node_modules/worker-timers-broker": {"version": "6.1.2", "resolved": "https://registry.npmjs.org/worker-timers-broker/-/worker-timers-broker-6.1.2.tgz", "integrity": "sha512-slFupigW5vtkGJ1VBCxYPwXFFRmvfioh02bCltBhbMkt3fFnkAbKBCg61pNTetlD0RAsP09mqx/FB0f4UMoHNw==", "dependencies": {"@babel/runtime": "^7.23.9", "fast-unique-numbers": "^9.0.0", "tslib": "^2.6.2", "worker-timers-worker": "^7.0.66"}}, "node_modules/worker-timers-worker": {"version": "7.0.66", "resolved": "https://registry.npmjs.org/worker-timers-worker/-/worker-timers-worker-7.0.66.tgz", "integrity": "sha512-VCLa0H5K9fE2DVI/9r5zDuFrMQIpNL3UD/h4Ui49fIiRBTgv1Sqe0RM12brr83anBsm103aUQkvKvCBL+KpNtg==", "dependencies": {"@babel/runtime": "^7.23.9", "tslib": "^2.6.2"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/ws": {"version": "8.16.0", "resolved": "https://registry.npmjs.org/ws/-/ws-8.16.0.tgz", "integrity": "sha512-HS0c//TP7Ina87TfiPUz1rQzMhHrl/SG2guqRcTOIUYD2q8uhUdNHZYJUaQ8aTGPzCh+c6oawMKW35nFl1dxyQ==", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xml-writer": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/xml-writer/-/xml-writer-1.7.0.tgz", "integrity": "sha512-elFVMRiV5jb59fbc87zzVa0C01QLBEWP909mRuWqFqrYC5wNTH5QW4AaKMNv7d6zAsuOulkD7wnztZNLQW0Nfg==", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/y18n": {"version": "5.0.8", "resolved": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yargs": {"version": "17.7.2", "resolved": "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz", "integrity": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==", "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/yauzl": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/yauzl/-/yauzl-3.2.0.tgz", "integrity": "sha512-Ow9nuGZE+qp1u4JIPvg+uCiUr7xGQWdff7JQSk5VGYTAZMDe2q8lxJ10ygv10qmSj031Ty/6FNJpLO4o1Sgc+w==", "license": "MIT", "dependencies": {"buffer-crc32": "~0.2.3", "pend": "~1.2.0"}, "engines": {"node": ">=12"}}}}