{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "StormApp Mobile API", "description": "Complete API collection for StormApp mobile application testing", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('access_token', response.access_token);", "    pm.environment.set('refresh_token', response.refresh_token);", "    pm.environment.set('user_id', response.user.id);", "    console.log('Login successful, tokens saved');", "} else {", "    console.log('<PERSON><PERSON> failed');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"operator1\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('access_token', response.access_token);", "    console.log('To<PERSON> refreshed successfully');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refresh_token\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/refresh", "host": ["{{base_url}}"], "path": ["auth", "refresh"]}}}]}, {"name": "Zones", "item": [{"name": "Get All Zones", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/zones", "host": ["{{base_url}}"], "path": ["zones"]}}}, {"name": "Get Zone by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/zones/{{zone_id}}", "host": ["{{base_url}}"], "path": ["zones", "{{zone_id}}"]}}}]}, {"name": "Locations", "item": [{"name": "Get All Locations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/locations", "host": ["{{base_url}}"], "path": ["locations"]}}}, {"name": "Get Location by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/locations/{{location_id}}", "host": ["{{base_url}}"], "path": ["locations", "{{location_id}}"]}}}, {"name": "Get Locations by Zone", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/locations/zone/{{zone_id}}", "host": ["{{base_url}}"], "path": ["locations", "zone", "{{zone_id}}"]}}}, {"name": "Get Location Data (All)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/locations/data", "host": ["{{base_url}}"], "path": ["locations", "data"]}}}, {"name": "Get Location Data by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/locations/{{location_id}}/data", "host": ["{{base_url}}"], "path": ["locations", "{{location_id}}", "data"]}}}]}, {"name": "Tags", "item": [{"name": "Get All Tags", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/tags", "host": ["{{base_url}}"], "path": ["tags"]}}}, {"name": "Get Tag by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/tags/{{tag_id}}", "host": ["{{base_url}}"], "path": ["tags", "{{tag_id}}"]}}}, {"name": "Get Tags by Location", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/tags/location/{{location_id}}", "host": ["{{base_url}}"], "path": ["tags", "location", "{{location_id}}"]}}}]}, {"name": "Data", "item": [{"name": "Get Latest Data", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/data/latest", "host": ["{{base_url}}"], "path": ["data", "latest"]}}}, {"name": "Get Tag History (24h)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/data/history/{{tag_id}}?hours=24", "host": ["{{base_url}}"], "path": ["data", "history", "{{tag_id}}"], "query": [{"key": "hours", "value": "24"}]}}}, {"name": "Get Tag History (1h)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/data/history/{{tag_id}}?hours=1", "host": ["{{base_url}}"], "path": ["data", "history", "{{tag_id}}"], "query": [{"key": "hours", "value": "1"}]}}}, {"name": "Get Dashboard Data", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/data/dashboard", "host": ["{{base_url}}"], "path": ["data", "dashboard"]}}}, {"name": "Get Zone Data", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/data/zones", "host": ["{{base_url}}"], "path": ["data", "zones"]}}}]}, {"name": "Test Scenarios", "item": [{"name": "Test Invalid Login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"invalid\",\n  \"password\": \"wrong\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}, {"name": "Test Without Authorization", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/zones", "host": ["{{base_url}}"], "path": ["zones"]}}}, {"name": "Test Invalid Zone ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/zones/999", "host": ["{{base_url}}"], "path": ["zones", "999"]}}}]}]}