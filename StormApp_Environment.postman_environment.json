{"id": "87654321-4321-4321-4321-210987654321", "name": "StormApp Environment", "values": [{"key": "base_url", "value": "http://192.168.1.70:3000/api", "type": "default", "enabled": true}, {"key": "server_url", "value": "http://192.168.1.70:3000", "type": "default", "enabled": true}, {"key": "access_token", "value": "", "type": "secret", "enabled": true}, {"key": "refresh_token", "value": "", "type": "secret", "enabled": true}, {"key": "user_id", "value": "", "type": "default", "enabled": true}, {"key": "zone_id", "value": "1", "type": "default", "enabled": true}, {"key": "location_id", "value": "1", "type": "default", "enabled": true}, {"key": "tag_id", "value": "3291", "type": "default", "enabled": true}, {"key": "username", "value": "operator1", "type": "default", "enabled": true}, {"key": "password", "value": "operator123", "type": "secret", "enabled": true}, {"key": "admin_username", "value": "admin", "type": "default", "enabled": true}, {"key": "admin_password", "value": "admin123", "type": "secret", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-01-01T12:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}