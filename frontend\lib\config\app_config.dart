/// Application configuration constants
/// This file contains all server URLs and configuration constants used throughout the app
class AppConfig {
  // Private constructor to prevent instantiation
  AppConfig._();

  // ===== SERVER CONFIGURATION =====

  /// Default server URLs for different environments
  static const String defaultServerUrl = 'https://amcstorm.smartdashview.com';
  static const String fallbackServerUrl = 'https://amcstorm.smartdashview.com';

  /// Development server URLs
  static const String devServerUrl = 'https://amcstorm.smartdashview.com';
  static const String devServerUrlLocal = 'https://amcstorm.smartdashview.com';

  /// Production server URLs (update these for your production environment)
  static const String prodServerUrl = 'https://amcstorm.smartdashview.com';
  static const String prodServerUrlBackup = 'https://amcstorm.smartdashview.com';

  // ===== API CONFIGURATION =====

  /// API endpoint paths
  static const String apiBasePath = '/api';
  static const String apiZonesPath = '/zones';
  static const String apiLocationsPath = '/locations';
  static const String apiTagsPath = '/tags';
  static const String apiDataPath = '/data';
  static const String apiDashboardPath = '/data/dashboard';
  static const String apiLatestDataPath = '/data/latest';
  static const String apiHistoryPath = '/data/history';



  // ===== APP SETTINGS =====

  /// Default app settings
  static const int defaultRefreshInterval = 5; // seconds
  static const bool defaultAutoRefresh = true;
  static const String appName = 'AMC Monsoon Critical';
  static const String appVersion = '3.0.0';

  /// Network timeout settings
  static const int httpTimeout = 10; // seconds
  static const int connectionTimeout = 15; // seconds

  /// Authentication settings
  static const bool enableAuthentication = true; // Enable real authentication
  static const bool useDemoCredentials = false; // Use real credentials

  // ===== ENVIRONMENT DETECTION =====

  /// Get the appropriate server URL based on environment
  static String getDefaultServerUrl() {
    // You can add environment detection logic here
    // For now, return the default development URL
    return defaultServerUrl;
  }

  /// Get fallback server URL
  static String getFallbackServerUrl() {
    return fallbackServerUrl;
  }

  /// Check if URL is a valid server URL
  static bool isValidServerUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme &&
             (uri.scheme == 'http' || uri.scheme == 'https') &&
             uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }

  /// Build API URL from server URL
  static String buildApiUrl(String serverUrl) {
    final cleanUrl = serverUrl.endsWith('/')
        ? serverUrl.substring(0, serverUrl.length - 1)
        : serverUrl;
    return '$cleanUrl$apiBasePath';
  }



  /// Build full API endpoint URL
  static String buildEndpointUrl(String serverUrl, String endpoint) {
    final apiUrl = buildApiUrl(serverUrl);
    final cleanEndpoint = endpoint.startsWith('/') ? endpoint : '/$endpoint';
    return '$apiUrl$cleanEndpoint';
  }

  // ===== PREDEFINED SERVER CONFIGURATIONS =====

  /// List of predefined server configurations for easy selection
  static const List<ServerConfig> predefinedServers = [
    ServerConfig(
      name: 'Local Development',
      url: devServerUrl,
      description: 'Local development server (localhost)',
    ),
    ServerConfig(
      name: 'Local Network',
      url: fallbackServerUrl,
      description: 'Local network server (127.0.0.1)',
    ),
    ServerConfig(
      name: 'Network Server',
      url: 'https://amcstorm.smartdashview.com',
      description: 'Network server (storm.smartdashview.com)',
    ),
    ServerConfig(
      name: 'Production',
      url: prodServerUrl,
      description: 'Production server',
    ),
  ];
}

/// Server configuration model
class ServerConfig {
  final String name;
  final String url;
  final String description;

  const ServerConfig({
    required this.name,
    required this.url,
    required this.description,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'url': url,
      'description': description,
    };
  }

  /// Create from JSON
  factory ServerConfig.fromJson(Map<String, dynamic> json) {
    return ServerConfig(
      name: json['name'] ?? '',
      url: json['url'] ?? '',
      description: json['description'] ?? '',
    );
  }
}
