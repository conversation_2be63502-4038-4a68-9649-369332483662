import jwt from 'jsonwebtoken';
import { executeQuery } from '../config/database.js';

// Middleware to verify JWT token
export const verifyToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({ 
        success: false, 
        message: 'Access token is required' 
      });
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'storm_app_secret_key');

    // Check if user still exists and is active
    const query = 'SELECT id, username, email, role FROM app_users WHERE id = ? AND is_active = 1';
    const users = await executeQuery(query, [decoded.id]);

    if (users.length === 0) {
      return res.status(401).json({ 
        success: false, 
        message: 'User not found or inactive' 
      });
    }

    // Add user info to request object
    req.user = users[0];
    next();

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ 
        success: false, 
        message: 'Invalid token' 
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        success: false, 
        message: 'Token expired' 
      });
    }

    console.error('Token verification error:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
};

// Middleware to check if user is authenticated (for static files)
export const requireAuth = (req, res, next) => {
  // Check if request has valid session/token
  const token = req.cookies?.auth_token || req.headers.authorization?.split(' ')[1];
  
  if (!token) {
    // Redirect to login page for admin routes
    if (req.path.startsWith('/admin') && !req.path.includes('login')) {
      return res.redirect('/admin/login.html');
    }
    return res.status(401).json({ 
      success: false, 
      message: 'Authentication required' 
    });
  }

  try {
    jwt.verify(token, process.env.JWT_SECRET || 'storm_app_secret_key');
    next();
  } catch (error) {
    // Redirect to login page for admin routes
    if (req.path.startsWith('/admin') && !req.path.includes('login')) {
      return res.redirect('/admin/login.html');
    }
    return res.status(401).json({ 
      success: false, 
      message: 'Invalid or expired token' 
    });
  }
};

export default { verifyToken, requireAuth };
