import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

import '../config/app_config.dart';
import '../models/user.dart';

class AuthProvider extends ChangeNotifier {
  // Private fields
  User? _user;
  String? _token;
  String? _sessionToken;
  DateTime? _expiresAt;
  bool _isLoading = false;
  bool _isInitialized = false;
  String? _error;

  // Getters
  User? get user => _user;
  String? get token => _token;
  String? get sessionToken => _sessionToken;
  DateTime? get expiresAt => _expiresAt;
  bool get isLoading => _isLoading;
  bool get isInitialized => _isInitialized;
  String? get error => _error;
  bool get isAuthenticated {
    final result = _isInitialized && _user != null && _token != null && _sessionToken != null;
    final tokenPreview = _token != null && _token!.length >= 10 ? _token!.substring(0, 10) : _token;
    final sessionPreview = _sessionToken != null && _sessionToken!.length >= 10 ? _sessionToken!.substring(0, 10) : _sessionToken;
    print('isAuthenticated check: initialized=$_isInitialized, user=${_user?.username}, token=$tokenPreview..., session=$sessionPreview..., result=$result');
    return result;
  }
  bool get isTokenExpired => _expiresAt != null && DateTime.now().isAfter(_expiresAt!);

  // SharedPreferences keys
  static const String _userKey = 'user';
  static const String _tokenKey = 'token';
  static const String _sessionTokenKey = 'session_token';
  static const String _expiresAtKey = 'expires_at';

  // Constructor
  AuthProvider() {
    _loadStoredAuth();
  }

  // Load stored authentication data
  Future<void> _loadStoredAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final userJson = prefs.getString(_userKey);
      final token = prefs.getString(_tokenKey);
      final sessionToken = prefs.getString(_sessionTokenKey);
      final expiresAtString = prefs.getString(_expiresAtKey);

      if (userJson != null && token != null && sessionToken != null && expiresAtString != null) {
        _user = User.fromJson(json.decode(userJson));
        _token = token;
        _sessionToken = sessionToken;
        _expiresAt = DateTime.parse(expiresAtString);

        // Check if token is expired
        if (isTokenExpired) {
          print('Stored token is expired, clearing auth');
          await _clearStoredAuth();
        } else {
          print('Found stored auth, verifying with server');
          // Verify token with server
          final isValid = await _verifyToken();
          if (!isValid) {
            print('Token verification failed, clearing auth');
            await _clearStoredAuth();
          }
        }
      } else {
        print('No stored authentication found');
      }
    } catch (e) {
      print('Error loading stored auth: $e');
      await _clearStoredAuth();
    }

    _isInitialized = true;
    notifyListeners();
  }

  // Save authentication data to storage
  Future<void> _saveAuth() async {
    try {
      print('_saveAuth called: user=${_user?.username}, token=${_token?.length}chars, session=${_sessionToken?.length}chars, expires=$_expiresAt');

      final prefs = await SharedPreferences.getInstance();

      if (_user != null && _token != null && _sessionToken != null && _expiresAt != null) {
        print('All auth data is not null, proceeding to save...');

        final userJson = json.encode(_user!.toJson());
        await prefs.setString(_userKey, userJson);
        await prefs.setString(_tokenKey, _token!);
        await prefs.setString(_sessionTokenKey, _sessionToken!);
        await prefs.setString(_expiresAtKey, _expiresAt!.toIso8601String());
        print('Auth data saved successfully');
      } else {
        print('Some auth data is null, not saving: user=${_user != null}, token=${_token != null}, session=${_sessionToken != null}, expires=${_expiresAt != null}');
      }
    } catch (e) {
      print('Error saving auth: $e');
      // Don't clear auth data on save error, just log it
    }
  }

  // Clear stored authentication data
  Future<void> _clearStoredAuth() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userKey);
      await prefs.remove(_tokenKey);
      await prefs.remove(_sessionTokenKey);
      await prefs.remove(_expiresAtKey);
    } catch (e) {
      print('Error clearing stored auth: $e');
    }
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Set error state
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Login method
  Future<bool> login(String username, String password, {String? deviceInfo}) async {
    _setLoading(true);
    _setError(null);



    // Normal authentication mode
    try {
      final response = await http.post(
        Uri.parse('${AppConfig.getDefaultServerUrl()}/api/app-auth/login'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'username': username,
          'password': password,
          'deviceInfo': deviceInfo ?? 'Flutter Mobile App',
        }),
      ).timeout(Duration(seconds: AppConfig.httpTimeout));

      final responseData = json.decode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        final data = responseData['data'];

        _user = User.fromJson(data['user']);
        _token = data['token'];
        _sessionToken = data['sessionToken'];
        _expiresAt = DateTime.parse(data['expiresAt']);

        await _saveAuth();
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError(responseData['error'] ?? 'Login failed');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Network error: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  // Logout method
  Future<void> logout() async {
    try {
      if (_sessionToken != null) {
        // Notify server about logout
        await http.post(
          Uri.parse('${AppConfig.getDefaultServerUrl()}/api/app-auth/logout'),
          headers: {'Content-Type': 'application/json'},
          body: json.encode({
            'sessionToken': _sessionToken,
          }),
        ).timeout(const Duration(seconds: AppConfig.httpTimeout));
      }
    } catch (e) {
      print('Error during logout: $e');
    }

    // Clear local data
    _user = null;
    _token = null;
    _sessionToken = null;
    _expiresAt = null;
    _error = null;

    await _clearStoredAuth();
    notifyListeners();
  }

  // Verify token with server
  Future<bool> _verifyToken() async {
    if (_token == null || _sessionToken == null) {
      return false;
    }



    try {
      final response = await http.post(
        Uri.parse('${AppConfig.getDefaultServerUrl()}/api/app-auth/verify'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'token': _token,
          'sessionToken': _sessionToken,
        }),
      ).timeout(const Duration(seconds: AppConfig.httpTimeout));

      final responseData = json.decode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        // Update user data if needed
        final userData = responseData['data']['user'];
        _user = User.fromJson(userData);
        await _saveAuth();
        return true;
      } else {
        await _clearStoredAuth();
        _user = null;
        _token = null;
        _sessionToken = null;
        _expiresAt = null;
        notifyListeners();
        return false;
      }
    } catch (e) {
      print('Error verifying token: $e');
      return false;
    }
  }

  // Refresh token
  Future<bool> refreshToken() async {
    if (_sessionToken == null) {
      return false;
    }



    try {
      final response = await http.post(
        Uri.parse('${AppConfig.getDefaultServerUrl()}/api/app-auth/refresh'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'sessionToken': _sessionToken,
        }),
      ).timeout(const Duration(seconds: AppConfig.httpTimeout));

      final responseData = json.decode(response.body);

      if (response.statusCode == 200 && responseData['success'] == true) {
        final data = responseData['data'];

        _token = data['token'];
        _expiresAt = DateTime.parse(data['expiresAt']);

        await _saveAuth();
        notifyListeners();
        return true;
      } else {
        await logout();
        return false;
      }
    } catch (e) {
      print('Error refreshing token: $e');
      return false;
    }
  }

  // Get authorization headers for API requests
  Map<String, String> getAuthHeaders() {
    if (_token == null || _sessionToken == null) {
      return {};
    }

    return {
      'Authorization': 'Bearer $_token',
      'X-Session-Token': _sessionToken!,
      'Content-Type': 'application/json',
    };
  }

  // Check if authentication is valid and refresh if needed
  Future<bool> ensureAuthenticated() async {
    if (!isAuthenticated) {
      return false;
    }



    // If token expires in less than 1 hour, refresh it
    if (_expiresAt != null &&
        DateTime.now().add(const Duration(hours: 1)).isAfter(_expiresAt!)) {
      return await refreshToken();
    }

    return true;
  }
}
