class User {
  final int id;
  final String username;
  final String? email;
  final String? fullName;
  final String role;

  User({
    required this.id,
    required this.username,
    this.email,
    this.fullName,
    required this.role,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      username: json['username'],
      email: json['email'],
      fullName: json['fullName'],
      role: json['role'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'fullName': fullName,
      'role': role,
    };
  }

  // Helper methods
  String get displayName => fullName ?? username;
  
  bool get isOperator => role == 'operator';
  bool get isViewer => role == 'viewer';
  bool get isManager => role == 'manager';
  bool get isAdmin => role == 'admin';

  // Role-based permissions
  bool get canViewData => true; // All users can view data
  bool get canControlPumps => isOperator || isManager;
  bool get canManageSettings => isManager;
  bool get canManageUsers => isAdmin;

  @override
  String toString() {
    return 'User{id: $id, username: $username, role: $role}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
