import {
    OPCUAClient,
    BrowseDirection,
    AttributeIds,
    NodeClassMask,
    makeBrowsePath,
    resolveNodeId,
    TimestampsToReturn,
    coerceInt32,
    MessageSecurityMode,
    SecurityPolicy, UserTokenType, timestamp,
} from "node-opcua";

import fs from 'fs';
import fetch from 'node-fetch';
const eps = 1e-9;
import mqtt from "mqtt";
import { DateTime } from "luxon";
const client = mqtt.connect("mqtt://ftp.rmcwwscada.com", {
    username: 'amcstorm',
    password: 'amc@127'
});

const endpointUrl = "opc.tcp://10.147.17.162:49320";
var LastValueMaps = new Map();
import dotenv from 'dotenv';
dotenv.config();

(async () => {
    const opcuaclient = OPCUAClient.create({
        /*  type: UserTokenType.Certificate,
          certificateData: fs.readFileSync('bruce1.pem'),
          privateKey: fs.readFileSync('bruce1_key_nopass.pem', 'utf8'),
          securityMode: MessageSecurityMode.SignAndEncrypt,
          securityPolicy: SecurityPolicy.Basic256,*/
        securityPolicy: SecurityPolicy.None,
        endpointMustExist: false
    });
    opcuaclient.on("backoff", (retry, delay) =>
        console.log("still trying to connect to ", endpointUrl, ": retry =", retry, "next attempt in ", delay / 1000, "seconds")
    );
    const subscriptionParameters = {
        maxNotificationsPerPublish: 1000,
        publishingEnabled: true,
        requestedLifetimeCount: 100,
        requestedMaxKeepAliveCount: 10,
        requestedPublishingInterval: 1000
    };

    // Helper to discover nodes to monitor
    async function discoverNodesToMonitor(session) {
        const browseResult = await session.browse({
            nodeId: resolveNodeId("RootFolder"),
            referenceTypeId: "Organizes",
            browseDirection: BrowseDirection.Forward,
            includeSubtypes: true,
            nodeClassMask: NodeClassMask.Object,
            resultMask: 63
        });


        if (browseResult.statusCode.isGood()) {
            console.log(" rootFolder contains: ");
            for (let reference of browseResult.references) {
                console.log("  ", reference.browseName.toString(), reference.nodeId.toString());
            }
        } else {
            console.log("cannot browse rootFolder", browseResult.toString());
        }
        //       console.log(browseResult.references);
        const Browselocations = await session.browse({

            nodeId: resolveNodeId(browseResult.references[0].nodeId.toString()),
            referenceTypeId: "Organizes",
            browseDirection: BrowseDirection.Forward,
            includeSubtypes: true,
            nodeClassMask: NodeClassMask.Object,
            resultMask: 63
        });


        var nodestoMonitor = [];
        //  console.log(browseResult2.references);
        let noOfLocations = 0;

        for (let reference of Browselocations.references) {

            let locationIdentifier = reference.browseName.name.toString();
            let locNamespaceIndex = reference.browseName.namespaceIndex.toString();
            console.log(locationIdentifier);
            // Location Names expected

            noOfLocations++;
            const BrowseLocations = await session.browse({
                nodeId: resolveNodeId("ns=" + locNamespaceIndex + ";s=" + locationIdentifier),
                referenceTypeId: "Organizes",
                browseDirection: BrowseDirection.Forward,
                includeSubtypes: true,
                nodeClassMask: NodeClassMask.Object,
                resultMask: 63
            });

            var locationDirectories = [];
            for (let reference of BrowseLocations.references) {
                let mfmNamespaceIndex = reference.browseName.namespaceIndex.toString();
                let MFMindentifier = reference.browseName.name.toString();
                if (MFMindentifier.includes("PUMP")) {
                    //   console.log(MFMindentifier);

                    let locNodeId = "ns=" + mfmNamespaceIndex + ";s=" + locationIdentifier + "." + MFMindentifier;

                    const BrowseParams = await session.browse({
                        nodeId: resolveNodeId(locNodeId),
                        referenceTypeId: "HasComponent",
                        browseDirection: BrowseDirection.Forward,
                        includeSubtypes: true,
                        nodeClassMask: NodeClassMask.Variable,
                        resultMask: 63
                    });
                    for (let reference of BrowseParams.references) {
                        const tagName = reference.browseName.name.toString();
                        if ((tagName.includes("LEVEL_") || tagName.includes("CURRENT_DAY")
                            || tagName.includes("PREVIOUS_DAY") ||
                            tagName.toString().includes("TOTALIZER") ||
                            tagName.includes("_RFB")) && !tagName.includes("RPT_")
                            && !tagName.includes("_MAX") && !tagName.includes("_MIN") && !tagName.includes("_ALARM") &&
                            !tagName.includes("LAST_DAY_TOTALIZER")
                        ) {
                            let mfmNamespaceIndex = reference.browseName.namespaceIndex.toString();
                            let TagIdentifier = reference.browseName.name.toString();

                            //   console.log(MFMindentifier);
                            let mfmNodeId = "ns=" + mfmNamespaceIndex + ";s=" + locationIdentifier + "." + MFMindentifier + "." + TagIdentifier;
                            //ns=2;s=AKHBAR NAGAR.5 PUMP.LEVEL_1
                            nodestoMonitor.push(mfmNodeId);
                        }
                    }

                }
            }


        }
        console.log(nodestoMonitor);

        // Write nodes to CSV file
        const csvContent = nodestoMonitor.join('\n');
        fs.writeFileSync('nodes_to_monitor.csv', csvContent, 'utf8');
        console.log('Nodes have been written to nodes_to_monitor.csv');


        return nodestoMonitor;
    }

    // Helper to write nodes to CSV
    function writeNodesToCSV(nodes, filename = 'nodes_to_monitor.csv') {
        const csvContent = nodes.join('\n');
        fs.writeFileSync(filename, csvContent, 'utf8');
        console.log(`Nodes have been written to ${filename}`);
    }

    // Helper to read nodes from CSV
    function readNodesFromCSV(filename = 'nodes_to_monitor.csv') {
        if (!fs.existsSync(filename)) {
            throw new Error(`CSV file ${filename} not found.`);
        }
        const content = fs.readFileSync(filename, 'utf8');
        return content.split(/\r?\n/).filter(Boolean);
    }

    // Helper to monitor nodes
    async function monitorNodes(session, subscription, nodestoMonitor) {
        const paramsToMonitor = [];
        for (let i = 0; i < nodestoMonitor.length; i++) {
            paramsToMonitor.push({
                nodeId: nodestoMonitor[i],
                attributeId: AttributeIds.Value
            });
        }
        // console.log(paramsToMonitor);
        const monitoringParameters = {
            samplingInterval: 5000,
            discardOldest: true,
            queueSize: paramsToMonitor.length
        };

        // Read and publish the current value for each node at startup, in batches
        const batchSize = 50;
        for (let batchStart = 0; batchStart < paramsToMonitor.length; batchStart += batchSize) {
            const batch = paramsToMonitor.slice(batchStart, batchStart + batchSize);
            try {
                const dataValues = await session.read(batch);
                for (let i = 0; i < batch.length; i++) {
                    let nodeID = batch[i].nodeId;
                    // Extract value after 's=' if present
                    let nodeIDStr = String(nodeID);
                    let sIndex = nodeIDStr.indexOf(';s=');
                    let tagName = sIndex !== -1 ? nodeIDStr.substring(sIndex + 3) : nodeIDStr;
                    //  console.log(sIndex);
                    let dataValue = dataValues[i];
                    let value = dataValue.value.value;
                    let valueformated;
                    if (typeof value === 'boolean') {
                        valueformated = value ? 1 : 0;
                    } else {
                        valueformated = isNaN(value) ? 'invalid' : value;
                        valueformated = (valueformated + eps).toFixed(3);
                        if (isNaN(valueformated)) {
                            continue;
                        }
                    }
                    let timestamp = dataValue.sourceTimestamp ? dataValue.sourceTimestamp.toISOString() : new Date().toISOString();
                    timestamp = Math.floor(DateTime.fromISO(timestamp).toSeconds());
                    publishtoMQTT(tagName, {
                        nodeID: tagName,
                        value: valueformated,
                        timestamp: timestamp
                    });
                    LastValueMaps.set(tagName, { value: valueformated, timestamp: timestamp });
                    console.log(tagName + ":" + valueformated + ":" + timestamp + " (initial value)");
                }
            } catch (err) {
                console.error('Error reading initial values batch:', err.message);
            }
        }

        const monitoredItems = await subscription.monitorItems(paramsToMonitor, monitoringParameters, TimestampsToReturn.Both);

        // Read and publish the current value for each node at startup

        monitoredItems.on("changed", function (items, data) {
            let valueformated;
            if (typeof data.value.value === 'boolean') {
                valueformated = data.value.value ? 1 : 0;
            } else {
                valueformated = isNaN(data.value.value) ? 'invalid' : data.value.value;
                valueformated = (valueformated + eps).toFixed(3);
                if (isNaN(valueformated)) {
                    return;
                }
            }
            let nodeID = items.itemToMonitor.nodeId.value;

            let timestamp = data.sourceTimestamp.toISOString();
            timestamp = Math.floor(DateTime.fromISO(timestamp).toSeconds());
            if (LastValueMaps.get(nodeID) === undefined) {
                publishtoMQTT(nodeID, {
                    nodeID: nodeID,
                    value: valueformated,
                    timestamp: timestamp
                });
                LastValueMaps.set(nodeID, { value: valueformated, timestamp: timestamp })
                console.log(nodeID + ":" + valueformated + ":" + timestamp);
            } else {
                let lastloggedValue = LastValueMaps.get(nodeID).value;
                let lastloggedTime = LastValueMaps.get(nodeID).timestamp;
                let currentValue = valueformated;
                let currentTime = timestamp;
                if (currentTime - lastloggedTime > 10) {
                    publishtoMQTT(nodeID, {
                        nodeID: nodeID,
                        value: valueformated,
                        timestamp: timestamp
                    });
                    LastValueMaps.set(nodeID, { value: valueformated, timestamp: timestamp });
                    console.log(nodeID + ":" + valueformated + ":" + timestamp);
                } else {
                    if (nodeID.includes('_RFB')) {
                        if ((lastloggedValue !== currentValue)) {
                            publishtoMQTT(nodeID, {
                                nodeID: nodeID,
                                value: valueformated,
                                timestamp: timestamp
                            });
                            LastValueMaps.set(nodeID, { value: valueformated, timestamp: timestamp });
                            console.log(nodeID + ":" + valueformated + ":" + timestamp);
                        }
                    }
                }
            }
        });
    }

    // Main mode selection
    const mode = process.argv[2] || 'mode3';

    if (mode === 'mode1') {
        // Discover and save to CSV, then exit
        await opcuaclient.withSessionAsync(endpointUrl, async (session) => {
            const nodes = await discoverNodesToMonitor(session);
            writeNodesToCSV(nodes);
        });
        return;
    }
    if (mode === 'mode2') {
        // Discover, save to CSV, then monitor
        await opcuaclient.withSubscriptionAsync(endpointUrl, subscriptionParameters, async (session, subscription) => {
            const nodes = await discoverNodesToMonitor(session);
            writeNodesToCSV(nodes);
            await monitorNodes(session, subscription, nodes);
            console.log("CTRL+C to stop");
            await new Promise((resolve) => process.once("SIGINT", resolve));
        });
        return;
    }
    if (mode === 'mode3') {
        // Read from CSV and monitor
        const nodes = readNodesFromCSV();
        await opcuaclient.withSubscriptionAsync(endpointUrl, subscriptionParameters, async (session, subscription) => {
            await monitorNodes(session, subscription, nodes);
            console.log("CTRL+C to stop");
            await new Promise((resolve) => process.once("SIGINT", resolve));
        });
        return;
    }
    if (mode === 'mode4') {
        // Get server IP from .env
        const serverIp = process.env.SERVER_IP;
        if (!serverIp) {
            console.error('SERVER_IP not set in .env file.');
            return;
        }
        // Fetch OPC tags from remote API and monitor those nodes
        const url = `http://${serverIp}/api/public/tags/opc-addresses`;
        (async () => {
            let currentTags = new Set();
            let monitoredItems = null;
            let session = null;
            let subscription = null;
            let isFirst = true;
            const opcuaclientLocal = opcuaclient;
            // Helper to subscribe to a set of tags
            async function subscribeToTags(tags) {
                if (!session || !subscription) {
                    // Create session and subscription if not already
                    await opcuaclientLocal.withSubscriptionAsync(endpointUrl, subscriptionParameters, async (sess, sub) => {
                        session = sess;
                        subscription = sub;
                        await monitorNodes(session, subscription, Array.from(tags));
                        console.log("CTRL+C to stop");
                        await new Promise((resolve) => process.once("SIGINT", resolve));
                    });
                } else {
                    // Unsubscribe all, then subscribe to new set
                    try {
                        await subscription.terminate();
                    } catch (e) {}
                    subscription = await session.createSubscription2(subscriptionParameters);
                    await monitorNodes(session, subscription, Array.from(tags));
                }
            }
            async function pollTagsAndUpdate() {
                try {
                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const data = await response.json();
                    const tags = Array.isArray(data.data) ? data.data : [];
                    const newTags = new Set(tags);
                    // If first time, or tags changed, update subscriptions
                    const tagsChanged = isFirst || tags.length !== currentTags.size || tags.some(tag => !currentTags.has(tag));
                    if (tagsChanged) {
                        console.log('Tags changed, updating subscriptions.');
                        currentTags = newTags;
                        await subscribeToTags(currentTags);
                        isFirst = false;
                    }
                } catch (err) {
                    console.error('Failed to fetch or monitor OPC tags:', err.message);
                }
            }
            // Initial poll
            await pollTagsAndUpdate();
            // Poll every 10 seconds
            setInterval(pollTagsAndUpdate, 10000);
        })();
        return;
    }
    console.log('Unknown mode. Use mode1, mode2, mode3, or mode4.');
})();




client.on("connect", () => {

    /*client.subscribe("EnergySCADA/#", (err) => {
        if (!err) {
            // client.publish("ph2presence", "Hello mqtt");
        }
    });*/

});


function publishtoMQTT(nodeID, topicmsg) {

    let msgObject = { "time": topicmsg.timestamp, [topicmsg.nodeID]: topicmsg.value };
    let topicName = String(nodeID).replaceAll(/\./gi, "/");
    client.publish('AMC/Storm/amcstorm/' + topicName, JSON.stringify(msgObject));

    // publishtoDatabase(topicName, topicmsg);
}

/*
function publishtoDatabase(topicName, topicmsg) {
    // Convert topicName to tagName format
    const tagName = topicName.replace(/\//g, '.');

    // Create SQL query to insert data into historical_records table
    //  const insertTagNameQuery = `INSERT IGNORE INTO tagnames (tagName) VALUES (?)`;
    // connection.query(insertTagNameQuery, [tagName], (err, results) => {
    const insertTagNameQuery = "INSERT INTO `tagnames` (tagName) SELECT ? FROM tagnames WHERE NOT EXISTS(SELECT NULL FROM `tagnames` WHERE `tagName`=?)";

    connection.query(insertTagNameQuery, [tagName, tagName], (err, results) => {

        if (err) {
            console.error('Error inserting tagName into tagnames table:', err);
            return;
        }
    });

    const sql = `INSERT INTO historicalrecords (tagID, value, timeStamp)
    SELECT COALESCE(t.ID, (SELECT ID FROM tagnames WHERE tagName = ?)) AS tagID,
           ? AS value,
           FROM_UNIXTIME(?) AS timeStamp
    FROM (SELECT ID FROM tagnames WHERE tagName = ?) t`;

    // Execute the SQL query
    connection.query(sql, [tagName, topicmsg.value, topicmsg.timestamp, tagName], (err, results) => {
        if (err) {
            console.error('Error inserting data into database:', err);
            return;
        }
        // console.log('Data inserted into database successfully');
    });
}*/