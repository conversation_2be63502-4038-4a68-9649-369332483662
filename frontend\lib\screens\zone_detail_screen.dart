import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../models/zone.dart';
import '../providers/data_provider.dart';
import '../utils/app_theme.dart';

class ZoneDetailScreen extends StatefulWidget {
  final dynamic zoneId; // Can be int or String for special zones

  const ZoneDetailScreen({
    super.key,
    required this.zoneId,
  });

  @override
  State<ZoneDetailScreen> createState() => _ZoneDetailScreenState();
}

class _ZoneDetailScreenState extends State<ZoneDetailScreen> {
  @override
  void initState() {
    super.initState();
    // Background refresh when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<DataProvider>(context, listen: false).refreshAllBackground();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(85),
        child: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.primaryLinearGradient,
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryColor.withOpacity(0.3),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white, size: 26),
                    onPressed: () => context.go('/home'),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _getZoneName(),
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            letterSpacing: 0.5,
                            height: 1.1,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            const Icon(Icons.location_city, color: Colors.white70, size: 14),
                            const SizedBox(width: 4),
                            Consumer<DataProvider>(
                              builder: (context, dataProvider, child) {
                                List<LocationData> locationDataList;
                                
                                if (widget.zoneId == 'CSPS') {
                                  locationDataList = dataProvider.locationData
                                      .where((locationData) => locationData.locationType.toString() == 'CSPS')
                                      .toList();
                                } else if (widget.zoneId == 'TSPS') {
                                  locationDataList = dataProvider.locationData
                                      .where((locationData) => locationData.locationType.toString() == 'TSPS')
                                      .toList();
                                } else if (widget.zoneId == 'SWPS') {
                                  locationDataList = dataProvider.locationData
                                      .where((locationData) => locationData.locationType.toString() == 'SWPS')
                                      .toList();
                                } else {
                                  locationDataList = dataProvider.locationData
                                      .where((locationData) => locationData.zoneId == widget.zoneId)
                                      .toList();
                                }
                                
                                return Text(
                                  '${locationDataList.length} Locations',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.white70,
                                    fontWeight: FontWeight.w500,
                                    height: 1.0,
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Consumer<DataProvider>(
                    builder: (context, dataProvider, child) {
                      return Container(
                        margin: const EdgeInsets.only(right: 12),
                        child: IconButton(
                          icon: dataProvider.isBackgroundRefreshing
                              ? const SizedBox(
                                  width: 22,
                                  height: 22,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2.5,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Icon(Icons.refresh, color: Colors.white, size: 26),
                          onPressed: dataProvider.isBackgroundRefreshing
                              ? null
                              : () {
                                  dataProvider.refreshAllBackground();
                                },
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      backgroundColor: AppTheme.backgroundColor,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Consumer<DataProvider>(
          builder: (context, dataProvider, child) {
            // Show loading only on initial load, not during background refresh
            if (dataProvider.isLoading && dataProvider.locations.isEmpty) {
              return const Center(
                child: Padding(
                  padding: EdgeInsets.all(50),
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                  ),
                ),
              );
            }

            if (dataProvider.errorMessage != null && dataProvider.locations.isEmpty) {
              return _buildErrorWidget(dataProvider.errorMessage!, dataProvider);
            }

            // Get real location data for the zone from backend using zone ID
            List<LocationData> locationDataList;
            
            if (widget.zoneId == 'CSPS') {
              // Filter by location type for Critical Pumping Station
              locationDataList = dataProvider.locationData
                  .where((locationData) => locationData.locationType.toString() == 'CSPS')
                  .toList();
            } else if (widget.zoneId == 'TSPS') {
              // Filter by location type for Terminal SPS
              locationDataList = dataProvider.locationData
                  .where((locationData) => locationData.locationType.toString() == 'TSPS')
                  .toList();
            } else if (widget.zoneId == 'SWPS') {
              // Filter by location type for Storm Water Pumping Station
              locationDataList = dataProvider.locationData
                  .where((locationData) => locationData.locationType.toString() == 'SWPS')
                  .toList();
            } else {
              // Regular zone filtering by zone ID
              locationDataList = dataProvider.locationData
                  .where((locationData) => locationData.zoneId == widget.zoneId)
                  .toList();
            }

            if (locationDataList.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.location_off,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No Locations Found',
                      style: AppTheme.headingMedium.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'No locations are available in this zone',
                      style: AppTheme.bodyMedium.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              );
            }

            return Column(
              children: locationDataList.map((locationData) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: _buildLocationCard(context, locationData),
              )).toList(),
            );
          },
        ),
      ),
    );
  }

  Widget _buildErrorWidget(String errorMessage, DataProvider dataProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              color: AppTheme.errorColor,
              size: 48,
            ),
            const SizedBox(height: 8),
            const Text(
              'Error loading data',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              errorMessage,
              style: const TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => dataProvider.refreshAll(),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationCard(BuildContext context, LocationData locationData) {
    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.white,
        boxShadow: AppTheme.cardShadow,
      ),
      child: InkWell(
        onTap: () => context.go('/location/${locationData.locationId}?zoneId=${widget.zoneId}'),
        borderRadius: BorderRadius.circular(16),
        child: Column(
          children: [
            // Header with gradient
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.accentColor.withOpacity(0.8),
                    AppTheme.accentColor.withOpacity(0.6),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.location_on,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      locationData.locationName,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        letterSpacing: 0.3,
                      ),
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      // Location Type Badge
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        margin: const EdgeInsets.only(bottom: 4),
                        decoration: BoxDecoration(
                          color: _getLocationTypeColor(locationData.locationType),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          locationData.locationType.code,
                          style: const TextStyle(
                            fontSize: 10,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ),
                      // Last Updated Time
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          locationData.formattedTimestamp,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Data metrics
                  Row(
                    children: [
                      Expanded(
                        child: _buildMetricTile(
                          'Current Day QTY',
                          locationData.formattedCurrentDayQty,
                          Icons.today,
                          AppTheme.primaryColor,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildMetricTile(
                          'Level',
                          locationData.formattedLevel,
                          Icons.water_drop,
                          AppTheme.infoColor,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Pump Status Section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.surfaceColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.settings,
                              color: AppTheme.primaryColor,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Pump Status',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: AppTheme.primaryColor,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        locationData.pumpStatuses.isEmpty
                            ? Text(
                                'No pumps configured',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                  fontStyle: FontStyle.italic,
                                ),
                              )
                            : Wrap(
                                spacing: 8,
                                runSpacing: 8,
                                children: () {
                                  final sortedPumps = List<PumpStatus>.from(locationData.pumpStatuses);
                                  sortedPumps.sort(PumpStatus.compare);
                                  return sortedPumps.map((pumpStatus) => Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: pumpStatus.isActive
                                            ? AppTheme.successGradient
                                            : AppTheme.errorGradient,
                                      ),
                                      borderRadius: BorderRadius.circular(20),
                                      boxShadow: [
                                        BoxShadow(
                                          color: (pumpStatus.isActive
                                                  ? AppTheme.successColor
                                                  : AppTheme.errorColor)
                                              .withOpacity(0.3),
                                          blurRadius: 4,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          pumpStatus.isActive ? Icons.play_circle : Icons.stop_circle,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 6),
                                        Text(
                                          pumpStatus.displayName,
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  )).toList();
                                }(),
                              ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricTile(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.secondaryTextColor,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getZoneName() {
    // Handle special zones first
    if (widget.zoneId == 'CSPS') {
      return 'Critical Pumping Station';
    } else if (widget.zoneId == 'TSPS') {
      return 'Terminal SPS';
    } else if (widget.zoneId == 'SWPS') {
      return 'Storm Water Pumping Station';
    }

    // Try to get zone name from data provider first
    final zone = Provider.of<DataProvider>(context, listen: false).getZoneById(widget.zoneId);
    if (zone != null) {
      return zone.name;
    }

    // Try to get zone name from zone data
    final zoneData = Provider.of<DataProvider>(context, listen: false).getZoneDataById(widget.zoneId);
    if (zoneData != null) {
      return zoneData.zoneName;
    }

    // Last fallback
    return 'Zone ${widget.zoneId}';
  }

  Color _getLocationTypeColor(LocationType locationType) {
    switch (locationType) {
      case LocationType.csps:
        return Colors.orange.shade600; // Combined Sewer Pumping Station
      case LocationType.swps:
        return Colors.blue.shade600;   // Storm Water Pumping Station
      case LocationType.tsps:
        return Colors.green.shade600;  // Treatment/Transfer Sewage Pumping Station
    }
  }


}
