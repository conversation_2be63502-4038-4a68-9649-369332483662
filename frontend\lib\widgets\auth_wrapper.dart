import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/auth_provider.dart';
import '../providers/app_provider.dart';
import '../providers/data_provider.dart';
import '../screens/splash_screen.dart';
import '../screens/login_screen.dart';

class AuthWrapper extends StatefulWidget {
  final Widget child;

  const AuthWrapper({super.key, required this.child});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _hasInitializedApp = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, _) {
        // Show splash screen while initializing
        if (!authProvider.isInitialized) {
          return const SplashScreen();
        }

        // Show login screen if not authenticated
        if (!authProvider.isAuthenticated) {
          _hasInitializedApp = false; // Reset flag when logged out
          // Stop auto-refresh when user logs out
          WidgetsBinding.instance.addPostFrameCallback((_) {
            final dataProvider = Provider.of<DataProvider>(context, listen: false);
            dataProvider.stopAutoRefresh();
          });
          return const LoginScreen();
        }

        // Initialize app data once when authenticated
        if (!_hasInitializedApp) {
          _initializeAppData();
          _hasInitializedApp = true;
        }

        // Show the main app if authenticated
        return widget.child;
      },
    );
  }

  void _initializeAppData() {
    // Initialize app data in the background
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        final dataProvider = Provider.of<DataProvider>(context, listen: false);

        // Start auto-refresh now that user is authenticated
        dataProvider.startAutoRefresh();

        // Load initial data
        await dataProvider.refreshAll();

        print('App data initialized successfully');
      } catch (e) {
        print('Error initializing app data: $e');
      }
    });
  }
}
