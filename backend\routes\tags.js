import express from 'express';
import { executeQuery } from '../config/database.js';

const router = express.Router();

// Static authorization token
const STATIC_AUTH_TOKEN = 'amc_monsoon_2024';

// Middleware to check authorization
const checkAuth = (req, res, next) => {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || authHeader !== `Bearer ${STATIC_AUTH_TOKEN}`) {
        return res.status(401).json({ error: 'Unauthorized access' });
    }
    
    next();
};

// Get OPC addresses from tags (must be before /:id route)
router.get('/opc-addresses', async (req, res) => {
    try {
        const query = `
            SELECT opc_address
            FROM tags
            WHERE is_active = TRUE
            ORDER BY opc_address
        `;
        
        const tags = await executeQuery(query);
        
        // Format the response to return only opc_addresses
        const opcAddresses = tags.map(tag => tag.opc_address);

        res.json({
            success: true,
            data: opcAddresses
        });
    } catch (error) {
        console.error('Error fetching OPC addresses:', error);
        res.status(500).json({ 
            success: false,
            error: 'Failed to fetch OPC addresses' 
        });
    }
});

// Get all tags
router.get('/', async (req, res) => {
  try {
    const query = `
      SELECT t.*, l.name as location_name, z.name as zone_name,
             td.value as latest_value,
             td.timestamp as latest_timestamp
      FROM tags t
      JOIN locations l ON t.location_id = l.id
      JOIN zones z ON l.zone_id = z.id
      LEFT JOIN (
        SELECT tag_id, value, timestamp,
               ROW_NUMBER() OVER (PARTITION BY tag_id ORDER BY timestamp DESC) as rn
        FROM tag_data
      ) td ON t.id = td.tag_id AND td.rn = 1
      ORDER BY z.name, l.name, t.tag_type, t.tag_name
    `; // WHERE t.is_active = TRUE
    const tags = await executeQuery(query);
    res.json(tags);
  } catch (error) {
    console.error('Error fetching tags:', error);
    res.status(500).json({ error: 'Failed to fetch tags' });
  }
});

// Get tags by location
router.get('/location/:locationId', async (req, res) => {
  try {
    const locationId = req.params.locationId;
    const query = `
      SELECT t.*,
             td.value as latest_value,
             td.timestamp as latest_timestamp
      FROM tags t
      LEFT JOIN (
        SELECT tag_id, value, timestamp,
               ROW_NUMBER() OVER (PARTITION BY tag_id ORDER BY timestamp DESC) as rn
        FROM tag_data
      ) td ON t.id = td.tag_id AND td.rn = 1
      WHERE t.location_id = ? AND t.is_active = TRUE
      ORDER BY t.tag_type, t.tag_name
    `;
    const tags = await executeQuery(query, [locationId]);
    res.json(tags);
  } catch (error) {
    console.error('Error fetching tags by location:', error);
    res.status(500).json({ error: 'Failed to fetch tags' });
  }
});

// Get tag by ID with recent data
router.get('/:id', async (req, res) => {
  try {
    const tagId = req.params.id;

    // Get tag details
    const tagQuery = `
      SELECT t.*, l.name as location_name, z.name as zone_name
      FROM tags t
      JOIN locations l ON t.location_id = l.id
      JOIN zones z ON l.zone_id = z.id
      WHERE t.id = ?
    `;
    const tags = await executeQuery(tagQuery, [tagId]);

    if (tags.length === 0) {
      return res.status(404).json({ error: 'Tag not found' });
    }

    // Get recent data (last 24 hours)
    const dataQuery = `
      SELECT value, timestamp, received_at
      FROM tag_data
      WHERE tag_id = ? AND timestamp > ?
      ORDER BY timestamp DESC
      LIMIT 100
    `;
    const oneDayAgo = Math.floor(Date.now() / 1000) - (24 * 60 * 60);
    const recentData = await executeQuery(dataQuery, [tagId, oneDayAgo]);

    const tag = tags[0];
    tag.recent_data = recentData;

    res.json(tag);
  } catch (error) {
    console.error('Error fetching tag:', error);
    res.status(500).json({ error: 'Failed to fetch tag' });
  }
});

// Create new tag
router.post('/', async (req, res) => {
  try {
    const {
      location_id,
      opc_address,
      tag_name,
      tag_type,
      unit,
      min_value,
      max_value
    } = req.body;

    if (!location_id || !opc_address || !tag_name) {
      return res.status(400).json({
        error: 'Location ID, OPC address, and tag name are required'
      });
    }

    // Verify location exists
    const locationCheck = await executeQuery('SELECT id FROM locations WHERE id = ?', [location_id]);
    if (locationCheck.length === 0) {
      return res.status(400).json({ error: 'Invalid location ID' });
    }

    // Generate MQTT topic from OPC address
    const mqttTopic = generateMQTTTopic(opc_address);

    const query = `
      INSERT INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value,is_active)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?,?)
    `;
    const result = await executeQuery(query, [
      location_id,
      opc_address,
      mqttTopic,
      tag_name,
      tag_type || 'OTHER',
      unit || null,
      min_value || null,
      max_value || null,
    1
    ]);

    res.status(201).json({
      id: result.insertId,
      location_id,
      opc_address,
      mqtt_topic: mqttTopic,
      tag_name,
      tag_type: tag_type || 'OTHER',
      unit,
      min_value,
      max_value,
      message: 'Tag created successfully'
    });
  } catch (error) {
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(400).json({ error: 'OPC address already exists' });
    }
    console.error('Error creating tag:', error);
    res.status(500).json({ error: 'Failed to create tag' });
  }
});

// Update tag
router.put('/:id', async (req, res) => {
  try {
    const tagId = req.params.id;
    const {
      location_id,
      opc_address,
      mqtt_topic,
      tag_name,
      tag_type,
      unit,
      min_value,
      max_value,
      is_active
    } = req.body;

    if (!location_id || !opc_address || !tag_name) {
      return res.status(400).json({
        error: 'Location ID, OPC address, and tag name are required'
      });
    }

    // Verify location exists
    const locationCheck = await executeQuery('SELECT id FROM locations WHERE id = ?', [location_id]);
    if (locationCheck.length === 0) {
      return res.status(400).json({ error: 'Invalid location ID' });
    }

    // Use provided MQTT topic or generate from OPC address
    const finalMqttTopic = mqtt_topic || generateMQTTTopic(opc_address);

    const query = `
      UPDATE tags SET
        location_id = ?, opc_address = ?, mqtt_topic = ?, tag_name = ?,
        tag_type = ?, unit = ?, min_value = ?, max_value = ?, is_active = ?
      WHERE id = ?
    `;
    const result = await executeQuery(query, [
      location_id,
      opc_address,
      finalMqttTopic,
      tag_name,
      tag_type || 'OTHER',
      unit || null,
      min_value || null,
      max_value || null,
      is_active !== undefined ? is_active : true,
      tagId
    ]);

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Tag not found' });
    }

    res.json({ message: 'Tag updated successfully' });
  } catch (error) {
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(400).json({ error: 'OPC address already exists' });
    }
    console.error('Error updating tag:', error);
    res.status(500).json({ error: 'Failed to update tag' });
  }
});

// Disable (soft delete) tag
router.put('/:id/disable', async (req, res) => {
  try {
    const tagId = req.params.id;
    const query = 'UPDATE tags SET is_active = FALSE WHERE id = ?';
    const result = await executeQuery(query, [tagId]);
    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Tag not found' });
    }
    res.json({ message: 'Tag disabled successfully' });
  } catch (error) {
    console.error('Error disabling tag:', error);
    res.status(500).json({ error: 'Failed to disable tag' });
  }
});

// Enable (reactivate) tag
router.put('/:id/enable', async (req, res) => {
  try {
    const tagId = req.params.id;
    const query = 'UPDATE tags SET is_active = TRUE WHERE id = ?';
    const result = await executeQuery(query, [tagId]);
    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Tag not found' });
    }
    res.json({ message: 'Tag enabled successfully' });
  } catch (error) {
    console.error('Error enabling tag:', error);
    res.status(500).json({ error: 'Failed to enable tag' });
  }
});

// Delete tag (hard delete)
router.delete('/:id', async (req, res) => {
  try {
    const tagId = req.params.id;
    const query = 'DELETE FROM tags WHERE id = ?';
    const result = await executeQuery(query, [tagId]);
    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Tag not found' });
    }
    res.json({ message: 'Tag deleted successfully' });
  } catch (error) {
    console.error('Error deleting tag:', error);
    res.status(500).json({ error: 'Failed to delete tag' });
  }
});

// Helper function to generate MQTT topic from OPC address
function generateMQTTTopic(opcAddress) {
  // Convert ns=2;s=AKHBAR NAGAR.5 PUMP.LEVEL_1
  // to AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/LEVEL_1
  const match = opcAddress.match(/ns=\d+;s=(.+)/);
  if (match) {
    const parts = match[1].split('.');
    if (parts.length >= 3) {
      const location = parts[0];
      const pumpInfo = parts[1];
      const tagName = parts.slice(2).join('.');
      return `AMC/Storm/amcstorm/${location}/${pumpInfo}/${tagName}`;
    }
  }
  return `AMC/Storm/amcstorm/UNKNOWN/UNKNOWN/UNKNOWN`;
}

export default router;
