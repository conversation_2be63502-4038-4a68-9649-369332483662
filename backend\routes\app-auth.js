import express from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import db from '../config/database.js';

const router = express.Router();

// JWT secret key (should be in environment variables in production)
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d'; // 7 days

// Helper function to generate session token
const generateSessionToken = () => {
  return crypto.randomBytes(32).toString('hex');
};

// Helper function to hash session token
const hashToken = (token) => {
  return crypto.createHash('sha256').update(token).digest('hex');
};

// Login endpoint for mobile app
router.post('/login', async (req, res) => {
  try {
    const { username, password, deviceInfo } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        error: 'Username and password are required'
      });
    }

    // Find user in database
    const [users] = await db.execute(
      'SELECT * FROM app_users WHERE username = ? AND is_active = TRUE',
      [username]
    );

    if (users.length === 0) {
      return res.status(401).json({
        success: false,
        error: 'Invalid username or password'
      });
    }

    const user = users[0];

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        error: 'Invalid username or password'
      });
    }

    // Generate JWT token
    const jwtToken = jwt.sign(
      {
        userId: user.id,
        username: user.username,
        role: user.role
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    // Generate session token
    const sessionToken = generateSessionToken();
    const sessionTokenHash = hashToken(sessionToken);

    // Calculate expiration date (7 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    // Store session in database
    const sessionParams = [
      user.id,
      sessionTokenHash,
      deviceInfo || 'Unknown Device',
      req.ip || req.headers['x-forwarded-for'] || req.socket?.remoteAddress || 'Unknown IP',
      expiresAt
    ];

    // Debug: Check for undefined parameters
    console.log('Session parameters:', sessionParams);

    await db.execute(
      `INSERT INTO user_sessions (user_id, token_hash, device_info, ip_address, expires_at)
       VALUES (?, ?, ?, ?, ?)`,
      sessionParams
    );

    // Update last login
    await db.execute(
      'UPDATE app_users SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
      [user.id]
    );

    // Clean up expired sessions
    await db.execute(
      'DELETE FROM user_sessions WHERE expires_at < NOW()'
    );

    // Return success response
    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.full_name,
          role: user.role
        },
        token: jwtToken,
        sessionToken: sessionToken,
        expiresAt: expiresAt.toISOString()
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Logout endpoint
router.post('/logout', async (req, res) => {
  try {
    const { sessionToken } = req.body;

    if (sessionToken) {
      const sessionTokenHash = hashToken(sessionToken);
      
      // Remove session from database
      await db.execute(
        'DELETE FROM user_sessions WHERE token_hash = ?',
        [sessionTokenHash]
      );
    }

    res.json({
      success: true,
      message: 'Logout successful'
    });

  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Verify token endpoint
router.post('/verify', async (req, res) => {
  try {
    const { token, sessionToken } = req.body;

    if (!token || !sessionToken) {
      return res.status(400).json({
        success: false,
        error: 'Token and session token are required'
      });
    }

    // Verify JWT token
    let decoded;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired token'
      });
    }

    // Verify session token
    const sessionTokenHash = hashToken(sessionToken);
    const [sessions] = await db.execute(
      `SELECT s.*, u.username, u.email, u.full_name, u.role, u.is_active 
       FROM user_sessions s 
       JOIN app_users u ON s.user_id = u.id 
       WHERE s.token_hash = ? AND s.expires_at > NOW() AND u.is_active = TRUE`,
      [sessionTokenHash]
    );

    if (sessions.length === 0) {
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired session'
      });
    }

    const session = sessions[0];

    // Verify user ID matches
    if (session.user_id !== decoded.userId) {
      return res.status(401).json({
        success: false,
        error: 'Token mismatch'
      });
    }

    res.json({
      success: true,
      message: 'Token is valid',
      data: {
        user: {
          id: session.user_id,
          username: session.username,
          email: session.email,
          fullName: session.full_name,
          role: session.role
        }
      }
    });

  } catch (error) {
    console.error('Token verification error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Refresh token endpoint
router.post('/refresh', async (req, res) => {
  try {
    const { sessionToken } = req.body;

    if (!sessionToken) {
      return res.status(400).json({
        success: false,
        error: 'Session token is required'
      });
    }

    // Verify session token
    const sessionTokenHash = hashToken(sessionToken);
    const [sessions] = await db.execute(
      `SELECT s.*, u.username, u.role, u.is_active 
       FROM user_sessions s 
       JOIN app_users u ON s.user_id = u.id 
       WHERE s.token_hash = ? AND s.expires_at > NOW() AND u.is_active = TRUE`,
      [sessionTokenHash]
    );

    if (sessions.length === 0) {
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired session'
      });
    }

    const session = sessions[0];

    // Generate new JWT token
    const newJwtToken = jwt.sign(
      {
        userId: session.user_id,
        username: session.username,
        role: session.role
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    // Extend session expiration
    const newExpiresAt = new Date();
    newExpiresAt.setDate(newExpiresAt.getDate() + 7);

    await db.execute(
      'UPDATE user_sessions SET expires_at = ? WHERE token_hash = ?',
      [newExpiresAt, sessionTokenHash]
    );

    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        token: newJwtToken,
        expiresAt: newExpiresAt.toISOString()
      }
    });

  } catch (error) {
    console.error('Token refresh error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;
