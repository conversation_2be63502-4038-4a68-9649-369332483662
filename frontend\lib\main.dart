import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import 'providers/app_provider.dart';
import 'providers/auth_provider.dart';
import 'providers/data_provider.dart';
import 'screens/splash_screen.dart';
import 'screens/home_screen.dart';
import 'screens/login_screen.dart';
import 'widgets/auth_wrapper.dart';
import 'screens/zones_screen.dart';
import 'screens/zone_detail_screen.dart';
import 'screens/location_detail_screen.dart';
import 'screens/tag_detail_screen.dart';
import 'screens/settings_screen.dart';
import 'utils/app_theme.dart';

void main() {
  runApp(const StormApp());
}

class StormApp extends StatelessWidget {
  const StormApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AppProvider()),
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProxyProvider2<AppProvider, AuthProvider, DataProvider>(
          create: (context) {
            final appProvider = Provider.of<AppProvider>(context, listen: false);
            final authProvider = Provider.of<AuthProvider>(context, listen: false);
            final dataProvider = DataProvider(appProvider);
            dataProvider.setAuthProvider(authProvider);
            return dataProvider;
          },
          update: (context, appProvider, authProvider, previous) {
            if (previous != null) {
              previous.setAuthProvider(authProvider);
              return previous;
            } else {
              final dataProvider = DataProvider(appProvider);
              dataProvider.setAuthProvider(authProvider);
              return dataProvider;
            }
          },
        ),

      ],
      child: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          return MaterialApp.router(
            title: 'StormApp',
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: appProvider.themeMode,
            routerConfig: _router,
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}

// Router configuration
final GoRouter _router = GoRouter(
  initialLocation: '/home',
  routes: [
    GoRoute(
      path: '/home',
      builder: (context, state) => const AuthWrapper(child: HomeScreen()),
    ),
    GoRoute(
      path: '/zones',
      builder: (context, state) => const AuthWrapper(child: ZonesScreen()),
    ),
    GoRoute(
      path: '/zone/:id',
      builder: (context, state) {
        final zoneIdString = state.pathParameters['id']!;
        // Try to parse as int, if it fails keep as string
        dynamic zoneId;
        try {
          zoneId = int.parse(zoneIdString);
        } catch (e) {
          zoneId = zoneIdString; // Keep as string for special zones like 'CSPS', 'TSPS'
        }
        return AuthWrapper(child: ZoneDetailScreen(zoneId: zoneId));
      },
    ),
    GoRoute(
      path: '/location/:id',
      builder: (context, state) {
        final locationId = int.parse(state.pathParameters['id']!);
        final zoneIdString = state.uri.queryParameters['zoneId'];
        // Handle both string and integer zone IDs
        dynamic zoneId;
        if (zoneIdString != null) {
          try {
            zoneId = int.parse(zoneIdString);
          } catch (e) {
            zoneId = zoneIdString; // Keep as string for special zones like 'CSPS', 'TSPS', 'SWPS'
          }
        }
        return AuthWrapper(child: LocationDetailScreen(
          locationId: locationId,
          zoneId: zoneId,
        ));
      },
    ),
    GoRoute(
      path: '/tag/:id',
      builder: (context, state) {
        final tagId = int.parse(state.pathParameters['id']!);
        return AuthWrapper(child: TagDetailScreen(tagId: tagId));
      },
    ),
    GoRoute(
      path: '/settings',
      builder: (context, state) => const AuthWrapper(child: SettingsScreen()),
    ),
  ],
);
