import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';

import '../models/zone.dart';
import '../config/app_config.dart';
import 'app_provider.dart';
import 'auth_provider.dart';

class DataProvider extends ChangeNotifier {
  final AppProvider _appProvider;
  AuthProvider? _authProvider;

  List<Zone> _zones = [];
  List<Location> _locations = [];
  List<Tag> _tags = [];
  Map<int, List<TagData>> _tagHistory = {};
  List<ZoneData> _zoneData = [];
  List<LocationData> _locationData = [];
  Map<String, dynamic>? _zoneDataResponse;

  bool _isLoading = false;
  bool _isBackgroundRefreshing = false;
  String? _errorMessage;
  DateTime? _lastUpdate;
  Timer? _refreshTimer;

  // Getters
  List<Zone> get zones => _zones;
  List<Location> get locations => _locations;
  List<Tag> get tags => _tags;
  Map<int, List<TagData>> get tagHistory => _tagHistory;
  List<ZoneData> get zoneData => _zoneData;
  List<LocationData> get locationData => _locationData;
  Map<String, dynamic>? get zoneDataResponse => _zoneDataResponse;
  bool get isLoading => _isLoading;
  bool get isBackgroundRefreshing => _isBackgroundRefreshing;
  String? get errorMessage => _errorMessage;
  DateTime? get lastUpdate => _lastUpdate;

  DataProvider(this._appProvider) {
    // Don't start auto-refresh immediately, wait for authentication
  }

  // Set auth provider (called after initialization)
  void setAuthProvider(AuthProvider authProvider) {
    _authProvider = authProvider;
    // Start auto-refresh only if user is authenticated
    if (authProvider.isAuthenticated) {
      _startAutoRefresh();
    }
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  // Auto refresh management
  void _startAutoRefresh() {
    _refreshTimer?.cancel();
    if (_appProvider.autoRefresh) {
      _refreshTimer = Timer.periodic(
        Duration(seconds: _appProvider.refreshInterval),
        (_) => refreshAllBackground(),
      );
    }
  }

  void updateAutoRefresh() {
    _startAutoRefresh();
  }

  // Start auto-refresh when user is authenticated
  void startAutoRefresh() {
    if (_authProvider != null && _authProvider!.isAuthenticated) {
      _startAutoRefresh();
    }
  }

  // Stop auto-refresh when user logs out
  void stopAutoRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  // Loading state management
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Background refresh state management
  void _setBackgroundRefreshing(bool refreshing) {
    _isBackgroundRefreshing = refreshing;
    notifyListeners();
  }

  // Error management
  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  void clearError() {
    _setError(null);
  }

  // HTTP helper
  Future<http.Response> _makeRequest(String endpoint) async {
    // Use app API endpoints for authenticated requests
    final baseUrl = _authProvider != null
        ? '${_appProvider.serverUrl}/api/app'
        : _appProvider.apiUrl;
    final url = '$baseUrl$endpoint';

    try {
      // Get authentication headers if available
      final headers = _authProvider?.getAuthHeaders() ??
          {'Content-Type': 'application/json'};

      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      ).timeout(Duration(seconds: AppConfig.httpTimeout));

      if (response.statusCode == 200) {
        _lastUpdate = DateTime.now();
        _setError(null);
      } else if (response.statusCode == 401) {
        // Handle authentication error
        _setError('Authentication required. Please login again.');
        if (_authProvider != null) {
          await _authProvider!.logout();
        }
      }

      return response;
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  // Fetch zones
  Future<void> fetchZones() async {
    try {
      _setLoading(true);
      final response = await _makeRequest('/zones');

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        _zones = data.map((json) => Zone.fromJson(json)).toList();
      } else {
        throw Exception('Failed to fetch zones: ${response.statusCode}');
      }
    } catch (e) {
      _setError('Failed to fetch zones: $e');
    } finally {
      _setLoading(false);
      notifyListeners();
    }
  }

  // Fetch locations
  Future<void> fetchLocations() async {
    try {
      _setLoading(true);
      final response = await _makeRequest('/locations');

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        _locations = data.map((json) => Location.fromJson(json)).toList();
      } else {
        throw Exception('Failed to fetch locations: ${response.statusCode}');
      }
    } catch (e) {
      _setError('Failed to fetch locations: $e');
    } finally {
      _setLoading(false);
      notifyListeners();
    }
  }

  // Fetch locations by zone
  Future<List<Location>> fetchLocationsByZone(int zoneId) async {
    try {
      final response = await _makeRequest('/locations/zone/$zoneId');

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => Location.fromJson(json)).toList();
      } else {
        throw Exception('Failed to fetch locations: ${response.statusCode}');
      }
    } catch (e) {
      _setError('Failed to fetch locations: $e');
      return [];
    }
  }

  // Fetch locations by type
  Future<List<Location>> fetchLocationsByType(String locationType) async {
    try {
      final response = await _makeRequest('/locations/type/$locationType');

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => Location.fromJson(json)).toList();
      } else {
        throw Exception('Failed to fetch locations: ${response.statusCode}');
      }
    } catch (e) {
      _setError('Failed to fetch locations: $e');
      return [];
    }
  }

  // Fetch location data by type
  Future<List<LocationData>> fetchLocationDataByType(String locationType) async {
    try {
      final response = await _makeRequest('/locations/data/type/$locationType');

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => LocationData.fromJson(json)).toList();
      } else {
        throw Exception('Failed to fetch location data: ${response.statusCode}');
      }
    } catch (e) {
      _setError('Failed to fetch location data: $e');
      return [];
    }
  }

  // Fetch tags
  Future<void> fetchTags() async {
    try {
      _setLoading(true);
      final response = await _makeRequest('/tags');

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        _tags = data.map((json) => Tag.fromJson(json)).toList();
      } else {
        throw Exception('Failed to fetch tags: ${response.statusCode}');
      }
    } catch (e) {
      _setError('Failed to fetch tags: $e');
    } finally {
      _setLoading(false);
      notifyListeners();
    }
  }

  // Fetch tags by location
  Future<List<Tag>> fetchTagsByLocation(int locationId) async {
    try {
      final response = await _makeRequest('/tags/location/$locationId');

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => Tag.fromJson(json)).toList();
      } else {
        throw Exception('Failed to fetch tags: ${response.statusCode}');
      }
    } catch (e) {
      _setError('Failed to fetch tags: $e');
      return [];
    }
  }

  // Fetch tag details
  Future<Tag?> fetchTagDetails(int tagId) async {
    try {
      final response = await _makeRequest('/tags/$tagId');

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        return Tag.fromJson(data);
      } else {
        throw Exception('Failed to fetch tag details: ${response.statusCode}');
      }
    } catch (e) {
      _setError('Failed to fetch tag details: $e');
      return null;
    }
  }

  // Fetch tag history
  Future<List<TagData>> fetchTagHistory(int tagId, {int hours = 24}) async {
    try {
      final response = await _makeRequest('/data/history/$tagId?hours=$hours');

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final history = data.map((json) => TagData.fromJson(json)).toList();
        _tagHistory[tagId] = history;
        notifyListeners();
        return history;
      } else {
        throw Exception('Failed to fetch tag history: ${response.statusCode}');
      }
    } catch (e) {
      _setError('Failed to fetch tag history: $e');
      return [];
    }
  }

  // Fetch latest data
  Future<void> fetchLatestData() async {
    try {
      final response = await _makeRequest('/data/latest');

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        // Update tags with latest values
        for (final item in data) {
          final tagId = item['id'];
          final tagIndex = _tags.indexWhere((tag) => tag.id == tagId);
          if (tagIndex != -1) {
            // Safely parse the value
            final value = _parseToDouble(item['value']);
            final timestamp = item['timestamp'];

            // Create updated tag with new values
            final updatedTag = Tag.fromJson({
              ..._tags[tagIndex].toJson(),
              'latest_value': value,
              'latest_timestamp': timestamp,
            });
            _tags[tagIndex] = updatedTag;
          }
        }
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to fetch latest data: $e');
    }
  }

  // Helper method to safely parse values to double
  double? _parseToDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  // Fetch dashboard data
  Future<Map<String, dynamic>?> fetchDashboardData() async {
    try {
      final response = await _makeRequest('/data/dashboard');

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to fetch dashboard data: ${response.statusCode}');
      }
    } catch (e) {
      _setError('Failed to fetch dashboard data: $e');
      return null;
    }
  }

  // Refresh all data (with loading state)
  Future<void> refreshAll() async {
    await Future.wait([
      fetchZones(),
      fetchLocations(),
      fetchTags(),
      fetchLatestData(),
      fetchZoneData(),
      fetchLocationData(),
    ]);
  }

  // Background refresh all data (without loading state)
  Future<void> refreshAllBackground() async {
    // Only refresh if user is authenticated
    if (_authProvider == null || !_authProvider!.isAuthenticated) {
      print('Skipping background refresh - user not authenticated');
      return;
    }

    _setBackgroundRefreshing(true);
    try {
      await Future.wait([
        fetchZonesBackground(),
        fetchLocationsBackground(),
        fetchTagsBackground(),
        fetchLatestDataBackground(),
        fetchZoneDataBackground(),
        fetchLocationDataBackground(),
      ]);
    } catch (e) {
      // Silently handle background refresh errors
      print('Background refresh error: $e');
    } finally {
      _setBackgroundRefreshing(false);
    }
  }

  // Fetch zone aggregated data
  Future<void> fetchZoneData() async {
    try {
      final response = await _makeRequest('/zones/data');
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        if (data.containsKey('zones')) {
          _zoneDataResponse = data;
          _zoneData = (data['zones'] as List)
              .map((json) => ZoneData.fromJson(json))
              .toList();
          _lastUpdate = DateTime.now();
          notifyListeners();
        } else {
          throw Exception('Invalid zone data response format');
        }
      } else {
        throw Exception('Failed to fetch zone data: ${response.statusCode}');
      }
    } catch (e) {
      _setError('Failed to fetch zone data: $e');
    }
  }

  // Fetch location detailed data
  Future<void> fetchLocationData() async {
    try {
      final response = await _makeRequest('/locations/data');
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        _locationData = data.map((json) => LocationData.fromJson({
          ...json,
          'operator_name': json['operator_name'] ?? '',
          'operator_number': json['operator_number'] ?? '',
          'supervisor_number': json['supervisor_number'] ?? '',
          'torrent_service_1': json['torrent_service_1'] ?? '',
          'supervisor_name': json['supervisor_name'] ?? '',
          'torrent_contact': json['torrent_contact'] ?? '',
        })).toList();
        _updateLocationTimestamps();
        _lastUpdate = DateTime.now();
        notifyListeners();
      } else {
        throw Exception('Failed to fetch location data: ${response.statusCode}');
      }
    } catch (e) {
      _setError('Failed to fetch location data: $e');
    }
  }

  // Background fetch zone data
  Future<void> fetchZoneDataBackground() async {
    try {
      final response = await _makeRequest('/zones/data');
      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        if (data.containsKey('zones')) {
          _zoneDataResponse = data;
          _zoneData = (data['zones'] as List)
              .map((json) => ZoneData.fromJson(json))
              .toList();
          notifyListeners();
        }
      }
    } catch (e) {
      print('Background zone data fetch error: $e');
    }
  }

  // Background fetch location data
  Future<void> fetchLocationDataBackground() async {
    try {
      final response = await _makeRequest('/locations/data');
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        _locationData = data.map((json) => LocationData.fromJson({
          ...json,
          'operator_name': json['operator_name'] ?? '',
          'operator_number': json['operator_number'] ?? '',
          'supervisor_number': json['supervisor_number'] ?? '',
          'torrent_service_1': json['torrent_service_1'] ?? '',
          'supervisor_name': json['supervisor_name'] ?? '',
          'torrent_contact': json['torrent_contact'] ?? '',
        })).toList();
        _updateLocationTimestamps();
        notifyListeners();
      }
    } catch (e) {
      print('Background location data fetch error: $e');
    }
  }

  // Update location timestamps based on most recent tag data
  void _updateLocationTimestamps() {
    for (int i = 0; i < _locationData.length; i++) {
      final locationData = _locationData[i];
      final locationTags = _tags.where((tag) => tag.locationId == locationData.locationId).toList();

      if (locationTags.isNotEmpty) {
        // Find the most recent timestamp among all tags for this location
        int? mostRecentTimestamp;
        for (final tag in locationTags) {
          if (tag.latestTimestamp != null) {
            if (mostRecentTimestamp == null || tag.latestTimestamp! > mostRecentTimestamp) {
              mostRecentTimestamp = tag.latestTimestamp;
            }
          }
        }

        if (mostRecentTimestamp != null) {
          // Update the location data with the most recent timestamp
          final updatedLocationData = LocationData(
            locationId: locationData.locationId,
            locationName: locationData.locationName,
            locationType: locationData.locationType,
            zoneId: locationData.zoneId,
            zoneName: locationData.zoneName,
            currentDayQty: locationData.currentDayQty,
            previousDayQty: locationData.previousDayQty,
            tillTodayQty: locationData.tillTodayQty,
            level: locationData.level,
            flowRate: locationData.flowRate,
            pumpStatuses: locationData.pumpStatuses,
            lastUpdated: DateTime.fromMillisecondsSinceEpoch(mostRecentTimestamp * 1000),
            operatorName: locationData.operatorName,
            operatorNumber: locationData.operatorNumber,
            supervisorNumber: locationData.supervisorNumber,
            torrentService1: locationData.torrentService1,
            supervisorName: locationData.supervisorName,
            torrentContact: locationData.torrentContact,
          );
          _locationData[i] = updatedLocationData;
        }
      }
    }
  }

  // Background fetch methods (without loading states)
  Future<void> fetchZonesBackground() async {
    try {
      final response = await _makeRequest('/zones');
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        _zones = data.map((json) => Zone.fromJson(json)).toList();
        notifyListeners();
      }
    } catch (e) {
      // Silently handle background errors
      print('Background zones fetch error: $e');
    }
  }

  Future<void> fetchLocationsBackground() async {
    try {
      final response = await _makeRequest('/locations');
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        _locations = data.map((json) => Location.fromJson(json)).toList();
        notifyListeners();
      }
    } catch (e) {
      // Silently handle background errors
      print('Background locations fetch error: $e');
    }
  }

  Future<void> fetchTagsBackground() async {
    try {
      final response = await _makeRequest('/tags');
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        _tags = data.map((json) => Tag.fromJson(json)).toList();
        notifyListeners();
      }
    } catch (e) {
      // Silently handle background errors
      print('Background tags fetch error: $e');
    }
  }

  Future<void> fetchLatestDataBackground() async {
    try {
      final response = await _makeRequest('/data/latest');
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        // Update tags with latest values
        for (final item in data) {
          final tagId = item['id'];
          final tagIndex = _tags.indexWhere((tag) => tag.id == tagId);
          if (tagIndex != -1) {
            final value = _parseToDouble(item['value']);
            final timestamp = item['timestamp'];
            final updatedTag = Tag.fromJson({
              ..._tags[tagIndex].toJson(),
              'latest_value': value,
              'latest_timestamp': timestamp,
            });
            _tags[tagIndex] = updatedTag;
          }
        }
        notifyListeners();
      }
    } catch (e) {
      // Silently handle background errors
      print('Background latest data fetch error: $e');
    }
  }

  // Get locations for a specific zone
  List<Location> getLocationsByZone(int zoneId) {
    return _locations.where((location) => location.zoneId == zoneId).toList();
  }

  // Get tags for a specific location
  List<Tag> getTagsByLocation(int locationId) {
    return _tags.where((tag) => tag.locationId == locationId).toList();
  }

  // Get zone by ID
  Zone? getZoneById(dynamic zoneId) {
    try {
      return _zones.firstWhere((zone) => zone.id == zoneId);
    } catch (e) {
      return null;
    }
  }

  // Get location by ID
  Location? getLocationById(int locationId) {
    try {
      return _locations.firstWhere((location) => location.id == locationId);
    } catch (e) {
      return null;
    }
  }

  // Get tag by ID
  Tag? getTagById(int tagId) {
    try {
      return _tags.firstWhere((tag) => tag.id == tagId);
    } catch (e) {
      return null;
    }
  }

  // Get zone data by ID
  ZoneData? getZoneDataById(dynamic zoneId) {
    try {
      return _zoneData.firstWhere((zoneData) => zoneData.zoneId == zoneId);
    } catch (e) {
      return null;
    }
  }

  // Get location data by ID
  LocationData? getLocationDataById(int locationId) {
    try {
      return _locationData.firstWhere((locationData) => locationData.locationId == locationId);
    } catch (e) {
      return null;
    }
  }

  // Get zone data by name
  ZoneData? getZoneDataByName(String zoneName) {
    try {
      return _zoneData.firstWhere((zoneData) => zoneData.zoneName == zoneName);
    } catch (e) {
      return null;
    }
  }

  // Get zone totals by type
  ZoneData? getZoneTotalsByType(String zoneType) {
    double totalCurrentDay = 0.0;
    double totalPreviousDay = 0.0;
    double totalTillToday = 0.0;
    DateTime? latestUpdate;
    int totalLocations = 0;

    // Filter zones by type
    final zonesOfType = _zoneData.where((zone) => zone.zoneType == zoneType).toList();
    if (zonesOfType.isEmpty) return null;

    for (final zoneData in zonesOfType) {
      totalCurrentDay += zoneData.totalCurrentDayQty;
      totalPreviousDay += zoneData.totalPreviousDayQty;
      totalTillToday += zoneData.totalTillTodayQty;
      totalLocations += zoneData.locationCount;

      if (latestUpdate == null || zoneData.lastUpdated.isAfter(latestUpdate)) {
        latestUpdate = zoneData.lastUpdated;
      }
    }

    return ZoneData(
      zoneId: 0,
      zoneName: '${zoneType.toUpperCase()} Total',
      zoneType: zoneType,
      totalCurrentDayQty: totalCurrentDay,
      totalPreviousDayQty: totalPreviousDay,
      totalTillTodayQty: totalTillToday,
      lastUpdated: latestUpdate ?? DateTime.now(),
      locationCount: totalLocations,
    );
  }

  // Calculate total zone data
  ZoneData calculateTotalZoneData() {
    double totalCurrentDay = 0.0;
    double totalPreviousDay = 0.0;
    double totalTillToday = 0.0;
    DateTime? latestUpdate;
    int totalLocations = 0;

    // Only include regular zones in the total
    for (final zoneData in _zoneData.where((zone) => zone.zoneType == 'regular')) {
      totalCurrentDay += zoneData.totalCurrentDayQty;
      totalPreviousDay += zoneData.totalPreviousDayQty;
      totalTillToday += zoneData.totalTillTodayQty;
      totalLocations += zoneData.locationCount;

      if (latestUpdate == null || zoneData.lastUpdated.isAfter(latestUpdate)) {
        latestUpdate = zoneData.lastUpdated;
      }
    }

    return ZoneData(
      zoneId: 0,
      zoneName: 'Total of All Zones',
      zoneType: 'regular',
      totalCurrentDayQty: totalCurrentDay,
      totalPreviousDayQty: totalPreviousDay,
      totalTillTodayQty: totalTillToday,
      lastUpdated: latestUpdate ?? DateTime.now(),
      locationCount: totalLocations,
    );
  }

  // Statistics
  int get totalZones => _zones.length;
  int get totalLocations => _locations.length;
  int get totalTags => _tags.length;
  int get activeTags => _tags.where((tag) => tag.hasValidValue).length;
  int get alertTags => _tags.where((tag) => !tag.isInRange).length;
}
