# iisnode configuration for ES6 modules and production deployment

# Node.js executable and arguments with ES6 module support
nodeProcessCommandLine: C:\Program Files\nodejs\node.exe --input-type=module

# Enhanced logging for debugging
loggingEnabled: true
logDirectory: iisnode
debuggingEnabled: true
debuggerExtensionDll: iisnode-inspector.dll
debuggerPathSegment: debug
debuggerPortRange: 5858-5858
maxLogFileSizeInKB: 10240
maxTotalLogFileSizeInKB: 102400
maxLogFiles: 50
devErrorsEnabled: true

# Log stdout and stderr
logFileFlushInterval: 5000
flushResponse: false
enableXFF: true
promoteServerVars: REMOTE_ADDR,HTTP_X_FORWARDED_FOR,HTTP_X_FORWARDED_PROTO,HTTP_HOST

# Environment variables for production
NODE_ENV: production

