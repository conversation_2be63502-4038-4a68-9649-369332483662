-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 19, 2025 at 09:24 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.0.30

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `storm_db`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin_users`
--

CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL,
  `username` varchar(100) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `admin_users`
--

INSERT INTO `admin_users` (`id`, `username`, `password_hash`, `email`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2a$10$zVFTa1BN0tJLydd9jXA8neinLCSFsorgz5MFwepoXd383W/cPTIj.', '<EMAIL>', 1, '2025-05-28 03:56:39', '2025-06-19 19:24:15');

-- --------------------------------------------------------

--
-- Table structure for table `app_users`
--

CREATE TABLE `app_users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `full_name` varchar(100) DEFAULT NULL,
  `role` varchar(20) DEFAULT 'user',
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `allowed_zones` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'JSON array of zone IDs user can access. NULL means all zones.' CHECK (json_valid(`allowed_zones`)),
  `zone_access_type` enum('all','specific','none') DEFAULT 'specific' COMMENT 'Type of zone access: all=superuser, specific=limited zones, none=no access'
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `app_users`
--

INSERT INTO `app_users` (`id`, `username`, `password_hash`, `email`, `full_name`, `role`, `is_active`, `last_login`, `created_at`, `updated_at`, `allowed_zones`, `zone_access_type`) VALUES
(1, 'appadmin', '$2a$10$5SNiMn6/uJ7PS5ATcIQ1n.NrKlsSYyR7o5nbUkD26YwQSh4s3WFOK', '<EMAIL>', 'Super Administrator', 'manager', 1, '2025-06-19 18:25:34', '2025-06-19 10:56:36', '2025-06-19 19:24:39', NULL, 'all'),
(5, 'northzone', '$2a$10$sjRBdsVnOhV.jEpV.sXaVeaDPODcQPU4U4fVbt2/FJwrC6QxNrcHi', '<EMAIL>', 'North Zone Operator', 'operator', 1, '2025-06-19 16:48:43', '2025-06-19 10:56:36', '2025-06-19 18:50:33', '[1]', 'specific'),
(6, 'southzone', '$2a$10$9C4hFFCaX1z4iU1CCWWs0.YomV4UNjBd5IrKZ5zsK.hnQjv./rwE6', '<EMAIL>', 'South Zone Operator', 'operator', 1, NULL, '2025-06-19 10:56:36', '2025-06-19 18:50:39', '[2]', 'specific'),
(7, 'eastzone', '$2a$10$3ALwMxL9n5C03M2k12M3hOg6eXApMRy3EW4gDGAj998Rj4zOGwD9e', '<EMAIL>', 'East Zone Operator', 'operator', 1, '2025-06-19 18:21:16', '2025-06-19 10:56:36', '2025-06-19 18:50:45', '[3]', 'specific'),
(8, 'westzone', '$2a$10$3E7da.RAIMxEgZEdlNgzvOFvPcr4T2epem6sdXoRdA3VekCEvJAbm', '<EMAIL>', 'West Zone Operator', 'operator', 1, NULL, '2025-06-19 10:56:36', '2025-06-19 18:50:50', '[4]', 'specific'),
(9, 'southwestzone', '$2a$10$vQe6qwiaz1jsRDns7LexWemLQuyj6ww3tUK0adCnbFOyD1ER.y4LG', '<EMAIL>', 'South West Zone Operator', 'operator', 1, NULL, '2025-06-19 10:56:36', '2025-06-19 18:50:56', '[5]', 'specific');

-- --------------------------------------------------------

--
-- Table structure for table `locations`
--

CREATE TABLE `locations` (
  `id` int(11) NOT NULL,
  `zone_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `pump_count` int(11) DEFAULT 0,
  `location_type` enum('CSPS','SWPS','TSPS') DEFAULT 'SWPS',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `operator_name` varchar(100) DEFAULT NULL,
  `operator_number` varchar(20) DEFAULT NULL,
  `supervisor_number` varchar(20) DEFAULT NULL,
  `torrent_service_1` varchar(50) DEFAULT NULL,
  `supervisor_name` varchar(50) DEFAULT NULL,
  `torrent_contact` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `locations`
--

INSERT INTO `locations` (`id`, `zone_id`, `name`, `description`, `pump_count`, `location_type`, `created_at`, `updated_at`, `operator_name`, `operator_number`, `supervisor_number`, `torrent_service_1`, `supervisor_name`, `torrent_contact`) VALUES
(382, 5, 'SHREENAND NAGAR', 'SHREENAND NAGAR Pumping Station', 5, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(383, 4, 'JAYDEEP TOWER', 'JAYDEEP TOWER Pumping Station', 2, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(384, 4, 'AKHBAR NAGAR', 'AKHBAR NAGAR Pumping Station', 5, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(385, 2, 'VYAYAMSHALA', 'VYAYAMSHALA Pumping Station', 1, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(386, 2, 'GEBANSHAPIR', 'GEBANSHAPIR Pumping Station', 1, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(387, 2, 'NOOR NAGAR', 'NOOR NAGAR Pumping Station', 2, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(388, 2, 'DEVIMATA', 'DEVIMATA Pumping Station', 3, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(389, 2, 'BHAIRAVNATH', 'BHAIRAVNATH Pumping Station', 4, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(390, 2, 'NIRMANALA', 'NIRMANALA Pumping Station', 2, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(391, 2, 'AVAKAR HALL', 'AVAKAR HALL Pumping Station', 3, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(392, 2, 'SURELIYA', 'SURELIYA Pumping Station', 3, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(393, 2, 'MATLA CIRCLE', 'MATLA CIRCLE Pumping Station', 2, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(394, 2, 'GURUJI BRIDGE', 'GURUJI BRIDGE Pumping Station', 3, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(395, 2, 'SMRUTI MANDIR', 'SMRUTI MANDIR Pumping Station', 1, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(396, 2, 'NAVANA VATVA', 'NAVANA VATVA Pumping Station', 10, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(397, 2, 'NAVANAVATAVA', 'NAVANAVATAVA Pumping Station', 4, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(398, 2, 'POOJA FARM', 'POOJA FARM Pumping Station', 4, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(399, 2, 'ARBUDA NAGAR', 'ARBUDA NAGAR Pumping Station', 2, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(400, 3, 'ODHAV AMBIKANAGAR', 'ODHAV AMBIKANAGAR Pumping Station', 3, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(401, 3, 'VASTRAL', 'VASTRAL Pumping Station', 4, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(402, 3, 'VIRATNAGAR', 'VIRATNAGAR Pumping Station', 8, 'CSPS', '2025-05-29 09:49:20', '2025-06-19 09:05:39', 'Oname', '04545', 'SName', '545412545', '577777', '48454564'),
(403, 3, 'ODHAVFIRE STATION', 'ODHAVFIRE STATION Pumping Station', 6, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(404, 1, 'HARIVILLA', 'HARIVILLA Pumping Station', 3, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:59:26', NULL, NULL, NULL, NULL, NULL, NULL),
(405, 1, 'JAY CHEMICAL', 'JAY CHEMICAL Pumping Station', 4, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:59:32', NULL, NULL, NULL, NULL, NULL, NULL),
(406, 1, 'NEW NIKOL', 'NEW NIKOL Pumping Station', 5, 'TSPS', '2025-05-29 09:49:20', '2025-06-19 08:49:52', NULL, NULL, NULL, NULL, NULL, NULL),
(407, 3, 'ODHAV 100 MLD', 'ODHAV 100 MLD Pumping Station', 6, 'SWPS', '2025-05-29 09:49:20', '2025-06-08 13:51:08', 'fjfew', '5487848', '848484878', '848484', '48484848', '4484884'),
(408, 1, 'NAVYUG', 'NAVYUG Pumping Station', 3, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(409, 1, 'BAPA SITARAM MADHULI', 'BAPA SITARAM MADHULI Pumping Station', 1, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(410, 1, 'KUBERNAGAR', 'KUBERNAGAR Pumping Station', 6, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(411, 1, 'KUBER NAGAR', 'KUBER NAGAR Pumping Station', 2, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(412, 1, 'RAJIV PARK', 'RAJIV PARK Pumping Station', 1, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(413, 1, 'MALEKSBAN', 'MALEKSBAN Pumping Station', 3, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(414, 1, 'TRIKAMLAL', 'TRIKAMLAL Pumping Station', 3, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(415, 1, 'NARODA HANSPURA', 'NARODA HANSPURA Pumping Station', 3, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(416, 1, 'NARODA GAYATRI', 'NARODA GAYATRI Pumping Station', 6, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(417, 1, 'DEHGAM ROAD', 'DEHGAM ROAD Pumping Station', 2, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(418, 1, 'GAYTRINAGAR', 'GAYTRINAGAR Pumping Station', 3, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(419, 4, 'DANI LIMDA', 'DANI LIMDA Pumping Station', 8, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(420, 1, 'JAMALPUR', 'JAMALPUR Pumping Station', 9, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(421, 1, 'NEW CHAMANPURA', 'NEW CHAMANPURA Pumping Station', 7, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(422, 1, 'NEW GOMTIPUR', 'NEW GOMTIPUR Pumping Station', 8, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(423, 4, 'PIRANA NEW', 'PIRANA NEW Pumping Station', 8, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(424, 4, 'PIRANA TERMINAL', 'PIRANA TERMINAL Pumping Station', 15, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(425, 1, 'TRIKAMPURA', 'TRIKAMPURA Pumping Station', 5, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(426, 4, 'VEJALPUR', 'VEJALPUR Pumping Station', 9, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL),
(427, 4, 'VINZOAL TERMINAL', 'VINZOAL TERMINAL Pumping Station', 10, 'SWPS', '2025-05-29 09:49:20', '2025-05-29 09:49:20', NULL, NULL, NULL, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `tags`
--

CREATE TABLE `tags` (
  `id` int(11) NOT NULL,
  `location_id` int(11) NOT NULL,
  `opc_address` varchar(500) NOT NULL,
  `mqtt_topic` varchar(500) NOT NULL,
  `tag_name` varchar(255) NOT NULL,
  `tag_type` enum('LEVEL','PUMP_STATUS','FLOW_RATE','TOTALIZER','OTHER') DEFAULT 'OTHER',
  `unit` varchar(50) DEFAULT NULL,
  `min_value` decimal(10,4) DEFAULT NULL,
  `max_value` decimal(10,4) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `tags`
--

INSERT INTO `tags` (`id`, `location_id`, `opc_address`, `mqtt_topic`, `tag_name`, `tag_type`, `unit`, `min_value`, `max_value`, `is_active`, `created_at`, `updated_at`) VALUES
(3165, 384, 'ns=2;s=AKHBAR NAGAR.5 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3166, 384, 'ns=2;s=AKHBAR NAGAR.5 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3167, 384, 'ns=2;s=AKHBAR NAGAR.5 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3168, 384, 'ns=2;s=AKHBAR NAGAR.5 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3169, 384, 'ns=2;s=AKHBAR NAGAR.5 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3170, 384, 'ns=2;s=AKHBAR NAGAR.5 PUMP.PUMP_5_RFB', 'AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/PUMP_5_RFB', 'PUMP_5_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3171, 384, 'ns=2;s=AKHBAR NAGAR.5 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3172, 384, 'ns=2;s=AKHBAR NAGAR.5 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3173, 384, 'ns=2;s=AKHBAR NAGAR.5 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3174, 399, 'ns=2;s=ARBUDA NAGAR.2 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/ARBUDA NAGAR/2 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3175, 399, 'ns=2;s=ARBUDA NAGAR.2 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/ARBUDA NAGAR/2 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3176, 399, 'ns=2;s=ARBUDA NAGAR.2 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/ARBUDA NAGAR/2 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3177, 399, 'ns=2;s=ARBUDA NAGAR.2 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/ARBUDA NAGAR/2 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3178, 399, 'ns=2;s=ARBUDA NAGAR.2 PUMP.SEWAGE_LAST_TOTALIZER', 'AMC/Storm/amcstorm/ARBUDA NAGAR/2 PUMP/SEWAGE_LAST_TOTALIZER', 'SEWAGE_LAST_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3179, 399, 'ns=2;s=ARBUDA NAGAR.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/ARBUDA NAGAR/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3180, 399, 'ns=2;s=ARBUDA NAGAR.2 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/ARBUDA NAGAR/2 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3181, 391, 'ns=2;s=AVAKAR HALL.3 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/AVAKAR HALL/3 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3182, 391, 'ns=2;s=AVAKAR HALL.3 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/AVAKAR HALL/3 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3183, 391, 'ns=2;s=AVAKAR HALL.3 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/AVAKAR HALL/3 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3184, 391, 'ns=2;s=AVAKAR HALL.3 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/AVAKAR HALL/3 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3185, 391, 'ns=2;s=AVAKAR HALL.3 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/AVAKAR HALL/3 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3186, 391, 'ns=2;s=AVAKAR HALL.3 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/AVAKAR HALL/3 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3187, 391, 'ns=2;s=AVAKAR HALL.3 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/AVAKAR HALL/3 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3188, 409, 'ns=2;s=BAPA SITARAM MADHULI.1 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/BAPA SITARAM MADHULI/1 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3189, 409, 'ns=2;s=BAPA SITARAM MADHULI.1 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/BAPA SITARAM MADHULI/1 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3190, 409, 'ns=2;s=BAPA SITARAM MADHULI.1 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/BAPA SITARAM MADHULI/1 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3191, 409, 'ns=2;s=BAPA SITARAM MADHULI.1 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/BAPA SITARAM MADHULI/1 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3192, 409, 'ns=2;s=BAPA SITARAM MADHULI.1 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/BAPA SITARAM MADHULI/1 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3193, 389, 'ns=2;s=BHAIRAVNATH.4 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/BHAIRAVNATH/4 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3194, 389, 'ns=2;s=BHAIRAVNATH.4 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/BHAIRAVNATH/4 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3195, 389, 'ns=2;s=BHAIRAVNATH.4 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/BHAIRAVNATH/4 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3196, 389, 'ns=2;s=BHAIRAVNATH.4 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/BHAIRAVNATH/4 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3197, 389, 'ns=2;s=BHAIRAVNATH.4 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/BHAIRAVNATH/4 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3198, 389, 'ns=2;s=BHAIRAVNATH.4 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/BHAIRAVNATH/4 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3199, 389, 'ns=2;s=BHAIRAVNATH.4 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/BHAIRAVNATH/4 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3200, 389, 'ns=2;s=BHAIRAVNATH.4 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/BHAIRAVNATH/4 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:20', '2025-05-29 09:49:20'),
(3201, 419, 'ns=2;s=DANI LIMDA.5 PUMP.HT_RFB', 'AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/HT_RFB', 'HT_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3202, 419, 'ns=2;s=DANI LIMDA.5 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3203, 419, 'ns=2;s=DANI LIMDA.5 PUMP.LT_RFB', 'AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/LT_RFB', 'LT_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3204, 419, 'ns=2;s=DANI LIMDA.5 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3205, 419, 'ns=2;s=DANI LIMDA.5 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3206, 419, 'ns=2;s=DANI LIMDA.5 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3207, 419, 'ns=2;s=DANI LIMDA.5 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3208, 419, 'ns=2;s=DANI LIMDA.5 PUMP.PUMP_5_RFB', 'AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/PUMP_5_RFB', 'PUMP_5_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3209, 419, 'ns=2;s=DANI LIMDA.5 PUMP.PUMP_6_RFB', 'AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/PUMP_6_RFB', 'PUMP_6_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3210, 419, 'ns=2;s=DANI LIMDA.5 PUMP.PUMP_7_RFB', 'AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/PUMP_7_RFB', 'PUMP_7_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3211, 419, 'ns=2;s=DANI LIMDA.5 PUMP.PUMP_8_RFB', 'AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/PUMP_8_RFB', 'PUMP_8_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3212, 419, 'ns=2;s=DANI LIMDA.5 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3213, 419, 'ns=2;s=DANI LIMDA.5 PUMP.SEWAGE_TOTALIZER_1', 'AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/SEWAGE_TOTALIZER_1', 'SEWAGE_TOTALIZER_1', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3214, 417, 'ns=2;s=DEHGAM ROAD.2 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/DEHGAM ROAD/2 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3215, 417, 'ns=2;s=DEHGAM ROAD.2 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/DEHGAM ROAD/2 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3216, 417, 'ns=2;s=DEHGAM ROAD.2 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/DEHGAM ROAD/2 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3217, 417, 'ns=2;s=DEHGAM ROAD.2 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/DEHGAM ROAD/2 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3218, 417, 'ns=2;s=DEHGAM ROAD.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/DEHGAM ROAD/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3219, 417, 'ns=2;s=DEHGAM ROAD.2 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/DEHGAM ROAD/2 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3220, 388, 'ns=2;s=DEVIMATA.3 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/DEVIMATA/3 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3221, 388, 'ns=2;s=DEVIMATA.3 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/DEVIMATA/3 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3222, 388, 'ns=2;s=DEVIMATA.3 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/DEVIMATA/3 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3223, 388, 'ns=2;s=DEVIMATA.3 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/DEVIMATA/3 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3224, 388, 'ns=2;s=DEVIMATA.3 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/DEVIMATA/3 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3225, 388, 'ns=2;s=DEVIMATA.3 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/DEVIMATA/3 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3226, 388, 'ns=2;s=DEVIMATA.3 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/DEVIMATA/3 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3227, 418, 'ns=2;s=GAYTRINAGAR.3 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/GAYTRINAGAR/3 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3228, 418, 'ns=2;s=GAYTRINAGAR.3 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/GAYTRINAGAR/3 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3229, 418, 'ns=2;s=GAYTRINAGAR.3 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/GAYTRINAGAR/3 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3230, 418, 'ns=2;s=GAYTRINAGAR.3 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/GAYTRINAGAR/3 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3231, 418, 'ns=2;s=GAYTRINAGAR.3 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/GAYTRINAGAR/3 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3232, 418, 'ns=2;s=GAYTRINAGAR.3 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/GAYTRINAGAR/3 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3233, 418, 'ns=2;s=GAYTRINAGAR.3 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/GAYTRINAGAR/3 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3234, 386, 'ns=2;s=GEBANSHAPIR.1 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/GEBANSHAPIR/1 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3235, 386, 'ns=2;s=GEBANSHAPIR.1 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/GEBANSHAPIR/1 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3236, 386, 'ns=2;s=GEBANSHAPIR.1 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/GEBANSHAPIR/1 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3237, 386, 'ns=2;s=GEBANSHAPIR.1 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/GEBANSHAPIR/1 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3238, 386, 'ns=2;s=GEBANSHAPIR.1 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/GEBANSHAPIR/1 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3239, 394, 'ns=2;s=GURUJI BRIDGE.2 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/GURUJI BRIDGE/2 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3240, 394, 'ns=2;s=GURUJI BRIDGE.2 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/GURUJI BRIDGE/2 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3241, 394, 'ns=2;s=GURUJI BRIDGE.2 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/GURUJI BRIDGE/2 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3242, 394, 'ns=2;s=GURUJI BRIDGE.2 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/GURUJI BRIDGE/2 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3243, 394, 'ns=2;s=GURUJI BRIDGE.2 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/GURUJI BRIDGE/2 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3244, 394, 'ns=2;s=GURUJI BRIDGE.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/GURUJI BRIDGE/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3245, 394, 'ns=2;s=GURUJI BRIDGE.2 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/GURUJI BRIDGE/2 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3246, 404, 'ns=2;s=HARIVILLA.1 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/HARIVILLA/1 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3247, 404, 'ns=2;s=HARIVILLA.1 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/HARIVILLA/1 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3248, 404, 'ns=2;s=HARIVILLA.1 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/HARIVILLA/1 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3249, 404, 'ns=2;s=HARIVILLA.1 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/HARIVILLA/1 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3250, 404, 'ns=2;s=HARIVILLA.1 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/HARIVILLA/1 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3251, 404, 'ns=2;s=HARIVILLA.1 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/HARIVILLA/1 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3252, 404, 'ns=2;s=HARIVILLA.1 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/HARIVILLA/1 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3253, 420, 'ns=2;s=JAMALPUR.9 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3254, 420, 'ns=2;s=JAMALPUR.9 PUMP.LEVEL_2', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/LEVEL_2', 'LEVEL_2', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3255, 420, 'ns=2;s=JAMALPUR.9 PUMP.LT_RFB', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/LT_RFB', 'LT_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3256, 420, 'ns=2;s=JAMALPUR.9 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3257, 420, 'ns=2;s=JAMALPUR.9 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3258, 420, 'ns=2;s=JAMALPUR.9 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3259, 420, 'ns=2;s=JAMALPUR.9 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3260, 420, 'ns=2;s=JAMALPUR.9 PUMP.PUMP_5_RFB', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_5_RFB', 'PUMP_5_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3261, 420, 'ns=2;s=JAMALPUR.9 PUMP.PUMP_6_RFB', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_6_RFB', 'PUMP_6_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3262, 420, 'ns=2;s=JAMALPUR.9 PUMP.PUMP_7_RFB', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_7_RFB', 'PUMP_7_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3263, 420, 'ns=2;s=JAMALPUR.9 PUMP.PUMP_8_RFB', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_8_RFB', 'PUMP_8_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3264, 420, 'ns=2;s=JAMALPUR.9 PUMP.PUMP_9_RFB', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_9_RFB', 'PUMP_9_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3265, 420, 'ns=2;s=JAMALPUR.9 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3266, 420, 'ns=2;s=JAMALPUR.9 PUMP.SEWAGE_CURRENT_DAY_2', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/SEWAGE_CURRENT_DAY_2', 'SEWAGE_CURRENT_DAY_2', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3267, 420, 'ns=2;s=JAMALPUR.9 PUMP.SEWAGE_CURRENT_DAY_3', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/SEWAGE_CURRENT_DAY_3', 'SEWAGE_CURRENT_DAY_3', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3268, 420, 'ns=2;s=JAMALPUR.9 PUMP.SEWAGE_TOTALIZER_1', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/SEWAGE_TOTALIZER_1', 'SEWAGE_TOTALIZER_1', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3269, 420, 'ns=2;s=JAMALPUR.9 PUMP.SEWAGE_TOTALIZER_2', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/SEWAGE_TOTALIZER_2', 'SEWAGE_TOTALIZER_2', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3270, 420, 'ns=2;s=JAMALPUR.9 PUMP.SEWAGE_TOTALIZER_3', 'AMC/Storm/amcstorm/JAMALPUR/9 PUMP/SEWAGE_TOTALIZER_3', 'SEWAGE_TOTALIZER_3', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3271, 405, 'ns=2;s=JAY CHEMICAL.2 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/JAY CHEMICAL/2 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3272, 405, 'ns=2;s=JAY CHEMICAL.2 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/JAY CHEMICAL/2 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3273, 405, 'ns=2;s=JAY CHEMICAL.2 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/JAY CHEMICAL/2 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3274, 405, 'ns=2;s=JAY CHEMICAL.2 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/JAY CHEMICAL/2 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3275, 405, 'ns=2;s=JAY CHEMICAL.2 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/JAY CHEMICAL/2 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3276, 405, 'ns=2;s=JAY CHEMICAL.2 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/JAY CHEMICAL/2 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3277, 405, 'ns=2;s=JAY CHEMICAL.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/JAY CHEMICAL/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3278, 405, 'ns=2;s=JAY CHEMICAL.2 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/JAY CHEMICAL/2 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3279, 383, 'ns=2;s=JAYDEEP TOWER.2 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/JAYDEEP TOWER/2 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3280, 383, 'ns=2;s=JAYDEEP TOWER.2 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/JAYDEEP TOWER/2 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3281, 383, 'ns=2;s=JAYDEEP TOWER.2 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/JAYDEEP TOWER/2 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3282, 383, 'ns=2;s=JAYDEEP TOWER.2 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/JAYDEEP TOWER/2 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3283, 383, 'ns=2;s=JAYDEEP TOWER.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/JAYDEEP TOWER/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3284, 383, 'ns=2;s=JAYDEEP TOWER.2 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/JAYDEEP TOWER/2 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3285, 411, 'ns=2;s=KUBER NAGAR.2 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/KUBER NAGAR/2 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3286, 411, 'ns=2;s=KUBER NAGAR.2 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/KUBER NAGAR/2 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3287, 411, 'ns=2;s=KUBER NAGAR.2 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/KUBER NAGAR/2 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3288, 411, 'ns=2;s=KUBER NAGAR.2 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/KUBER NAGAR/2 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3289, 411, 'ns=2;s=KUBER NAGAR.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/KUBER NAGAR/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3290, 411, 'ns=2;s=KUBER NAGAR.2 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/KUBER NAGAR/2 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3291, 410, 'ns=2;s=KUBERNAGAR.4 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3292, 410, 'ns=2;s=KUBERNAGAR.4 PUMP.LT_RFB', 'AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/LT_RFB', 'LT_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3293, 410, 'ns=2;s=KUBERNAGAR.4 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3294, 410, 'ns=2;s=KUBERNAGAR.4 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3295, 410, 'ns=2;s=KUBERNAGAR.4 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3296, 410, 'ns=2;s=KUBERNAGAR.4 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3297, 410, 'ns=2;s=KUBERNAGAR.4 PUMP.PUMP_5_RFB', 'AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/PUMP_5_RFB', 'PUMP_5_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3298, 410, 'ns=2;s=KUBERNAGAR.4 PUMP.PUMP_6_RFB', 'AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/PUMP_6_RFB', 'PUMP_6_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3299, 410, 'ns=2;s=KUBERNAGAR.4 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3300, 410, 'ns=2;s=KUBERNAGAR.4 PUMP.SEWAGE_TOTALIZER_1', 'AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/SEWAGE_TOTALIZER_1', 'SEWAGE_TOTALIZER_1', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3301, 413, 'ns=2;s=MALEKSBAN.3 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/MALEKSBAN/3 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3302, 413, 'ns=2;s=MALEKSBAN.3 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/MALEKSBAN/3 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3303, 413, 'ns=2;s=MALEKSBAN.3 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/MALEKSBAN/3 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3304, 413, 'ns=2;s=MALEKSBAN.3 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/MALEKSBAN/3 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3305, 413, 'ns=2;s=MALEKSBAN.3 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/MALEKSBAN/3 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3306, 413, 'ns=2;s=MALEKSBAN.3 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/MALEKSBAN/3 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3307, 413, 'ns=2;s=MALEKSBAN.3 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/MALEKSBAN/3 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3308, 393, 'ns=2;s=MATLA CIRCLE.2 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/MATLA CIRCLE/2 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3309, 393, 'ns=2;s=MATLA CIRCLE.2 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/MATLA CIRCLE/2 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3310, 393, 'ns=2;s=MATLA CIRCLE.2 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/MATLA CIRCLE/2 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3311, 393, 'ns=2;s=MATLA CIRCLE.2 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/MATLA CIRCLE/2 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3312, 393, 'ns=2;s=MATLA CIRCLE.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/MATLA CIRCLE/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3313, 393, 'ns=2;s=MATLA CIRCLE.2 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/MATLA CIRCLE/2 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3314, 416, 'ns=2;s=NARODA GAYATRI.6 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3315, 416, 'ns=2;s=NARODA GAYATRI.6 PUMP.LT_RFB', 'AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/LT_RFB', 'LT_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3316, 416, 'ns=2;s=NARODA GAYATRI.6 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3317, 416, 'ns=2;s=NARODA GAYATRI.6 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3318, 416, 'ns=2;s=NARODA GAYATRI.6 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3319, 416, 'ns=2;s=NARODA GAYATRI.6 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3320, 416, 'ns=2;s=NARODA GAYATRI.6 PUMP.PUMP_5_RFB', 'AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/PUMP_5_RFB', 'PUMP_5_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3321, 416, 'ns=2;s=NARODA GAYATRI.6 PUMP.PUMP_6_RFB', 'AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/PUMP_6_RFB', 'PUMP_6_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3322, 416, 'ns=2;s=NARODA GAYATRI.6 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3323, 416, 'ns=2;s=NARODA GAYATRI.6 PUMP.SEWAGE_TOTALIZER_1', 'AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/SEWAGE_TOTALIZER_1', 'SEWAGE_TOTALIZER_1', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3324, 415, 'ns=2;s=NARODA HANSPURA.3PUMP.LEVEL_1', 'AMC/Storm/amcstorm/NARODA HANSPURA/3PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3325, 415, 'ns=2;s=NARODA HANSPURA.3PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/NARODA HANSPURA/3PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3326, 415, 'ns=2;s=NARODA HANSPURA.3PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/NARODA HANSPURA/3PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3327, 415, 'ns=2;s=NARODA HANSPURA.3PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/NARODA HANSPURA/3PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3328, 415, 'ns=2;s=NARODA HANSPURA.3PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/NARODA HANSPURA/3PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3329, 415, 'ns=2;s=NARODA HANSPURA.3PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/NARODA HANSPURA/3PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3330, 415, 'ns=2;s=NARODA HANSPURA.3PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/NARODA HANSPURA/3PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3331, 396, 'ns=2;s=NAVANA VATVA.5 PUMP.HT_RFB', 'AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/HT_RFB', 'HT_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3332, 396, 'ns=2;s=NAVANA VATVA.5 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3333, 396, 'ns=2;s=NAVANA VATVA.5 PUMP.LT_RFB', 'AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/LT_RFB', 'LT_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3334, 396, 'ns=2;s=NAVANA VATVA.5 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3335, 396, 'ns=2;s=NAVANA VATVA.5 PUMP.PUMP_10_RFB', 'AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_10_RFB', 'PUMP_10_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3336, 396, 'ns=2;s=NAVANA VATVA.5 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3337, 396, 'ns=2;s=NAVANA VATVA.5 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3338, 396, 'ns=2;s=NAVANA VATVA.5 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3339, 396, 'ns=2;s=NAVANA VATVA.5 PUMP.PUMP_5_RFB', 'AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_5_RFB', 'PUMP_5_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3340, 396, 'ns=2;s=NAVANA VATVA.5 PUMP.PUMP_6_RFB', 'AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_6_RFB', 'PUMP_6_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3341, 396, 'ns=2;s=NAVANA VATVA.5 PUMP.PUMP_7_RFB', 'AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_7_RFB', 'PUMP_7_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3342, 396, 'ns=2;s=NAVANA VATVA.5 PUMP.PUMP_8_RFB', 'AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_8_RFB', 'PUMP_8_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3343, 396, 'ns=2;s=NAVANA VATVA.5 PUMP.PUMP_9_RFB', 'AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_9_RFB', 'PUMP_9_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3344, 396, 'ns=2;s=NAVANA VATVA.5 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3345, 396, 'ns=2;s=NAVANA VATVA.5 PUMP.SEWAGE_CURRENT_DAY_2', 'AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/SEWAGE_CURRENT_DAY_2', 'SEWAGE_CURRENT_DAY_2', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3346, 396, 'ns=2;s=NAVANA VATVA.5 PUMP.SEWAGE_TOTALIZER_1', 'AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/SEWAGE_TOTALIZER_1', 'SEWAGE_TOTALIZER_1', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3347, 397, 'ns=2;s=NAVANAVATAVA.4 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/NAVANAVATAVA/4 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3348, 397, 'ns=2;s=NAVANAVATAVA.4 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/NAVANAVATAVA/4 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3349, 397, 'ns=2;s=NAVANAVATAVA.4 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/NAVANAVATAVA/4 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3350, 397, 'ns=2;s=NAVANAVATAVA.4 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/NAVANAVATAVA/4 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3351, 397, 'ns=2;s=NAVANAVATAVA.4 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/NAVANAVATAVA/4 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3352, 397, 'ns=2;s=NAVANAVATAVA.4 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/NAVANAVATAVA/4 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3353, 397, 'ns=2;s=NAVANAVATAVA.4 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/NAVANAVATAVA/4 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3354, 397, 'ns=2;s=NAVANAVATAVA.4 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/NAVANAVATAVA/4 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3355, 408, 'ns=2;s=NAVYUG.3 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/NAVYUG/3 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3356, 408, 'ns=2;s=NAVYUG.3 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/NAVYUG/3 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3357, 408, 'ns=2;s=NAVYUG.3 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/NAVYUG/3 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3358, 408, 'ns=2;s=NAVYUG.3 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/NAVYUG/3 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3359, 408, 'ns=2;s=NAVYUG.3 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/NAVYUG/3 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3360, 408, 'ns=2;s=NAVYUG.3 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/NAVYUG/3 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3361, 408, 'ns=2;s=NAVYUG.3 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/NAVYUG/3 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3362, 421, 'ns=2;s=NEW CHAMANPURA.6 PUMP.HT_RFB', 'AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/HT_RFB', 'HT_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3363, 421, 'ns=2;s=NEW CHAMANPURA.6 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3364, 421, 'ns=2;s=NEW CHAMANPURA.6 PUMP.LEVEL_VAL_DL', 'AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/LEVEL_VAL_DL', 'LEVEL_VAL_DL', 'OTHER', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3365, 421, 'ns=2;s=NEW CHAMANPURA.6 PUMP.LT_1_RFB', 'AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/LT_1_RFB', 'LT_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3366, 421, 'ns=2;s=NEW CHAMANPURA.6 PUMP.LT_2_RFB', 'AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/LT_2_RFB', 'LT_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3367, 421, 'ns=2;s=NEW CHAMANPURA.6 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3368, 421, 'ns=2;s=NEW CHAMANPURA.6 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3369, 421, 'ns=2;s=NEW CHAMANPURA.6 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3370, 421, 'ns=2;s=NEW CHAMANPURA.6 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3371, 421, 'ns=2;s=NEW CHAMANPURA.6 PUMP.PUMP_5_RFB', 'AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/PUMP_5_RFB', 'PUMP_5_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3372, 421, 'ns=2;s=NEW CHAMANPURA.6 PUMP.PUMP_6_RFB', 'AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/PUMP_6_RFB', 'PUMP_6_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3373, 421, 'ns=2;s=NEW CHAMANPURA.6 PUMP.PUMP_7_RFB', 'AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/PUMP_7_RFB', 'PUMP_7_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3374, 421, 'ns=2;s=NEW CHAMANPURA.6 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3375, 421, 'ns=2;s=NEW CHAMANPURA.6 PUMP.SEWAGE_CURRENT_DAY_2', 'AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/SEWAGE_CURRENT_DAY_2', 'SEWAGE_CURRENT_DAY_2', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3376, 421, 'ns=2;s=NEW CHAMANPURA.6 PUMP.SEWAGE_TOTALIZER_1', 'AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/SEWAGE_TOTALIZER_1', 'SEWAGE_TOTALIZER_1', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3377, 421, 'ns=2;s=NEW CHAMANPURA.6 PUMP.SEWAGE_TOTALIZER_2', 'AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/SEWAGE_TOTALIZER_2', 'SEWAGE_TOTALIZER_2', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3378, 422, 'ns=2;s=NEW GOMTIPUR.8 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3379, 422, 'ns=2;s=NEW GOMTIPUR.8 PUMP.LEVEL_VAL_DL', 'AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/LEVEL_VAL_DL', 'LEVEL_VAL_DL', 'OTHER', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3380, 422, 'ns=2;s=NEW GOMTIPUR.8 PUMP.LT_RFB', 'AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/LT_RFB', 'LT_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3381, 422, 'ns=2;s=NEW GOMTIPUR.8 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3382, 422, 'ns=2;s=NEW GOMTIPUR.8 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3383, 422, 'ns=2;s=NEW GOMTIPUR.8 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3384, 422, 'ns=2;s=NEW GOMTIPUR.8 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3385, 422, 'ns=2;s=NEW GOMTIPUR.8 PUMP.PUMP_5_RFB', 'AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/PUMP_5_RFB', 'PUMP_5_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3386, 422, 'ns=2;s=NEW GOMTIPUR.8 PUMP.PUMP_6_RFB', 'AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/PUMP_6_RFB', 'PUMP_6_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3387, 422, 'ns=2;s=NEW GOMTIPUR.8 PUMP.PUMP_7_RFB', 'AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/PUMP_7_RFB', 'PUMP_7_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3388, 422, 'ns=2;s=NEW GOMTIPUR.8 PUMP.PUMP_8_RFB', 'AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/PUMP_8_RFB', 'PUMP_8_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3389, 422, 'ns=2;s=NEW GOMTIPUR.8 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3390, 422, 'ns=2;s=NEW GOMTIPUR.8 PUMP.SEWAGE_TOTALIZER_1', 'AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/SEWAGE_TOTALIZER_1', 'SEWAGE_TOTALIZER_1', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3391, 406, 'ns=2;s=NEW NIKOL.7 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3392, 406, 'ns=2;s=NEW NIKOL.7 PUMP.LT_RFB', 'AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/LT_RFB', 'LT_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3393, 406, 'ns=2;s=NEW NIKOL.7 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3394, 406, 'ns=2;s=NEW NIKOL.7 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3395, 406, 'ns=2;s=NEW NIKOL.7 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3396, 406, 'ns=2;s=NEW NIKOL.7 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3397, 406, 'ns=2;s=NEW NIKOL.7 PUMP.PUMP_5_RFB', 'AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/PUMP_5_RFB', 'PUMP_5_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3398, 406, 'ns=2;s=NEW NIKOL.7 PUMP.PUMP_6_RFB', 'AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/PUMP_6_RFB', 'PUMP_6_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3399, 406, 'ns=2;s=NEW NIKOL.7 PUMP.PUMP_7_RFB', 'AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/PUMP_7_RFB', 'PUMP_7_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21');
INSERT INTO `tags` (`id`, `location_id`, `opc_address`, `mqtt_topic`, `tag_name`, `tag_type`, `unit`, `min_value`, `max_value`, `is_active`, `created_at`, `updated_at`) VALUES
(3400, 406, 'ns=2;s=NEW NIKOL.7 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3401, 406, 'ns=2;s=NEW NIKOL.7 PUMP.SEWAGE_TOTALIZER_1', 'AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/SEWAGE_TOTALIZER_1', 'SEWAGE_TOTALIZER_1', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3402, 390, 'ns=2;s=NIRMANALA.2 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/NIRMANALA/2 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3403, 390, 'ns=2;s=NIRMANALA.2 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/NIRMANALA/2 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3404, 390, 'ns=2;s=NIRMANALA.2 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/NIRMANALA/2 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3405, 390, 'ns=2;s=NIRMANALA.2 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/NIRMANALA/2 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3406, 390, 'ns=2;s=NIRMANALA.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/NIRMANALA/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3407, 390, 'ns=2;s=NIRMANALA.2 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/NIRMANALA/2 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3408, 387, 'ns=2;s=NOOR NAGAR.2 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/NOOR NAGAR/2 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3409, 387, 'ns=2;s=NOOR NAGAR.2 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/NOOR NAGAR/2 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3410, 387, 'ns=2;s=NOOR NAGAR.2 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/NOOR NAGAR/2 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3411, 387, 'ns=2;s=NOOR NAGAR.2 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/NOOR NAGAR/2 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3412, 387, 'ns=2;s=NOOR NAGAR.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/NOOR NAGAR/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3413, 387, 'ns=2;s=NOOR NAGAR.2 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/NOOR NAGAR/2 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3414, 407, 'ns=2;s=ODHAV 100 MLD.6 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3415, 407, 'ns=2;s=ODHAV 100 MLD.6 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3416, 407, 'ns=2;s=ODHAV 100 MLD.6 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3417, 407, 'ns=2;s=ODHAV 100 MLD.6 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3418, 407, 'ns=2;s=ODHAV 100 MLD.6 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3419, 407, 'ns=2;s=ODHAV 100 MLD.6 PUMP.PUMP_5_RFB', 'AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/PUMP_5_RFB', 'PUMP_5_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3420, 407, 'ns=2;s=ODHAV 100 MLD.6 PUMP.PUMP_6_RFB', 'AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/PUMP_6_RFB', 'PUMP_6_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3421, 407, 'ns=2;s=ODHAV 100 MLD.6 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3422, 407, 'ns=2;s=ODHAV 100 MLD.6 PUMP.SEWAGE_TOTALIZER_1', 'AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/SEWAGE_TOTALIZER_1', 'SEWAGE_TOTALIZER_1', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3423, 400, 'ns=2;s=ODHAV AMBIKANAGAR.3 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/ODHAV AMBIKANAGAR/3 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3424, 400, 'ns=2;s=ODHAV AMBIKANAGAR.3 PUMP.LEVEL_2', 'AMC/Storm/amcstorm/ODHAV AMBIKANAGAR/3 PUMP/LEVEL_2', 'LEVEL_2', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3425, 400, 'ns=2;s=ODHAV AMBIKANAGAR.3 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/ODHAV AMBIKANAGAR/3 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3426, 400, 'ns=2;s=ODHAV AMBIKANAGAR.3 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/ODHAV AMBIKANAGAR/3 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3427, 400, 'ns=2;s=ODHAV AMBIKANAGAR.3 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/ODHAV AMBIKANAGAR/3 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3428, 400, 'ns=2;s=ODHAV AMBIKANAGAR.3 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/ODHAV AMBIKANAGAR/3 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3429, 400, 'ns=2;s=ODHAV AMBIKANAGAR.3 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/ODHAV AMBIKANAGAR/3 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3430, 400, 'ns=2;s=ODHAV AMBIKANAGAR.3 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/ODHAV AMBIKANAGAR/3 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3431, 403, 'ns=2;s=ODHAVFIRE STATION.5 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3432, 403, 'ns=2;s=ODHAVFIRE STATION.5 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3433, 403, 'ns=2;s=ODHAVFIRE STATION.5 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3434, 403, 'ns=2;s=ODHAVFIRE STATION.5 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3435, 403, 'ns=2;s=ODHAVFIRE STATION.5 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3436, 403, 'ns=2;s=ODHAVFIRE STATION.5 PUMP.PUMP_5_RFB', 'AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/PUMP_5_RFB', 'PUMP_5_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3437, 403, 'ns=2;s=ODHAVFIRE STATION.5 PUMP.PUMP_6_RFB', 'AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/PUMP_6_RFB', 'PUMP_6_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3438, 403, 'ns=2;s=ODHAVFIRE STATION.5 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3439, 403, 'ns=2;s=ODHAVFIRE STATION.5 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3440, 403, 'ns=2;s=ODHAVFIRE STATION.5 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3441, 423, 'ns=2;s=PIRANA NEW.8 PUMP.HT_RFB', 'AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/HT_RFB', 'HT_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3442, 423, 'ns=2;s=PIRANA NEW.8 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3443, 423, 'ns=2;s=PIRANA NEW.8 PUMP.LT_1_RFB', 'AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/LT_1_RFB', 'LT_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3444, 423, 'ns=2;s=PIRANA NEW.8 PUMP.LT_2_RFB', 'AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/LT_2_RFB', 'LT_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3445, 423, 'ns=2;s=PIRANA NEW.8 PUMP.PREVEOUS DAY TOTALIZER', 'AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PREVEOUS DAY TOTALIZER', 'PREVEOUS DAY TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3446, 423, 'ns=2;s=PIRANA NEW.8 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3447, 423, 'ns=2;s=PIRANA NEW.8 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3448, 423, 'ns=2;s=PIRANA NEW.8 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3449, 423, 'ns=2;s=PIRANA NEW.8 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3450, 423, 'ns=2;s=PIRANA NEW.8 PUMP.PUMP_5_RFB', 'AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PUMP_5_RFB', 'PUMP_5_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3451, 423, 'ns=2;s=PIRANA NEW.8 PUMP.PUMP_6_RFB', 'AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PUMP_6_RFB', 'PUMP_6_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3452, 423, 'ns=2;s=PIRANA NEW.8 PUMP.PUMP_7_RFB', 'AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PUMP_7_RFB', 'PUMP_7_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3453, 423, 'ns=2;s=PIRANA NEW.8 PUMP.PUMP_8_RFB', 'AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PUMP_8_RFB', 'PUMP_8_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3454, 423, 'ns=2;s=PIRANA NEW.8 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3455, 423, 'ns=2;s=PIRANA NEW.8 PUMP.SEWAGE_TOTALIZER_1', 'AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/SEWAGE_TOTALIZER_1', 'SEWAGE_TOTALIZER_1', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3456, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.HT_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/HT_RFB', 'HT_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3457, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3458, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.LEVEL_2', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/LEVEL_2', 'LEVEL_2', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3459, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.LT_1_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/LT_1_RFB', 'LT_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3460, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.LT_2_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/LT_2_RFB', 'LT_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3461, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.LT_3_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/LT_3_RFB', 'LT_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3462, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.PREVEOUS DAY TOTALIZER', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PREVEOUS DAY TOTALIZER', 'PREVEOUS DAY TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3463, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3464, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3465, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3466, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3467, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.PUMP_5_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PUMP_5_RFB', 'PUMP_5_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3468, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.PUMP_6_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PUMP_6_RFB', 'PUMP_6_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3469, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.PUMP_7_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PUMP_7_RFB', 'PUMP_7_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3470, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.PUMP_8_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PUMP_8_RFB', 'PUMP_8_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3471, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.PUMP_9_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PUMP_9_RFB', 'PUMP_9_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3472, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.PUMP_10_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PUMP_10_RFB', 'PUMP_10_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3473, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.PUMP_11_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PUMP_11_RFB', 'PUMP_11_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3474, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.PUMP_12_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PUMP_12_RFB', 'PUMP_12_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3475, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.PUMP_13_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PUMP_13_RFB', 'PUMP_13_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3476, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.PUMP_14_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PUMP_14_RFB', 'PUMP_14_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3477, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.PUMP_15_RFB', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PUMP_15_RFB', 'PUMP_15_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3478, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3479, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.SEWAGE_CURRENT_DAY_2', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/SEWAGE_CURRENT_DAY_2', 'SEWAGE_CURRENT_DAY_2', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3480, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.SEWAGE_CURRENT_DAY_3', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/SEWAGE_CURRENT_DAY_3', 'SEWAGE_CURRENT_DAY_3', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3481, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.SEWAGE_CURRENT_DAY_4', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/SEWAGE_CURRENT_DAY_4', 'SEWAGE_CURRENT_DAY_4', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3482, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.SEWAGE_TOTALIZER_1', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/SEWAGE_TOTALIZER_1', 'SEWAGE_TOTALIZER_1', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3483, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.SEWAGE_TOTALIZER_2', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/SEWAGE_TOTALIZER_2', 'SEWAGE_TOTALIZER_2', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3484, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.SEWAGE_TOTALIZER_3', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/SEWAGE_TOTALIZER_3', 'SEWAGE_TOTALIZER_3', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3485, 424, 'ns=2;s=PIRANA TERMINAL.10 PUMP.SEWAGE_TOTALIZER_4', 'AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/SEWAGE_TOTALIZER_4', 'SEWAGE_TOTALIZER_4', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3486, 398, 'ns=2;s=POOJA FARM.4 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/POOJA FARM/4 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3487, 398, 'ns=2;s=POOJA FARM.4 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/POOJA FARM/4 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3488, 398, 'ns=2;s=POOJA FARM.4 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/POOJA FARM/4 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3489, 398, 'ns=2;s=POOJA FARM.4 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/POOJA FARM/4 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3490, 398, 'ns=2;s=POOJA FARM.4 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/POOJA FARM/4 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3491, 398, 'ns=2;s=POOJA FARM.4 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/POOJA FARM/4 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3492, 398, 'ns=2;s=POOJA FARM.4 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/POOJA FARM/4 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3493, 398, 'ns=2;s=POOJA FARM.4 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/POOJA FARM/4 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3494, 382, 'ns=2;s=SHREENAND NAGAR.5 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3495, 382, 'ns=2;s=SHREENAND NAGAR.5 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3496, 382, 'ns=2;s=SHREENAND NAGAR.5 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3497, 382, 'ns=2;s=SHREENAND NAGAR.5 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3498, 382, 'ns=2;s=SHREENAND NAGAR.5 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3499, 382, 'ns=2;s=SHREENAND NAGAR.5 PUMP.PUMP_5_RFB', 'AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/PUMP_5_RFB', 'PUMP_5_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3500, 382, 'ns=2;s=SHREENAND NAGAR.5 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3501, 382, 'ns=2;s=SHREENAND NAGAR.5 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3502, 382, 'ns=2;s=SHREENAND NAGAR.5 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3503, 412, 'ns=2;s=RAJIV PARK.1 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/RAJIV PARK/1 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3504, 412, 'ns=2;s=RAJIV PARK.1 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/RAJIV PARK/1 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3505, 412, 'ns=2;s=RAJIV PARK.1 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/RAJIV PARK/1 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3506, 412, 'ns=2;s=RAJIV PARK.1 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/RAJIV PARK/1 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3507, 412, 'ns=2;s=RAJIV PARK.1 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/RAJIV PARK/1 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3508, 395, 'ns=2;s=SMRUTI MANDIR.1PUMP.LEVEL_1', 'AMC/Storm/amcstorm/SMRUTI MANDIR/1PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3509, 395, 'ns=2;s=SMRUTI MANDIR.1PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/SMRUTI MANDIR/1PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3510, 395, 'ns=2;s=SMRUTI MANDIR.1PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/SMRUTI MANDIR/1PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3511, 395, 'ns=2;s=SMRUTI MANDIR.1PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/SMRUTI MANDIR/1PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3512, 395, 'ns=2;s=SMRUTI MANDIR.1PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/SMRUTI MANDIR/1PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3513, 392, 'ns=2;s=SURELIYA.3 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/SURELIYA/3 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3514, 392, 'ns=2;s=SURELIYA.3 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/SURELIYA/3 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3515, 392, 'ns=2;s=SURELIYA.3 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/SURELIYA/3 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3516, 392, 'ns=2;s=SURELIYA.3 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/SURELIYA/3 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3517, 392, 'ns=2;s=SURELIYA.3 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/SURELIYA/3 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3518, 392, 'ns=2;s=SURELIYA.3 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/SURELIYA/3 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3519, 392, 'ns=2;s=SURELIYA.3 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/SURELIYA/3 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3520, 414, 'ns=2;s=TRIKAMLAL.3 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/TRIKAMLAL/3 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3521, 414, 'ns=2;s=TRIKAMLAL.3 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/TRIKAMLAL/3 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3522, 414, 'ns=2;s=TRIKAMLAL.3 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/TRIKAMLAL/3 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3523, 414, 'ns=2;s=TRIKAMLAL.3 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/TRIKAMLAL/3 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3524, 414, 'ns=2;s=TRIKAMLAL.3 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/TRIKAMLAL/3 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3525, 414, 'ns=2;s=TRIKAMLAL.3 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/TRIKAMLAL/3 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3526, 414, 'ns=2;s=TRIKAMLAL.3 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/TRIKAMLAL/3 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3527, 425, 'ns=2;s=TRIKAMPURA.4 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/TRIKAMPURA/4 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3528, 425, 'ns=2;s=TRIKAMPURA.4 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/TRIKAMPURA/4 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3529, 425, 'ns=2;s=TRIKAMPURA.4 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/TRIKAMPURA/4 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3530, 425, 'ns=2;s=TRIKAMPURA.4 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/TRIKAMPURA/4 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3531, 401, 'ns=2;s=VASTRAL.4 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/VASTRAL/4 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3532, 401, 'ns=2;s=VASTRAL.4 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/VASTRAL/4 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3533, 401, 'ns=2;s=VASTRAL.4 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/VASTRAL/4 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3534, 401, 'ns=2;s=VASTRAL.4 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/VASTRAL/4 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3535, 401, 'ns=2;s=VASTRAL.4 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/VASTRAL/4 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3536, 401, 'ns=2;s=VASTRAL.4 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/VASTRAL/4 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3537, 401, 'ns=2;s=VASTRAL.4 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/VASTRAL/4 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3538, 401, 'ns=2;s=VASTRAL.4 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/VASTRAL/4 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3539, 426, 'ns=2;s=VEJALPUR.8 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/VEJALPUR/8 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3540, 426, 'ns=2;s=VEJALPUR.8 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3541, 426, 'ns=2;s=VEJALPUR.8 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3542, 426, 'ns=2;s=VEJALPUR.8 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3543, 426, 'ns=2;s=VEJALPUR.8 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3544, 426, 'ns=2;s=VEJALPUR.8 PUMP.PUMP_5_RFB', 'AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_5_RFB', 'PUMP_5_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3545, 426, 'ns=2;s=VEJALPUR.8 PUMP.PUMP_6_RFB', 'AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_6_RFB', 'PUMP_6_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3546, 426, 'ns=2;s=VEJALPUR.8 PUMP.PUMP_7_RFB', 'AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_7_RFB', 'PUMP_7_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3547, 426, 'ns=2;s=VEJALPUR.8 PUMP.PUMP_8_RFB', 'AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_8_RFB', 'PUMP_8_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3548, 426, 'ns=2;s=VEJALPUR.8 PUMP.PUMP_9_RFB', 'AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_9_RFB', 'PUMP_9_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3549, 426, 'ns=2;s=VEJALPUR.8 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/VEJALPUR/8 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3550, 426, 'ns=2;s=VEJALPUR.8 PUMP.SEWAGE_CURRENT_DAY_2', 'AMC/Storm/amcstorm/VEJALPUR/8 PUMP/SEWAGE_CURRENT_DAY_2', 'SEWAGE_CURRENT_DAY_2', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3551, 426, 'ns=2;s=VEJALPUR.8 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/VEJALPUR/8 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3552, 426, 'ns=2;s=VEJALPUR.8 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/VEJALPUR/8 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3553, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3554, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.PREVEOUS DAY TOTALIZER', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PREVEOUS DAY TOTALIZER', 'PREVEOUS DAY TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3555, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3556, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_10_RFB', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_10_RFB', 'PUMP_10_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3557, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3558, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3559, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3560, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_5_RFB', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_5_RFB', 'PUMP_5_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3561, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_6_RFB', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_6_RFB', 'PUMP_6_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3562, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_7_RFB', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_7_RFB', 'PUMP_7_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3563, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_8_RFB', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_8_RFB', 'PUMP_8_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3564, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_9_RFB', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_9_RFB', 'PUMP_9_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3565, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3566, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.SEWAGE_CURRENT_DAY_2', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/SEWAGE_CURRENT_DAY_2', 'SEWAGE_CURRENT_DAY_2', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3567, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.SEWAGE_CURRENT_DAY_3', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/SEWAGE_CURRENT_DAY_3', 'SEWAGE_CURRENT_DAY_3', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3568, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.SEWAGE_CURRENT_DAY_4', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/SEWAGE_CURRENT_DAY_4', 'SEWAGE_CURRENT_DAY_4', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3569, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.SEWAGE_TOTALIZER_1', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/SEWAGE_TOTALIZER_1', 'SEWAGE_TOTALIZER_1', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3570, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.SEWAGE_TOTALIZER_2', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/SEWAGE_TOTALIZER_2', 'SEWAGE_TOTALIZER_2', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3571, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.SEWAGE_TOTALIZER_3', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/SEWAGE_TOTALIZER_3', 'SEWAGE_TOTALIZER_3', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3572, 427, 'ns=2;s=VINZOAL TERMINAL.6 PUMP.SEWAGE_TOTALIZER_4', 'AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/SEWAGE_TOTALIZER_4', 'SEWAGE_TOTALIZER_4', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3573, 402, 'ns=2;s=VIRATNAGAR.8 PUMP.HT_RFB', 'AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/HT_RFB', 'HT_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 0, '2025-05-29 09:49:21', '2025-06-13 15:26:19'),
(3574, 402, 'ns=2;s=VIRATNAGAR.8 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3575, 402, 'ns=2;s=VIRATNAGAR.8 PUMP.LT_1_RFB', 'AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/LT_1_RFB', 'LT_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 0, '2025-05-29 09:49:21', '2025-06-13 15:28:18'),
(3576, 402, 'ns=2;s=VIRATNAGAR.8 PUMP.LT_2_RFB', 'AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/LT_2_RFB', 'LT_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 0, '2025-05-29 09:49:21', '2025-06-13 15:28:38'),
(3577, 402, 'ns=2;s=VIRATNAGAR.8 PUMP.LT_3_RFB', 'AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/LT_3_RFB', 'LT_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 0, '2025-05-29 09:49:21', '2025-06-13 15:28:44'),
(3578, 402, 'ns=2;s=VIRATNAGAR.8 PUMP.PREVEOUS DAY TOTALIZER', 'AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PREVEOUS DAY TOTALIZER', 'PREVEOUS DAY TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3579, 402, 'ns=2;s=VIRATNAGAR.8 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3580, 402, 'ns=2;s=VIRATNAGAR.8 PUMP.PUMP_2_RFB', 'AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PUMP_2_RFB', 'PUMP_2_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3581, 402, 'ns=2;s=VIRATNAGAR.8 PUMP.PUMP_3_RFB', 'AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PUMP_3_RFB', 'PUMP_3_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3582, 402, 'ns=2;s=VIRATNAGAR.8 PUMP.PUMP_4_RFB', 'AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PUMP_4_RFB', 'PUMP_4_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3583, 402, 'ns=2;s=VIRATNAGAR.8 PUMP.PUMP_5_RFB', 'AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PUMP_5_RFB', 'PUMP_5_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3584, 402, 'ns=2;s=VIRATNAGAR.8 PUMP.PUMP_6_RFB', 'AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PUMP_6_RFB', 'PUMP_6_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3585, 402, 'ns=2;s=VIRATNAGAR.8 PUMP.PUMP_7_RFB', 'AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PUMP_7_RFB', 'PUMP_7_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3586, 402, 'ns=2;s=VIRATNAGAR.8 PUMP.PUMP_8_RFB', 'AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PUMP_8_RFB', 'PUMP_8_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3587, 402, 'ns=2;s=VIRATNAGAR.8 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3588, 402, 'ns=2;s=VIRATNAGAR.8 PUMP.SEWAGE_CURRENT_DAY_2', 'AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/SEWAGE_CURRENT_DAY_2', 'SEWAGE_CURRENT_DAY_2', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3589, 402, 'ns=2;s=VIRATNAGAR.8 PUMP.SEWAGE_TOTALIZER_1', 'AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/SEWAGE_TOTALIZER_1', 'SEWAGE_TOTALIZER_1', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3590, 385, 'ns=2;s=VYAYAMSHALA.1 PUMP.LEVEL_1', 'AMC/Storm/amcstorm/VYAYAMSHALA/1 PUMP/LEVEL_1', 'LEVEL_1', 'LEVEL', 'm', 0.0000, 10.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3591, 385, 'ns=2;s=VYAYAMSHALA.1 PUMP.PUMP_1_RFB', 'AMC/Storm/amcstorm/VYAYAMSHALA/1 PUMP/PUMP_1_RFB', 'PUMP_1_RFB', 'PUMP_STATUS', NULL, 0.0000, 1.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3592, 385, 'ns=2;s=VYAYAMSHALA.1 PUMP.SEWAGE_CURRENT_DAY_1', 'AMC/Storm/amcstorm/VYAYAMSHALA/1 PUMP/SEWAGE_CURRENT_DAY_1', 'SEWAGE_CURRENT_DAY_1', 'FLOW_RATE', 'MLD', 0.0000, 1000.0000, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3593, 385, 'ns=2;s=VYAYAMSHALA.1 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER', 'AMC/Storm/amcstorm/VYAYAMSHALA/1 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER', 'SEWAGE_PREVIOUS_DAY_TOTALIZER', 'TOTALIZER', 'MLD', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21'),
(3594, 385, 'ns=2;s=VYAYAMSHALA.1 PUMP.SEWAGE_TOTALIZER', 'AMC/Storm/amcstorm/VYAYAMSHALA/1 PUMP/SEWAGE_TOTALIZER', 'SEWAGE_TOTALIZER', 'TOTALIZER', 'ML', 0.0000, NULL, 1, '2025-05-29 09:49:21', '2025-05-29 09:49:21');

-- --------------------------------------------------------

--
-- Table structure for table `tag_data`
--

CREATE TABLE `tag_data` (
  `id` int(11) NOT NULL,
  `tag_id` int(11) NOT NULL,
  `value` decimal(15,6) DEFAULT NULL,
  `timestamp` bigint(20) NOT NULL,
  `received_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_sessions`
--

CREATE TABLE `user_sessions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `token_hash` varchar(255) NOT NULL,
  `device_info` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `expires_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `user_sessions`
--

INSERT INTO `user_sessions` (`id`, `user_id`, `token_hash`, `device_info`, `ip_address`, `expires_at`, `created_at`) VALUES
(69, 5, 'fe1896a9975556bbddf88341d65a243b597cac553d3dd2209059872cabb9e9b5', 'Flutter Mobile App', '::ffff:**************', '2025-06-26 11:32:05', '2025-06-19 11:32:05'),
(70, 5, 'd1febfd29cfeacecdac2c1ebff550123dc30d277bc63f44537c9ec4d0a9e14ed', 'Flutter Mobile App', '::ffff:************', '2025-06-26 14:00:20', '2025-06-19 14:00:21');

-- --------------------------------------------------------

--
-- Table structure for table `zones`
--

CREATE TABLE `zones` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `zone_type` enum('regular','tsps','critical') NOT NULL DEFAULT 'regular',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `zones`
--

INSERT INTO `zones` (`id`, `name`, `description`, `zone_type`, `created_at`, `updated_at`) VALUES
(1, 'North Zone', 'Northern area pumping stations', 'regular', '2025-05-28 03:48:43', '2025-06-05 07:29:20'),
(2, 'South Zone', 'Southern area pumping stations', 'regular', '2025-05-28 03:48:43', '2025-06-05 07:29:24'),
(3, 'East Zone', 'Eastern area pumping stations', 'regular', '2025-05-28 03:48:43', '2025-06-05 07:29:15'),
(4, 'West Zone', 'Western area pumping stations', 'regular', '2025-05-28 03:48:43', '2025-06-05 07:29:39'),
(5, 'South West Zone', 'South Western area pumping stations', 'regular', '2025-05-28 03:48:43', '2025-06-05 07:29:33');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_users`
--
ALTER TABLE `admin_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`);

--
-- Indexes for table `app_users`
--
ALTER TABLE `app_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`);

--
-- Indexes for table `locations`
--
ALTER TABLE `locations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_location_per_zone` (`zone_id`,`name`);

--
-- Indexes for table `tags`
--
ALTER TABLE `tags`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `opc_address` (`opc_address`),
  ADD KEY `location_id` (`location_id`);

--
-- Indexes for table `tag_data`
--
ALTER TABLE `tag_data`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_tag_timestamp` (`tag_id`,`timestamp`),
  ADD KEY `idx_received_at` (`received_at`);

--
-- Indexes for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_token_hash` (`token_hash`),
  ADD KEY `idx_expires_at` (`expires_at`);

--
-- Indexes for table `zones`
--
ALTER TABLE `zones`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_users`
--
ALTER TABLE `admin_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `app_users`
--
ALTER TABLE `app_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `locations`
--
ALTER TABLE `locations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=429;

--
-- AUTO_INCREMENT for table `tags`
--
ALTER TABLE `tags`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3595;

--
-- AUTO_INCREMENT for table `tag_data`
--
ALTER TABLE `tag_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=409682;

--
-- AUTO_INCREMENT for table `user_sessions`
--
ALTER TABLE `user_sessions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=75;

--
-- AUTO_INCREMENT for table `zones`
--
ALTER TABLE `zones`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=60;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `locations`
--
ALTER TABLE `locations`
  ADD CONSTRAINT `locations_ibfk_1` FOREIGN KEY (`zone_id`) REFERENCES `zones` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `tags`
--
ALTER TABLE `tags`
  ADD CONSTRAINT `tags_ibfk_1` FOREIGN KEY (`location_id`) REFERENCES `locations` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `tag_data`
--
ALTER TABLE `tag_data`
  ADD CONSTRAINT `tag_data_ibfk_1` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `app_users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
