import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import db from '../config/database.js';

// JWT secret key (should be in environment variables in production)
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// Helper function to hash session token
const hashToken = (token) => {
  return crypto.createHash('sha256').update(token).digest('hex');
};

// Middleware to authenticate mobile app users
export const authenticateAppUser = async (req, res, next) => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    const sessionToken = req.headers['x-session-token'];

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Access token is required'
      });
    }

    if (!sessionToken) {
      return res.status(401).json({
        success: false,
        error: 'Session token is required'
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify JWT token
    let decoded;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired access token'
      });
    }

    // Verify session token
    const sessionTokenHash = hashToken(sessionToken);
    const [sessions] = await db.execute(
      `SELECT s.*, u.username, u.email, u.full_name, u.role, u.is_active 
       FROM user_sessions s 
       JOIN app_users u ON s.user_id = u.id 
       WHERE s.token_hash = ? AND s.expires_at > NOW() AND u.is_active = TRUE`,
      [sessionTokenHash]
    );

    if (sessions.length === 0) {
      return res.status(401).json({
        success: false,
        error: 'Invalid or expired session'
      });
    }

    const session = sessions[0];

    // Verify user ID matches
    if (session.user_id !== decoded.userId) {
      return res.status(401).json({
        success: false,
        error: 'Token mismatch'
      });
    }

    // Add user information to request object
    req.user = {
      id: session.user_id,
      username: session.username,
      email: session.email,
      fullName: session.full_name,
      role: session.role
    };

    next();

  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
};

// Middleware to check user roles
export const requireRole = (allowedRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Insufficient permissions'
      });
    }

    next();
  };
};

// Middleware for optional authentication (doesn't fail if no token)
export const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const sessionToken = req.headers['x-session-token'];

    if (!authHeader || !sessionToken) {
      return next(); // Continue without authentication
    }

    if (!authHeader.startsWith('Bearer ')) {
      return next(); // Continue without authentication
    }

    const token = authHeader.substring(7);

    // Verify JWT token
    let decoded;
    try {
      decoded = jwt.verify(token, JWT_SECRET);
    } catch (jwtError) {
      return next(); // Continue without authentication
    }

    // Verify session token
    const sessionTokenHash = hashToken(sessionToken);
    const [sessions] = await db.execute(
      `SELECT s.*, u.username, u.email, u.full_name, u.role, u.is_active 
       FROM user_sessions s 
       JOIN app_users u ON s.user_id = u.id 
       WHERE s.token_hash = ? AND s.expires_at > NOW() AND u.is_active = TRUE`,
      [sessionTokenHash]
    );

    if (sessions.length > 0 && sessions[0].user_id === decoded.userId) {
      const session = sessions[0];
      req.user = {
        id: session.user_id,
        username: session.username,
        email: session.email,
        fullName: session.full_name,
        role: session.role
      };
    }

    next();

  } catch (error) {
    console.error('Optional authentication error:', error);
    next(); // Continue without authentication on error
  }
};

// Clean up expired sessions (utility function)
export const cleanupExpiredSessions = async () => {
  try {
    const [result] = await db.execute(
      'DELETE FROM user_sessions WHERE expires_at < NOW()'
    );
    
    if (result.affectedRows > 0) {
      console.log(`Cleaned up ${result.affectedRows} expired sessions`);
    }
  } catch (error) {
    console.error('Error cleaning up expired sessions:', error);
  }
};

// Schedule cleanup every hour
setInterval(cleanupExpiredSessions, 60 * 60 * 1000); // 1 hour
