import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../models/zone.dart';
import '../models/user.dart';
import '../providers/data_provider.dart';
import '../providers/auth_provider.dart';

import '../utils/app_theme.dart';
import '../widgets/dashboard_card.dart';
import '../widgets/zone_card.dart';

import '../widgets/background_refresh_indicator.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    // Initial data load
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final dataProvider = Provider.of<DataProvider>(context, listen: false);
      // Use regular refresh for initial load, then background refresh for subsequent updates
      if (dataProvider.zones.isEmpty) {
        dataProvider.refreshAll();
      } else {
        dataProvider.refreshAllBackground();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(85),
        child: Container(
          decoration: BoxDecoration(
            gradient: AppTheme.primaryLinearGradient,
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryColor.withOpacity(0.3),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.menu, color: Colors.white, size: 26),
                    onPressed: () => _scaffoldKey.currentState?.openDrawer(),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Text(
                          'PUMPING STATIONS DATA',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            letterSpacing: 0.5,
                            height: 1.1,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            const Icon(Icons.calendar_today, color: Colors.white70, size: 14),
                            const SizedBox(width: 4),
                            Text(
                              _formatDate(DateTime.now()),
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.white70,
                                fontWeight: FontWeight.w500,
                                height: 1.0,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Consumer<DataProvider>(
                    builder: (context, dataProvider, child) {
                      return Container(
                        margin: const EdgeInsets.only(right: 12),
                        child: IconButton(
                          icon: dataProvider.isBackgroundRefreshing
                              ? const SizedBox(
                                  width: 22,
                                  height: 22,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2.5,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Icon(Icons.refresh, color: Colors.white, size: 26),
                          onPressed: dataProvider.isBackgroundRefreshing
                              ? null
                              : () {
                                  dataProvider.refreshAllBackground();
                                },
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      drawer: _buildNavigationDrawer(context),
      backgroundColor: AppTheme.backgroundColor,
      body: const BackgroundRefreshIndicator(
        child: StormWaterDataScreen(),
      ),
    );
  }

  Widget _buildNavigationDrawer(BuildContext context) {
    return Drawer(
      child: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFF8FAFC),
              Color(0xFFE2E8F0),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Enhanced User Profile Header - Made responsive
              Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  final user = authProvider.user;
                  return Container(
                    width: double.infinity,
                    constraints: const BoxConstraints(
                      minHeight: 140,
                      maxHeight: 180,
                    ),
                    decoration: BoxDecoration(
                      gradient: AppTheme.primaryLinearGradient,
                      boxShadow: [
                        BoxShadow(
                          color: AppTheme.primaryColor.withOpacity(0.3),
                          blurRadius: 15,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Profile Avatar - Made smaller for better space usage
                          Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 8,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                            ),
                            child: ClipOval(
                              child: Image.asset(
                                'assets/images/logo100px.png',
                                width: 60,
                                height: 60,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  // Fallback to icon if image fails to load
                                  return const Icon(
                                    Icons.person,
                                    color: AppTheme.primaryColor,
                                    size: 30,
                                  );
                                },
                              ),
                            ),
                          ),
                          const SizedBox(height: 12),

                          // User Info - Reduced spacing
                          Text(
                            user?.username ?? 'AMC User',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: const Text(
                              'Monsoon Critical',
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),

              // Zone Navigation - Made more flexible
              Expanded(
                child: Consumer<DataProvider>(
                  builder: (context, dataProvider, child) {
                    if (dataProvider.isLoading) {
                      return const Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                        ),
                      );
                    }

                    // Sort zones by type and name (special zones first)
                    final sortedZones = List<Zone>.from(dataProvider.zones)
                      ..sort((a, b) {
                        // First sort by type priority
                        int getTypePriority(String type) {
                          switch (type) {
                            case 'critical': return 0;
                            case 'tsps': return 1;
                            case 'regular': return 2;
                            default: return 3;
                          }
                        }
                        
                        final priorityA = getTypePriority(a.zoneType);
                        final priorityB = getTypePriority(b.zoneType);
                        
                        if (priorityA != priorityB) {
                          return priorityA.compareTo(priorityB);
                        }
                        
                        // Then sort by name within same type
                        return a.name.compareTo(b.name);
                      });

                    return Column(
                      children: [
                        // Zones List - Takes most of the space
                        Expanded(
                          child: ListView(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            children: [
                              // Zones Header
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 6),
                                child: Text(
                                  'Locations',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey[600],
                                    letterSpacing: 1.2,
                                  ),
                                ),
                              ),

                              // Zone List - Compact design
                              ...sortedZones.map((zone) => Container(
                                margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 1),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10),
                                  color: Colors.white.withOpacity(0.7),
                                ),
                                child: ListTile(
                                  dense: true,
                                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
                                  leading: Container(
                                    padding: const EdgeInsets.all(6),
                                    decoration: BoxDecoration(
                                      color: _getZoneTypeColor(zone.zoneType).withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    child: Icon(
                                      _getZoneTypeIcon(zone.zoneType),
                                      color: _getZoneTypeColor(zone.zoneType),
                                      size: 18,
                                    ),
                                  ),
                                  title: Text(
                                    zone.name,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w600,
                                      color: AppTheme.primaryTextColor,
                                      fontSize: 14,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  subtitle: Text(
                                    '${zone.locationCount} locations',
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  trailing: const Icon(
                                    Icons.arrow_forward_ios,
                                    size: 14,
                                    color: AppTheme.primaryColor,
                                  ),
                                  onTap: () {
                                    Navigator.pop(context);
                                    context.go('/zone/${zone.id}');
                                  },
                                ),
                              )),
                            ],
                          ),
                        ),

                        // Logout Section - Fixed at bottom with safe spacing
                        Container(
                          padding: const EdgeInsets.fromLTRB(12, 8, 12, 16),
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: AppTheme.errorColor.withOpacity(0.1),
                            ),
                            child: ListTile(
                              dense: true,
                              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
                              leading: Container(
                                padding: const EdgeInsets.all(6),
                                decoration: BoxDecoration(
                                  color: AppTheme.errorColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: const Icon(
                                  Icons.logout,
                                  color: AppTheme.errorColor,
                                  size: 18,
                                ),
                              ),
                              title: const Text(
                                'Log Out',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: AppTheme.errorColor,
                                  fontSize: 14,
                                ),
                              ),
                              onTap: () {
                                Navigator.pop(context);
                                _showLogoutDialog(context);
                              },
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Logout'),
          content: const Text('Are you sure you want to logout?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                Provider.of<AuthProvider>(context, listen: false).logout();
              },
              child: const Text('Logout'),
            ),
          ],
        );
      },
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  // Helper method to get icon based on zone type
  IconData _getZoneTypeIcon(String zoneType) {
    switch (zoneType) {
      case 'critical':
        return Icons.warning_amber_rounded;
      case 'tsps':
        return Icons.water;
      default:
        return Icons.location_city;
    }
  }

  // Helper method to get color based on zone type
  Color _getZoneTypeColor(String zoneType) {
    switch (zoneType) {
      case 'critical':
        return Colors.red;
      case 'tsps':
        return Colors.orange;
      default:
        return AppTheme.primaryColor;
    }
  }
}

// Storm Water Data Screen - Main content matching the requirements
class StormWaterDataScreen extends StatelessWidget {
  const StormWaterDataScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Consumer<DataProvider>(
        builder: (context, dataProvider, child) {
          // Show loading only on initial load, not during background refresh
          if (dataProvider.isLoading && dataProvider.zones.isEmpty) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(50),
                child: CircularProgressIndicator(),
              ),
            );
          }

          if (dataProvider.errorMessage != null && dataProvider.zones.isEmpty) {
            return _buildErrorWidget(dataProvider.errorMessage!, dataProvider);
          }

          // Get real zone data from backend
          final zoneDataList = dataProvider.zoneData;
          final Map<String, dynamic>? response = dataProvider.zoneDataResponse;

          if (response == null) {
            return const Center(child: CircularProgressIndicator());
          }

          // Filter for regular zones first, then sort
          final regularZones = zoneDataList
              .where((zone) => zone.zoneType == 'regular')
              .toList()
            ..sort((a, b) => a.zoneName.compareTo(b.zoneName));

          return Column(
            children: [
              // Critical Total Section
              if (response['totals']['critical'] != null && 
                  (response['totals']['critical']['location_count'] ?? 0) > 0)
                _buildTotalZoneCard(
                  context,
                  ZoneData(
                    zoneId: -1,
                    zoneName: 'Total of Critical Pumping Stations',
                    zoneType: 'critical',
                    totalCurrentDayQty: _parseConcatenatedValue(response['totals']['critical']['total_current_day_qty']),
                    totalPreviousDayQty: _parseConcatenatedValue(response['totals']['critical']['total_previous_day_qty']),
                    totalTillTodayQty: _parseConcatenatedValue(response['totals']['critical']['total_till_today_qty']),
                    lastUpdated: DateTime.now(),
                    locationCount: response['totals']['critical']['location_count'],
                  ),
                ),

              const SizedBox(height: 16),

              // TSPS Total Section
              if (response['totals']['tsps'] != null && 
                  (response['totals']['tsps']['location_count'] ?? 0) > 0)
                _buildTotalZoneCard(
                  context,
                  ZoneData(
                    zoneId: -2,
                    zoneName: 'Total of Terminal Pumping Stations',
                    zoneType: 'tsps',
                    totalCurrentDayQty: _parseConcatenatedValue(response['totals']['tsps']['total_current_day_qty']),
                    totalPreviousDayQty: _parseConcatenatedValue(response['totals']['tsps']['total_previous_day_qty']),
                    totalTillTodayQty: _parseConcatenatedValue(response['totals']['tsps']['total_till_today_qty']),
                    lastUpdated: DateTime.now(),
                    locationCount: response['totals']['tsps']['location_count'],
                  ),
                ),

              const SizedBox(height: 16),

              // SWPS Total Section
              if (response['totals']['swps'] != null && 
                  (response['totals']['swps']['location_count'] ?? 0) > 0)
                _buildTotalZoneCard(
                  context,
                  ZoneData(
                    zoneId: -3,
                    zoneName: 'Total of Storm Water Pumping Stations',
                    zoneType: 'swps',
                    totalCurrentDayQty: _parseConcatenatedValue(response['totals']['swps']['total_current_day_qty']),
                    totalPreviousDayQty: _parseConcatenatedValue(response['totals']['swps']['total_previous_day_qty']),
                    totalTillTodayQty: _parseConcatenatedValue(response['totals']['swps']['total_till_today_qty']),
                    lastUpdated: DateTime.now(),
                    locationCount: response['totals']['swps']['location_count'],
                  ),
                ),

              const SizedBox(height: 16),

              // Individual Zone Cards (only regular zones)
              ...regularZones.map((zoneData) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: _buildZoneDataCard(context, zoneData),
              )),
            ],
          );
        },
      ),
    );
  }

  Widget _buildErrorWidget(String errorMessage, DataProvider dataProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              color: AppTheme.errorColor,
              size: 48,
            ),
            const SizedBox(height: 8),
            const Text(
              'Error loading data',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              errorMessage,
              style: const TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => dataProvider.refreshAll(),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalZoneCard(BuildContext context, ZoneData totalData) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: AppTheme.primaryLinearGradient,
        boxShadow: AppTheme.elevatedShadow,
      ),
      child: InkWell(
        onTap: () {
          // Navigate to appropriate zone detail screen based on zone type
          String zoneId;
          switch (totalData.zoneType) {
            case 'critical':
              zoneId = 'CSPS';
              break;
            case 'tsps':
              zoneId = 'TSPS';
              break;
            case 'regular':
              // For regular zones total, show all SWPS locations
              zoneId = 'SWPS';
              break;
            default:
              zoneId = 'SWPS';
          }
          context.go('/zone/$zoneId');
        },
        borderRadius: BorderRadius.circular(20),
        child: Column(
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.analytics,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      totalData.zoneName.toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                  const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white70,
                    size: 16,
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
              ),
              child: Column(
                children: [
                  _buildEnhancedDataRow('Total Current Day Qty', totalData.formattedCurrentDayQty, Icons.today, AppTheme.primaryColor),
                  const SizedBox(height: 16),
                  _buildEnhancedDataRow('Total Previous Day Qty', totalData.formattedPreviousDayQty, Icons.history, AppTheme.accentColor),
                  if (totalData.zoneType != 'critical' && totalData.zoneType != 'tsps') ...[
                    const SizedBox(height: 16),
                    _buildEnhancedDataRow('Total Till Today Qty', totalData.formattedTillTodayQty, Icons.analytics, AppTheme.successColor),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildZoneDataCard(BuildContext context, ZoneData zoneData) {
    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.white,
        boxShadow: AppTheme.cardShadow,
      ),
      child: InkWell(
        onTap: () {
          // Navigate to zone detail screen
          context.go('/zone/${zoneData.zoneId}');
        },
        borderRadius: BorderRadius.circular(16),
        child: Column(
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.accentColor.withOpacity(0.8),
                    AppTheme.accentColor.withOpacity(0.6),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.location_city,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      zoneData.zoneName.toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                  const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white70,
                    size: 16,
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildEnhancedDataRow('CSPS', zoneData.formattedCspsCurrentDayQty, Icons.warning_amber_rounded, AppTheme.primaryColor),
                  const SizedBox(height: 12),
                  _buildEnhancedDataRow('SWPS', zoneData.formattedSwpsCurrentDayQty, Icons.waves, AppTheme.accentColor),
                  const SizedBox(height: 12),
                  _buildEnhancedDataRow('TSPS', zoneData.formattedTspsCurrentDayQty, Icons.water, AppTheme.successColor),
                  const SizedBox(height: 16),
                  Consumer<DataProvider>(
                    builder: (context, dataProvider, child) {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          LastUpdateIndicator(lastUpdate: dataProvider.lastUpdate),
                          SubtleRefreshIndicator(isRefreshing: dataProvider.isBackgroundRefreshing),
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedDataRow(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: AppTheme.secondaryTextColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: AppTheme.secondaryTextColor,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryTextColor,
          ),
        ),
      ],
    );
  }

  // Helper method to safely parse concatenated values
  double _parseConcatenatedValue(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    
    String strValue = value.toString();
    // Try to parse the first number in the concatenated string
    try {
      // Find the first number in the string
      final match = RegExp(r'^\d+\.?\d*').firstMatch(strValue);
      if (match != null) {
        return double.parse(match.group(0)!);
      }
    } catch (e) {
      print('Error parsing value: $strValue');
    }
    return 0.0;
  }
}


