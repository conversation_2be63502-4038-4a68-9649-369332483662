class Zone {
  final dynamic id; // Can be int or String for special zones
  final String name;
  final String? description;
  final String zoneType;
  final int locationCount;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Zone({
    required this.id,
    required this.name,
    this.description,
    required this.zoneType,
    required this.locationCount,
    this.createdAt,
    this.updatedAt,
  });

  factory Zone.fromJson(Map<String, dynamic> json) {
    return Zone(
      id: json['id'], // Can be int or String
      name: json['name'],
      description: json['description'],
      zoneType: json['zone_type'] ?? 'regular',
      locationCount: json['location_count'] ?? 0,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'zone_type': zoneType,
      'location_count': locationCount,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}

// Zone Data Model for aggregated water data
class ZoneData {
  final dynamic zoneId; // Can be int or String for special zones
  final String zoneName;
  final String zoneType;
  final double totalCurrentDayQty; // MLD
  final double totalPreviousDayQty; // MLD
  final double totalTillTodayQty; // ML
  final DateTime lastUpdated;
  final int locationCount;
  
  // Current day quantities by location type
  final double cspsCurrentDayQty; // MLD
  final double swpsCurrentDayQty; // MLD
  final double tspsCurrentDayQty; // MLD

  ZoneData({
    required this.zoneId,
    required this.zoneName,
    required this.zoneType,
    required this.totalCurrentDayQty,
    required this.totalPreviousDayQty,
    required this.totalTillTodayQty,
    required this.lastUpdated,
    required this.locationCount,
    this.cspsCurrentDayQty = 0.0,
    this.swpsCurrentDayQty = 0.0,
    this.tspsCurrentDayQty = 0.0,
  });

  factory ZoneData.fromJson(Map<String, dynamic> json) {
    return ZoneData(
      zoneId: json['zone_id'], // Can be int or String
      zoneName: json['zone_name'] as String,
      zoneType: json['zone_type'] as String? ?? 'regular',
      totalCurrentDayQty: _parseDouble(json['total_current_day_qty']),
      totalPreviousDayQty: _parseDouble(json['total_previous_day_qty']),
      totalTillTodayQty: _parseDouble(json['total_till_today_qty']),
      lastUpdated: DateTime.fromMillisecondsSinceEpoch(
        (json['last_updated'] as int) * 1000,
      ),
      locationCount: json['location_count'] as int,
      cspsCurrentDayQty: _parseDouble(json['csps_current_day_qty']),
      swpsCurrentDayQty: _parseDouble(json['swps_current_day_qty']),
      tspsCurrentDayQty: _parseDouble(json['tsps_current_day_qty']),
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'zone_id': zoneId,
      'zone_name': zoneName,
      'zone_type': zoneType,
      'total_current_day_qty': totalCurrentDayQty,
      'total_previous_day_qty': totalPreviousDayQty,
      'total_till_today_qty': totalTillTodayQty,
      'last_updated': lastUpdated.toIso8601String(),
      'location_count': locationCount,
    };
  }

  String get formattedCurrentDayQty => '${totalCurrentDayQty.toStringAsFixed(2)} MLD';
  String get formattedPreviousDayQty => '${totalPreviousDayQty.toStringAsFixed(2)} MLD';
  String get formattedTillTodayQty => '${totalTillTodayQty.toStringAsFixed(2)} ML';
  
  // Formatted location type quantities
  String get formattedCspsCurrentDayQty => '${cspsCurrentDayQty.toStringAsFixed(2)} MLD';
  String get formattedSwpsCurrentDayQty => '${swpsCurrentDayQty.toStringAsFixed(2)} MLD';
  String get formattedTspsCurrentDayQty => '${tspsCurrentDayQty.toStringAsFixed(2)} MLD';
}

// Zone Totals Model for aggregated totals by zone type
class ZoneTotals {
  final double totalCurrentDayQty;
  final double totalPreviousDayQty;
  final double totalTillTodayQty;
  final int locationCount;

  ZoneTotals({
    required this.totalCurrentDayQty,
    required this.totalPreviousDayQty,
    required this.totalTillTodayQty,
    required this.locationCount,
  });

  factory ZoneTotals.zero() {
    return ZoneTotals(
      totalCurrentDayQty: 0.0,
      totalPreviousDayQty: 0.0,
      totalTillTodayQty: 0.0,
      locationCount: 0,
    );
  }

  factory ZoneTotals.fromJson(Map<String, dynamic> json) {
    return ZoneTotals(
      totalCurrentDayQty: (json['total_current_day_qty'] ?? 0).toDouble(),
      totalPreviousDayQty: (json['total_previous_day_qty'] ?? 0).toDouble(),
      totalTillTodayQty: (json['total_till_today_qty'] ?? 0).toDouble(),
      locationCount: json['location_count'] ?? 0,
    );
  }
}

// Zone Data Response Model
class ZoneDataResponse {
  final List<ZoneData> zones;
  final Map<String, ZoneTotals> totals;

  ZoneDataResponse({
    required this.zones,
    required this.totals,
  });

  factory ZoneDataResponse.fromJson(Map<String, dynamic> json) {
    return ZoneDataResponse(
      zones: (json['zones'] as List)
          .map((zone) => ZoneData.fromJson(zone))
          .toList(),
      totals: {
        'regular': ZoneTotals.fromJson(json['totals']['regular']),
        'tsps': ZoneTotals.fromJson(json['totals']['tsps']),
        'critical': ZoneTotals.fromJson(json['totals']['critical']),
        'swps': json['totals']['swps'] != null ? ZoneTotals.fromJson(json['totals']['swps']) : ZoneTotals.zero(),
      },
    );
  }
}

// Location Type Enum
enum LocationType {
  csps('CSPS', 'Combined Sewer Pumping Station'),
  swps('SWPS', 'Storm Water Pumping Station'),
  tsps('TSPS', 'Treatment/Transfer Sewage Pumping Station');

  const LocationType(this.code, this.description);
  final String code;
  final String description;

  static LocationType fromString(String value) {
    switch (value.toUpperCase()) {
      case 'CSPS':
        return LocationType.csps;
      case 'SWPS':
        return LocationType.swps;
      case 'TSPS':
        return LocationType.tsps;
      default:
        return LocationType.swps; // Default to SWPS
    }
  }

  @override
  String toString() => code;
}

class Location {
  final int id;
  final int zoneId;
  final String name;
  final String? description;
  final int pumpCount;
  final LocationType locationType;
  final int tagCount;
  final String? zoneName;
  final DateTime createdAt;
  final DateTime updatedAt;

  Location({
    required this.id,
    required this.zoneId,
    required this.name,
    this.description,
    required this.pumpCount,
    required this.locationType,
    required this.tagCount,
    this.zoneName,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      id: json['id'],
      zoneId: json['zone_id'],
      name: json['name'],
      description: json['description'],
      pumpCount: json['pump_count'] ?? 0,
      locationType: LocationType.fromString(json['location_type'] ?? 'SWPS'),
      tagCount: json['tag_count'] ?? 0,
      zoneName: json['zone_name'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'zone_id': zoneId,
      'name': name,
      'description': description,
      'pump_count': pumpCount,
      'location_type': locationType.toString(),
      'tag_count': tagCount,
      'zone_name': zoneName,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

// Location Data Model for detailed location information
class LocationData {
  final int locationId;
  final String locationName;
  final int zoneId;
  final String? zoneName;
  final LocationType locationType;
  final double currentDayQty; // MLD
  final double previousDayQty; // MLD
  final double tillTodayQty; // ML
  final double level; // Meters
  final double flowRate; // m3/Hr
  final List<PumpStatus> pumpStatuses;
  final DateTime lastUpdated;
  final String? operatorName;
  final String? operatorNumber;
  final String? supervisorNumber;
  final String? torrentService1;
  final String? supervisorName;
  final String? torrentContact;

  LocationData({
    required this.locationId,
    required this.locationName,
    required this.zoneId,
    this.zoneName,
    required this.locationType,
    required this.currentDayQty,
    required this.previousDayQty,
    required this.tillTodayQty,
    required this.level,
    required this.flowRate,
    required this.pumpStatuses,
    required this.lastUpdated,
    this.operatorName,
    this.operatorNumber,
    this.supervisorNumber,
    this.torrentService1,
    this.supervisorName,
    this.torrentContact,
  });

  factory LocationData.fromJson(Map<String, dynamic> json) {
    return LocationData(
      locationId: json['location_id'] ?? json['id'] ?? 0,
      locationName: json['location_name'] ?? json['name'] ?? '',
      zoneId: json['zone_id'] is String 
          ? int.parse(json['zone_id']) 
          : json['zone_id'] is int 
              ? json['zone_id'] 
              : 0,
      zoneName: json['zone_name'] ?? '',
      locationType: LocationType.fromString(json['location_type'] ?? 'SWPS'),
      currentDayQty: _parseDouble(json['current_day_qty']),
      previousDayQty: _parseDouble(json['previous_day_qty']),
      tillTodayQty: _parseDouble(json['till_today_qty']),
      level: _parseDouble(json['level']),
      flowRate: _parseDouble(json['flow_rate']),
      pumpStatuses: (json['pump_statuses'] as List<dynamic>?)
          ?.map((e) => PumpStatus.fromJson(e))
          .toList() ?? [],
      lastUpdated: json['last_updated'] is int
          ? DateTime.fromMillisecondsSinceEpoch(json['last_updated'] * 1000)
          : DateTime.parse(json['last_updated'] ?? DateTime.now().toIso8601String()),
      operatorName: json['operator_name'] ?? '',
      operatorNumber: json['operator_number'] ?? '',
      supervisorNumber: json['supervisor_number'] ?? '',
      torrentService1: json['torrent_service_1'] ?? '',
      supervisorName: json['supervisor_name'] ?? '',
      torrentContact: json['torrent_contact'] ?? '',
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }

  Map<String, dynamic> toJson() {
    return {
      'location_id': locationId,
      'location_name': locationName,
      'zone_id': zoneId,
      'zone_name': zoneName,
      'location_type': locationType.toString(),
      'current_day_qty': currentDayQty,
      'previous_day_qty': previousDayQty,
      'till_today_qty': tillTodayQty,
      'level': level,
      'flow_rate': flowRate,
      'pump_statuses': pumpStatuses.map((e) => e.toJson()).toList(),
      'last_updated': lastUpdated.toIso8601String(),
      'operator_name': operatorName,
      'operator_number': operatorNumber,
      'supervisor_number': supervisorNumber,
      'torrent_service_1': torrentService1,
      'supervisor_name': supervisorName,
      'torrent_contact': torrentContact,
    };
  }

  String get formattedCurrentDayQty => '${currentDayQty.toStringAsFixed(2)} MLD';
  String get formattedPreviousDayQty => '${previousDayQty.toStringAsFixed(2)} MLD';
  String get formattedTillTodayQty => '${tillTodayQty.toStringAsFixed(2)} ML';
  String get formattedLevel => '${level.toStringAsFixed(2)} Mtr';
  String get formattedFlowRate => '${flowRate.toStringAsFixed(2)} m3/Hr';
  String get formattedTimestamp => '${lastUpdated.day.toString().padLeft(2, '0')}-${lastUpdated.month.toString().padLeft(2, '0')}-${lastUpdated.year} ${lastUpdated.hour.toString().padLeft(2, '0')}:${lastUpdated.minute.toString().padLeft(2, '0')}:${lastUpdated.second.toString().padLeft(2, '0')}';
}

// Pump Status Model
class PumpStatus {
  final int pumpNumber;
  final bool isActive;
  final DateTime? lastUpdated;
  final String tagName;

  PumpStatus({
    required this.pumpNumber,
    required this.isActive,
    this.lastUpdated,
    required this.tagName,
  });

  factory PumpStatus.fromJson(Map<String, dynamic> json) {
    return PumpStatus(
      pumpNumber: json['pump_number'] is String 
          ? int.parse(json['pump_number']) 
          : json['pump_number'] is int 
              ? json['pump_number'] 
              : int.parse(json['pump_number'].toString()),
      isActive: json['is_active'] is bool
          ? json['is_active']
          : (json['is_active'] == 1 || json['is_active'] == '1'),
      lastUpdated: json['last_updated'] != null 
          ? DateTime.parse(json['last_updated'])
          : null,
      tagName: json['tag_name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pump_number': pumpNumber,
      'is_active': isActive,
      'last_updated': lastUpdated?.toIso8601String(),
      'tag_name': tagName,
    };
  }

  String get displayName {
    if (tagName.contains('HT')) {
      return 'HT Pump ${pumpNumber}';
    } else if (tagName.contains('LT')) {
      return 'LT Pump ${pumpNumber}';
    }
    return 'Pump ${pumpNumber}';
  }

  // Sort order: HT pumps first, then LT pumps, then other pumps
  int get sortOrder {
    if (tagName.contains('HT')) return 0;
    if (tagName.contains('LT')) return 1;
    return 2;
  }

  // Compare method for sorting
  static int compare(PumpStatus a, PumpStatus b) {
    // First compare by sort order (HT, LT, other)
    int orderCompare = a.sortOrder.compareTo(b.sortOrder);
    if (orderCompare != 0) return orderCompare;
    
    // If same type, compare by pump number
    return a.pumpNumber.compareTo(b.pumpNumber);
  }
}

class Tag {
  final int id;
  final int locationId;
  final String opcAddress;
  final String mqttTopic;
  final String tagName;
  final TagType tagType;
  final String? unit;
  final double? minValue;
  final double? maxValue;
  final bool isActive;
  final String? locationName;
  final String? zoneName;
  final double? latestValue;
  final int? latestTimestamp;
  final DateTime createdAt;
  final DateTime updatedAt;

  Tag({
    required this.id,
    required this.locationId,
    required this.opcAddress,
    required this.mqttTopic,
    required this.tagName,
    required this.tagType,
    this.unit,
    this.minValue,
    this.maxValue,
    required this.isActive,
    this.locationName,
    this.zoneName,
    this.latestValue,
    this.latestTimestamp,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Tag.fromJson(Map<String, dynamic> json) {
    return Tag(
      id: json['id'],
      locationId: json['location_id'],
      opcAddress: json['opc_address'],
      mqttTopic: json['mqtt_topic'],
      tagName: json['tag_name'],
      tagType: TagType.fromString(json['tag_type']),
      unit: json['unit'],
      minValue: _parseToDouble(json['min_value']),
      maxValue: _parseToDouble(json['max_value']),
      isActive: _parseToBool(json['is_active']) ?? true,
      locationName: json['location_name'],
      zoneName: json['zone_name'],
      latestValue: _parseToDouble(json['latest_value']),
      latestTimestamp: json['latest_timestamp'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  // Helper method to safely parse values to double
  static double? _parseToDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  // Helper method to safely parse values to bool
  static bool? _parseToBool(dynamic value) {
    if (value == null) return null;
    if (value is bool) return value;
    if (value is int) return value == 1;
    if (value is String) {
      final lower = value.toLowerCase();
      if (lower == 'true' || lower == '1') return true;
      if (lower == 'false' || lower == '0') return false;
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'location_id': locationId,
      'opc_address': opcAddress,
      'mqtt_topic': mqttTopic,
      'tag_name': tagName,
      'tag_type': tagType.toString(),
      'unit': unit,
      'min_value': minValue,
      'max_value': maxValue,
      'is_active': isActive,
      'location_name': locationName,
      'zone_name': zoneName,
      'latest_value': latestValue,
      'latest_timestamp': latestTimestamp,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  bool get hasValidValue => latestValue != null;

  bool get isInRange {
    if (latestValue == null) return true;
    if (minValue != null && latestValue! < minValue!) return false;
    if (maxValue != null && latestValue! > maxValue!) return false;
    return true;
  }

  String get displayValue {
    if (latestValue == null) return 'No Data';
    return '${latestValue!.toStringAsFixed(2)} ${unit ?? ''}';
  }
}

enum TagType {
  level,
  pumpStatus,
  flowRate,
  totalizer,
  other;

  static TagType fromString(String value) {
    switch (value.toUpperCase()) {
      case 'LEVEL':
        return TagType.level;
      case 'PUMP_STATUS':
        return TagType.pumpStatus;
      case 'FLOW_RATE':
        return TagType.flowRate;
      case 'TOTALIZER':
        return TagType.totalizer;
      default:
        return TagType.other;
    }
  }

  @override
  String toString() {
    switch (this) {
      case TagType.level:
        return 'LEVEL';
      case TagType.pumpStatus:
        return 'PUMP_STATUS';
      case TagType.flowRate:
        return 'FLOW_RATE';
      case TagType.totalizer:
        return 'TOTALIZER';
      case TagType.other:
        return 'OTHER';
    }
  }

  String get displayName {
    switch (this) {
      case TagType.level:
        return 'Level';
      case TagType.pumpStatus:
        return 'Pump Status';
      case TagType.flowRate:
        return 'Flow Rate';
      case TagType.totalizer:
        return 'Totalizer';
      case TagType.other:
        return 'Other';
    }
  }
}

class TagData {
  final int tagId;
  final double value;
  final int timestamp;
  final DateTime receivedAt;

  TagData({
    required this.tagId,
    required this.value,
    required this.timestamp,
    required this.receivedAt,
  });

  factory TagData.fromJson(Map<String, dynamic> json) {
    return TagData(
      tagId: json['tag_id'],
      value: _parseToDoubleRequired(json['value']),
      timestamp: json['timestamp'],
      receivedAt: DateTime.parse(json['received_at']),
    );
  }

  // Helper method to safely parse values to double (required)
  static double _parseToDoubleRequired(dynamic value) {
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      final parsed = double.tryParse(value);
      if (parsed != null) return parsed;
    }
    return 0.0; // Default value if parsing fails
  }

  Map<String, dynamic> toJson() {
    return {
      'tag_id': tagId,
      'value': value,
      'timestamp': timestamp,
      'received_at': receivedAt.toIso8601String(),
    };
  }

  DateTime get timestampAsDateTime {
    return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
  }
}
