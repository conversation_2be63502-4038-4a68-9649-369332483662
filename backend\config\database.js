import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'storm_db',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

// Test connection
export const testConnection = async () => {
  try {
    const connection = await pool.getConnection();
    console.log('✅ Database connected successfully');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
};

// Execute query with better error handling
export const executeQuery = async (query, params = []) => {
  try {
    //console.log(`Executing SQL: ${query}`);
   // console.log('With params:', params);
    
    const [results] = await pool.execute(query, params);
   // console.log('Query successful, rows affected:', results.length || results.affectedRows);
    return results;
  } catch (error) {
    console.error('Database query error:', {
      message: error.message,
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState,
      sqlMessage: error.sqlMessage,
      sql: error.sql
    });
    throw error;
  }
};

// Get connection for transactions
export const getConnection = async () => {
  return await pool.getConnection();
};

export default pool;
