<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StormApp Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .nav-link {
            color: rgba(255,255,255,0.8) !important;
            transition: all 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            color: white !important;
            background-color: rgba(255,255,255,0.1);
            border-radius: 5px;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s;
        }
        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }
        .btn-group-sm .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        .content-area {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
            color: #667eea !important;
        }

        /* Sortable table headers */
        .sortable {
            cursor: pointer;
            user-select: none;
            position: relative;
        }

        .sortable:hover {
            background-color: #f8f9fa;
        }

        .sortable i {
            margin-left: 5px;
            opacity: 0.5;
        }

        .sortable.sort-asc i:before {
            content: "\f0de";
            opacity: 1;
            color: #007bff;
        }

        .sortable.sort-desc i:before {
            content: "\f0dd";
            opacity: 1;
            color: #007bff;
        }

        /* Filter section styling */
        .filter-section {
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        /* Table row highlighting for filtered results */
        .table tbody tr.filtered-out {
            display: none;
        }

        .table tbody tr.highlight {
            background-color: #fff3cd;
        }

        /* Real-time data paused indicator */
        .realtime-paused {
            opacity: 0.7;
        }

        /* Count badges */
        .count-badge {
            background-color: #6c757d;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
        }

        /* Mobile sidebar styles */
        @media (max-width: 767.98px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: -100%;
                width: 280px;
                height: 100vh;
                z-index: 1050;
                transition: left 0.3s ease-in-out;
            }

            .sidebar.show {
                left: 0;
            }

            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1040;
                display: none;
            }

            .sidebar-overlay.show {
                display: block;
            }

            .content-area {
                margin-left: 0 !important;
            }

            .mobile-menu-toggle {
                display: block !important;
            }
        }

        @media (min-width: 768px) {
            .mobile-menu-toggle {
                display: none !important;
            }
        }

        /* Mobile header optimization */
        @media (max-width: 767.98px) {
            .mobile-header {
                flex-wrap: nowrap !important;
            }

            .mobile-header .btn-toolbar {
                margin-bottom: 0 !important;
            }

            .mobile-header h1 {
                font-size: 1.5rem !important;
                margin-bottom: 0 !important;
            }

            .mobile-status {
                font-size: 0.75rem !important;
            }

            .mobile-refresh-btn {
                padding: 0.25rem 0.5rem !important;
                font-size: 0.75rem !important;
            }
        }
    </style>
</head>
<body>
    <!-- Mobile sidebar overlay -->
    <div class="sidebar-overlay" id="sidebar-overlay" onclick="toggleMobileSidebar()"></div>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar" id="sidebar">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-water"></i> StormApp
                        </h4>
                        <small class="text-white-50">Admin Dashboard</small>
                        <div class="mt-2">
                            <small class="text-white-50" id="user-info">Loading...</small>
                        </div>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showSection('dashboard')">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('zones')">
                                <i class="fas fa-map-marked-alt"></i> Zones
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('locations')">
                                <i class="fas fa-map-marker-alt"></i> Locations
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('tags')">
                                <i class="fas fa-tags"></i> Tags
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('realtime')">
                                <i class="fas fa-chart-line"></i> Real-time Data
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('users')">
                                <i class="fas fa-users"></i> User Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showSection('settings')">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                        </li>
                    </ul>

                    <!-- User Actions -->
                    <div class="mt-auto pt-3 border-top border-secondary">
                        <div class="mb-2" id="user-info" style="display: none;">
                            <small class="text-light">Logged in as:</small>
                            <div class="text-warning" id="current-username">-</div>
                        </div>
                        <button class="btn btn-outline-info btn-sm w-100 mb-2" id="change-password-btn" onclick="changeMyPassword()" style="display: none;">
                            <i class="fas fa-key me-1"></i>Change Password
                        </button>
                        <button class="btn btn-outline-light btn-sm w-100" id="logout-btn" onclick="logout()" style="display: none;">
                            <i class="fas fa-sign-out-alt me-1"></i>Logout
                        </button>
                    </div>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content-area">
                <div class="d-flex justify-content-between mobile-header align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div class="d-flex align-items-center flex-grow-1">
                        <button class="btn btn-outline-secondary me-2 mobile-menu-toggle" onclick="toggleMobileSidebar()" style="display: none;">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h1 class="h2 mb-0 me-auto" id="page-title">Dashboard</h1>
                    </div>
                    <div class="btn-toolbar mb-2 mb-md-0 d-flex align-items-center">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary mobile-refresh-btn" onclick="refreshData()">
                                <i class="fas fa-sync-alt"></i>
                                <span class="d-none d-md-inline"> Refresh</span>
                            </button>
                        </div>
                        <div class="d-flex align-items-center mobile-status">
                            <span class="status-indicator status-online"></span>
                            <small class="text-muted">
                                <span class="d-none d-sm-inline">System </span>Online
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Section -->
                <div id="dashboard-section" class="content-section">
                    <div class="row mb-4">
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                Total Zones</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-zones">0</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-map-marked-alt fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                Total Locations</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-locations">0</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-map-marker-alt fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                Active Tags</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-tags">0</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-tags fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card border-left-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                                Data Points (1h)</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="data-points">0</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Zone Overview</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="zones-overview-table">
                                            <thead>
                                                <tr>
                                                    <th>Zone</th>
                                                    <th>Locations</th>
                                                    <th>Tags</th>
                                                    <th>Active</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- Data will be loaded here -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-danger">Recent Alerts</h6>
                                </div>
                                <div class="card-body">
                                    <div id="alerts-container">
                                        <!-- Alerts will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other sections will be loaded dynamically -->
                <div id="zones-section" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3>Zones Management</h3>
                        <button class="btn btn-primary" onclick="showAddZoneModal()">
                            <i class="fas fa-plus"></i> Add Zone
                        </button>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped" id="zones-table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Description</th>
                                            <th>Locations</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="locations-section" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3>Locations Management</h3>
                        <button class="btn btn-primary" onclick="showAddLocationModal()">
                            <i class="fas fa-plus"></i> Add Location
                        </button>
                    </div>

                    <!-- Locations Filters -->
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label class="form-label">Filter by Zone</label>
                                    <select class="form-select" id="locations-zone-filter">
                                        <option value="">All Zones</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Search Location</label>
                                    <input type="text" class="form-control" id="locations-search" placeholder="Search by name or description...">
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">Min Pumps</label>
                                    <input type="number" class="form-control" id="locations-min-pumps" min="0" placeholder="0">
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <button class="btn btn-outline-secondary btn-sm" onclick="clearLocationsFilters()">
                                        <i class="fas fa-times"></i> Clear Filters
                                    </button>
                                    <span class="ms-3 text-muted" id="locations-count">0 locations</span>
                                    <small class="ms-3 text-muted">Click column headers to sort</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped" id="locations-table">
                                    <thead>
                                        <tr>
                                            <th class="sortable" data-column="id">ID <i class="fas fa-sort"></i></th>
                                            <th class="sortable" data-column="zone_name">Zone <i class="fas fa-sort"></i></th>
                                            <th class="sortable" data-column="name">Name <i class="fas fa-sort"></i></th>
                                            <th>Description</th>
                                            <th class="sortable" data-column="location_type">Type <i class="fas fa-sort"></i></th>
                                            <th class="sortable" data-column="pump_count">Pumps <i class="fas fa-sort"></i></th>
                                            <th class="sortable" data-column="tag_count">Tags <i class="fas fa-sort"></i></th>
                                            <th>Operator</th>
                                            <th>Contact Info</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="tags-section" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3>Tags Management</h3>
                        <button class="btn btn-primary" onclick="showAddTagModal()">
                            <i class="fas fa-plus"></i> Add Tag
                        </button>
                    </div>

                    <!-- Tags Filters -->
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-2">
                                    <label class="form-label">Filter by Zone</label>
                                    <select class="form-select" id="tags-zone-filter">
                                        <option value="">All Zones</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Filter by Location</label>
                                    <select class="form-select" id="tags-location-filter">
                                        <option value="">All Locations</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Filter by Type</label>
                                    <select class="form-select" id="tags-type-filter">
                                        <option value="">All Types</option>
                                        <option value="LEVEL">Level</option>
                                        <option value="PUMP_STATUS">Pump Status</option>
                                        <option value="FLOW_RATE">Flow Rate</option>
                                        <option value="TOTALIZER">Totalizer</option>
                                        <option value="OTHER">Other</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Status</label>
                                    <select class="form-select" id="tags-status-filter">
                                        <option value="">All Status</option>
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Search Tag</label>
                                    <input type="text" class="form-control" id="tags-search" placeholder="Search by name or OPC address...">
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <button class="btn btn-outline-secondary btn-sm" onclick="clearTagsFilters()">
                                        <i class="fas fa-times"></i> Clear Filters
                                    </button>
                                    <span class="ms-3 text-muted" id="tags-count">0 tags</span>
                                    <small class="ms-3 text-muted">Click column headers to sort</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped" id="tags-table">
                                    <thead>
                                        <tr>
                                            <th class="sortable" data-column="id">ID <i class="fas fa-sort"></i></th>
                                            <th class="sortable" data-column="zone_name">Zone <i class="fas fa-sort"></i></th>
                                            <th class="sortable" data-column="location_name">Location <i class="fas fa-sort"></i></th>
                                            <th class="sortable" data-column="tag_name">Tag Name <i class="fas fa-sort"></i></th>
                                            <th class="sortable" data-column="tag_type">Type <i class="fas fa-sort"></i></th>
                                            <th>OPC Address</th>
                                            <th class="sortable" data-column="latest_value">Latest Value <i class="fas fa-sort"></i></th>
                                            <th class="sortable" data-column="is_active">Status <i class="fas fa-sort"></i></th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="realtime-section" class="content-section" style="display: none;">
                    <h3>Real-time Data Monitor</h3>

                    <!-- Real-time Filters -->
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-2">
                                    <label class="form-label">Filter by Zone</label>
                                    <select class="form-select" id="realtime-zone-filter">
                                        <option value="">All Zones</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Filter by Location</label>
                                    <select class="form-select" id="realtime-location-filter">
                                        <option value="">All Locations</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Filter by Tag Type</label>
                                    <select class="form-select" id="realtime-tag-filter">
                                        <option value="">All Tags</option>
                                        <option value="LEVEL">Level</option>
                                        <option value="PUMP_STATUS">Pump Status</option>
                                        <option value="FLOW_RATE">Flow Rate</option>
                                        <option value="TOTALIZER">Totalizer</option>
                                        <option value="OTHER">Other</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Max Rows</label>
                                    <select class="form-select" id="realtime-max-rows">
                                        <option value="25">25 rows</option>
                                        <option value="50" selected>50 rows</option>
                                        <option value="100">100 rows</option>
                                        <option value="200">200 rows</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">Search</label>
                                    <input type="text" class="form-control" id="realtime-search" placeholder="Search tag name...">
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <button class="btn btn-outline-secondary btn-sm" onclick="clearRealtimeFilters()">
                                        <i class="fas fa-times"></i> Clear Filters
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm ms-2" onclick="pauseRealtimeData()" id="pause-btn">
                                        <i class="fas fa-pause"></i> Pause
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm ms-2" onclick="clearRealtimeData()">
                                        <i class="fas fa-trash"></i> Clear Data
                                    </button>
                                    <span class="ms-3 text-muted" id="realtime-count">0 readings</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Live Data Stream</h5>
                                    <div class="d-flex align-items-center">
                                        <span class="status-indicator" id="ws-status"></span>
                                        <small id="ws-status-text">Connecting...</small>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm" id="realtime-table">
                                            <thead>
                                                <tr>
                                                    <th class="sortable" data-column="time">Time <i class="fas fa-sort"></i></th>
                                                    <th class="sortable" data-column="zone">Zone <i class="fas fa-sort"></i></th>
                                                    <th class="sortable" data-column="location">Location <i class="fas fa-sort"></i></th>
                                                    <th class="sortable" data-column="tag">Tag <i class="fas fa-sort"></i></th>
                                                    <th class="sortable" data-column="value">Value <i class="fas fa-sort"></i></th>
                                                    <th>Unit</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- Real-time data will appear here -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Management Section -->
                <div id="users-section" class="content-section" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>App Users Management</h3>
                        <button class="btn btn-primary" onclick="showAddUserModal()">
                            <i class="fas fa-user-plus"></i> Add User
                        </button>
                    </div>

                    <!-- User Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-white bg-success">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="total-users">0</h4>
                                            <small>Total Users</small>
                                        </div>
                                        <i class="fas fa-users fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-primary">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="super-admins">0</h4>
                                            <small>Super Admins</small>
                                        </div>
                                        <i class="fas fa-user-shield fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-warning">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="zone-users">0</h4>
                                            <small>Zone Specific</small>
                                        </div>
                                        <i class="fas fa-map-marked-alt fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-info">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="active-users">0</h4>
                                            <small>Active Users</small>
                                        </div>
                                        <i class="fas fa-user-check fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Users Table -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-users me-2"></i>User List
                                <span class="badge bg-secondary ms-2" id="users-count">0</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped" id="users-table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Username</th>
                                            <th>Full Name</th>
                                            <th>Role</th>
                                            <th>Zone Access</th>
                                            <th>Allowed Zones</th>
                                            <th>Status</th>
                                            <th>Last Login</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Users will be loaded here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Access - Login Credentials -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-key me-2"></i>Quick Login Credentials
                                <small class="text-muted">(Default password: password123)</small>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <strong>Super Admin:</strong>
                                    <br><code>appadmin</code>
                                </div>
                                <div class="col-md-8">
                                    <strong>Zone Users:</strong>
                                    <br><code>northzone</code> - North Zone Only
                                    <br><code>southzone</code> - South Zone Only
                                    <br><code>eastzone</code> - East Zone Only
                                    <br><code>westzone</code> - West Zone Only
                                    <br><code>southwestzone</code> - South West Zone Only
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="settings-section" class="content-section" style="display: none;">
                    <h3>System Settings</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>MQTT Configuration</h5>
                                </div>
                                <div class="card-body">
                                    <form>
                                        <div class="mb-3">
                                            <label class="form-label">MQTT Broker URL</label>
                                            <input type="text" class="form-control" value="mqtt://localhost:1883" readonly>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Topic Prefix</label>
                                            <input type="text" class="form-control" value="AMC/Storm/amcstorm" readonly>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Connection Status</label>
                                            <div class="d-flex align-items-center">
                                                <span class="status-indicator status-online"></span>
                                                <span>Connected</span>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Database Configuration</h5>
                                </div>
                                <div class="card-body">
                                    <form>
                                        <div class="mb-3">
                                            <label class="form-label">Database Host</label>
                                            <input type="text" class="form-control" value="localhost" readonly>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Database Name</label>
                                            <input type="text" class="form-control" value="storm_db" readonly>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Connection Status</label>
                                            <div class="d-flex align-items-center">
                                                <span class="status-indicator status-online"></span>
                                                <span>Connected</span>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Add Zone Modal -->
    <div class="modal fade" id="addZoneModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Zone</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addZoneForm">
                        <div class="mb-3">
                            <label class="form-label">Zone Name *</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" name="description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveZone()">Save Zone</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Location Modal -->
    <div class="modal fade" id="addLocationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Location</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addLocationForm">
                        <div class="mb-3">
                            <label class="form-label">Zone *</label>
                            <select class="form-select" name="zone_id" required>
                                <option value="">Select Zone</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Location Name *</label>
                            <input type="text" class="form-control" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" name="description" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Location Type *</label>
                            <select class="form-select" name="location_type" required>
                                <option value="">Select Location Type</option>
                                <option value="CSPS">CSPS (Critical Sewage Pumping Station)</option>
                                <option value="SWPS">SWPS (Storm Water Pumping Station)</option>
                                <option value="TSPS">TSPS (Terminal Sewage Pumping Station)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Number of Pumps</label>
                            <input type="number" class="form-control" name="pump_count" min="0" value="0">
                        </div>

                        <!-- Operator Contact Information -->
                        <hr>
                        <h6 class="text-muted mb-3">Operator Contact Information</h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Operator Name</label>
                                    <input type="text" class="form-control" name="operator_name" placeholder="Enter operator name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Operator Number</label>
                                    <input type="tel" class="form-control" name="operator_number" placeholder="Enter phone number">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Supervisor Number</label>
                                    <input type="tel" class="form-control" name="supervisor_number" placeholder="Enter supervisor phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Torrent Contact</label>
                                    <input type="tel" class="form-control" name="torrent_contact" placeholder="Enter torrent contact">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Torrent Service 1</label>
                                    <input type="text" class="form-control" name="torrent_service_1" placeholder="Enter service details">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Torrent Service 2</label>
                                    <input type="text" class="form-control" name="supervisor_name" placeholder="Enter service details">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveLocation()">Save Location</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Tag Modal -->
    <div class="modal fade" id="addTagModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Tag</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addTagForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Location *</label>
                                    <select class="form-select" name="location_id" required>
                                        <option value="">Select Location</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Tag Type</label>
                                    <select class="form-select" name="tag_type">
                                        <option value="LEVEL">Level</option>
                                        <option value="PUMP_STATUS">Pump Status</option>
                                        <option value="FLOW_RATE">Flow Rate</option>
                                        <option value="TOTALIZER">Totalizer</option>
                                        <option value="OTHER">Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Tag Name *</label>
                            <input type="text" class="form-control" name="tag_name" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">OPC Address *</label>
                            <input type="text" class="form-control" name="opc_address" required
                                   placeholder="ns=2;s=LOCATION.PUMP.TAG_NAME">
                            <div class="form-text">Format: ns=2;s=LOCATION.PUMP.TAG_NAME</div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Unit</label>
                                    <input type="text" class="form-control" name="unit" placeholder="e.g., m, L/s, %">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Min Value</label>
                                    <input type="number" class="form-control" name="min_value" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Max Value</label>
                                    <input type="number" class="form-control" name="max_value" step="0.01">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveTag()">Save Tag</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Zone Modal -->
    <div class="modal fade" id="editZoneModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Zone</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editZoneForm">
                        <input type="hidden" id="editZoneId">
                        <div class="mb-3">
                            <label class="form-label">Zone Name *</label>
                            <input type="text" class="form-control" id="editZoneName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" id="editZoneDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="updateZone()">Update Zone</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Location Modal -->
    <div class="modal fade" id="editLocationModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Location</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editLocationForm">
                        <input type="hidden" id="editLocationId">
                        <div class="mb-3">
                            <label class="form-label">Zone *</label>
                            <select class="form-select" id="editLocationZone" required>
                                <option value="">Select Zone</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Location Name *</label>
                            <input type="text" class="form-control" id="editLocationName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control" id="editLocationDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Location Type *</label>
                            <select class="form-select" id="editLocationLocationType" required>
                                <option value="">Select Location Type</option>
                                <option value="CSPS">CSPS (Critical Sewage Pumping Station)</option>
                                <option value="SWPS">SWPS (Storm Water Pumping Station)</option>
                                <option value="TSPS">TSPS (Terminal Sewage Pumping Station)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Number of Pumps</label>
                            <input type="number" class="form-control" id="editLocationPumpCount" min="0" value="0">
                        </div>

                        <!-- Operator Contact Information -->
                        <hr>
                        <h6 class="text-muted mb-3">Operator Contact Information</h6>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Operator Name</label>
                                    <input type="text" class="form-control" id="editLocationOperatorName" placeholder="Enter operator name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Operator Number</label>
                                    <input type="tel" class="form-control" id="editLocationOperatorNumber" placeholder="Enter phone number">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Supervisor Number</label>
                                    <input type="tel" class="form-control" id="editLocationSupervisorNumber" placeholder="Enter supervisor phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Torrent Contact</label>
                                    <input type="tel" class="form-control" id="editLocationTorrentContact" placeholder="Enter torrent contact">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Torrent Service 1</label>
                                    <input type="text" class="form-control" id="editLocationTorrentService1" placeholder="Enter service details">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Supervisor Name</label>
                                    <input type="text" class="form-control" id="editLocationSupervisorName" placeholder="Enter supervisor Name">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="updateLocation()">Update Location</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Tag Modal -->
    <div class="modal fade" id="editTagModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Tag</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editTagForm">
                        <input type="hidden" id="editTagId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Location *</label>
                                    <select class="form-select" id="editTagLocation" required>
                                        <option value="">Select Location</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Tag Type</label>
                                    <select class="form-select" id="editTagType">
                                        <option value="LEVEL">Level</option>
                                        <option value="PUMP_STATUS">Pump Status</option>
                                        <option value="FLOW_RATE">Flow Rate</option>
                                        <option value="TOTALIZER">Totalizer</option>
                                        <option value="OTHER">Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Tag Name *</label>
                            <input type="text" class="form-control" id="editTagName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">OPC Address *</label>
                            <input type="text" class="form-control" id="editTagOpcAddress" required
                                   placeholder="ns=2;s=LOCATION.PUMP.TAG_NAME">
                            <div class="form-text">Format: ns=2;s=LOCATION.PUMP.TAG_NAME</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">MQTT Topic</label>
                            <input type="text" class="form-control" id="editTagMqttTopic"
                                   placeholder="AMC/Storm/amcstorm/LOCATION/PUMP/TAG">
                            <div class="form-text">MQTT topic for this tag</div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Unit</label>
                                    <input type="text" class="form-control" id="editTagUnit" placeholder="e.g., m, L/s, %">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Min Value</label>
                                    <input type="number" class="form-control" id="editTagMinValue" step="0.01">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Max Value</label>
                                    <input type="number" class="form-control" id="editTagMaxValue" step="0.01">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="updateTag()">Update Tag</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New App User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Username *</label>
                                    <input type="text" class="form-control" id="addUserUsername" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Full Name</label>
                                    <input type="text" class="form-control" id="addUserFullName">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-control" id="addUserEmail">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Password *</label>
                                    <input type="password" class="form-control" id="addUserPassword" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Role *</label>
                                    <select class="form-select" id="addUserRole" required>
                                        <option value="">Select Role</option>
                                        <option value="manager">Manager</option>
                                        <option value="operator">Operator</option>
                                        <option value="viewer">Viewer</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Zone Access Type *</label>
                                    <select class="form-select" id="addUserZoneAccessType" required onchange="toggleZoneSelection('add')">
                                        <option value="">Select Access Type</option>
                                        <option value="all">All Zones (Super Admin)</option>
                                        <option value="specific">Specific Zones</option>
                                        <option value="none">No Access</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3" id="addUserZonesContainer" style="display: none;">
                            <label class="form-label">Allowed Zones</label>
                            <div id="addUserZonesList">
                                <!-- Zone checkboxes will be loaded here -->
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">Save User</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit App User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editUserForm">
                        <input type="hidden" id="editUserId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Username *</label>
                                    <input type="text" class="form-control" id="editUserUsername" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Full Name</label>
                                    <input type="text" class="form-control" id="editUserFullName">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-control" id="editUserEmail">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Role *</label>
                                    <select class="form-select" id="editUserRole" required>
                                        <option value="manager">Manager</option>
                                        <option value="operator">Operator</option>
                                        <option value="viewer">Viewer</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Zone Access Type *</label>
                                    <select class="form-select" id="editUserZoneAccessType" required onchange="toggleZoneSelection('edit')">
                                        <option value="all">All Zones (Super Admin)</option>
                                        <option value="specific">Specific Zones</option>
                                        <option value="none">No Access</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Status</label>
                                    <select class="form-select" id="editUserStatus">
                                        <option value="1">Active</option>
                                        <option value="0">Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3" id="editUserZonesContainer" style="display: none;">
                            <label class="form-label">Allowed Zones</label>
                            <div id="editUserZonesList">
                                <!-- Zone checkboxes will be loaded here -->
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="updateUser()">Update User</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Change Password Modal -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Change User Password</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="changePasswordForm">
                        <input type="hidden" id="changePasswordUserId">
                        <div class="mb-3">
                            <label class="form-label">Username</label>
                            <input type="text" class="form-control" id="changePasswordUsername" readonly>
                        </div>
                        <div class="mb-3" id="currentPasswordContainer" style="display: none;">
                            <label class="form-label">Current Password</label>
                            <input type="password" class="form-control" id="changePasswordCurrent" 
                                   placeholder="Enter current password">
                            <div class="form-text">Required when changing admin password</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">New Password *</label>
                            <input type="password" class="form-control" id="changePasswordNew" 
                                   placeholder="Enter new password" required minlength="6">
                            <div class="form-text">Minimum 6 characters</div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Confirm New Password *</label>
                            <input type="password" class="form-control" id="changePasswordConfirm" 
                                   placeholder="Confirm new password" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="savePasswordChange()">Change Password</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/socket.io/socket.io.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>
