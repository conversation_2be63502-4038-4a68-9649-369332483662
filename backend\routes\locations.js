import express from 'express';
import { executeQuery } from '../config/database.js';
import jwt from 'jsonwebtoken';

const router = express.Router();

// Zone Access Control Middleware
const checkZoneAccess = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    const userId = decoded.userId || decoded.id; // Support both userId and id formats
    
    //console.log('JWT decoded:', { userId, tokenPayload: decoded });

    const userQuery = 'SELECT zone_access_type, allowed_zones, role FROM app_users WHERE id = ? AND is_active = 1';
    const userResult = await executeQuery(userQuery, [userId]);
    
    //console.log('User query result:', userResult);
    
    if (userResult.length === 0) {
      return res.status(403).json({ error: 'User not found or inactive' });
    }

    const user = userResult[0];
    
    if (user.zone_access_type === 'all') {
      req.userZoneAccess = {
        type: 'all',
        allowedZones: null,
        role: user.role
      };
      return next();
    }

    if (user.zone_access_type === 'none') {
      return res.status(403).json({ error: 'User has no zone access' });
    }

    let allowedZones = [];
    try {
      allowedZones = user.allowed_zones ? JSON.parse(user.allowed_zones) : [];
    } catch (e) {
      console.error('Error parsing allowed_zones JSON:', e);
      return res.status(500).json({ error: 'Invalid zone configuration' });
    }

    req.userZoneAccess = {
      type: 'specific',
      allowedZones: allowedZones,
      role: user.role
    };

    next();
  } catch (error) {
    console.error('Zone access check error:', error);
    return res.status(500).json({ error: 'Zone access check failed' });
  }
};

// Helper function to get zone WHERE clause
const getZoneWhereClause = (userZoneAccess, tableAlias = 'l') => {
  if (userZoneAccess.type === 'all') {
    return { clause: '', params: [] };
  }

  if (userZoneAccess.type === 'specific' && userZoneAccess.allowedZones && userZoneAccess.allowedZones.length > 0) {
    const placeholders = userZoneAccess.allowedZones.map(() => '?').join(',');
    // Use the correct column name based on table alias
    const columnName = tableAlias === 'z' ? 'id' : 'zone_id';
    return {
      clause: `AND ${tableAlias}.${columnName} IN (${placeholders})`,
      params: userZoneAccess.allowedZones
    };
  }

  return { clause: 'AND 1=0', params: [] };
};

// Get all locations
router.get('/', checkZoneAccess, async (req, res) => {
  try {
    const zoneFilter = getZoneWhereClause(req.userZoneAccess, 'l');
    const query = `
      SELECT l.*, z.name as zone_name,
             COUNT(t.id) as tag_count
      FROM locations l
      JOIN zones z ON l.zone_id = z.id
      LEFT JOIN tags t ON l.id = t.location_id AND t.is_active = TRUE
      WHERE 1=1 ${zoneFilter.clause}
      GROUP BY l.id
      ORDER BY z.name, l.name
    `;
    const locations = await executeQuery(query, zoneFilter.params);
    res.json(locations);
  } catch (error) {
    console.error('Error fetching locations:', error);
    res.status(500).json({ error: 'Failed to fetch locations' });
  }
});

// Get location detailed data
router.get('/data', checkZoneAccess, async (req, res) => {
  try {
    // Get zone filter for current user
    const zoneFilter = getZoneWhereClause(req.userZoneAccess, 'z');
    
    const query = `
      SELECT
        l.id as location_id,
        l.name as location_name,
        l.location_type as location_type,
        l.zone_id as zone_id,
        z.name as zone_name,
        l.operator_name,
        l.operator_number,
        l.supervisor_number,
        l.torrent_service_1,
        l.supervisor_name,
        l.torrent_contact,
        COALESCE(MAX(CASE
          WHEN t.tag_name LIKE '%CURRENT_DAY_1%'
          THEN td.value/1000
          ELSE 0
        END), 0) as current_day_qty,
        COALESCE(MAX(CASE
          WHEN t.tag_name LIKE '%PREVIOUS_DAY_TOTALIZER%'
          THEN td.value/1000
          ELSE 0
        END), 0) as previous_day_qty,
        COALESCE(MAX(CASE
          WHEN t.tag_name LIKE '%SEWAGE_TOTALIZER%'
          THEN td.value/1000
          ELSE 0
        END), 0) as till_today_qty,
        COALESCE(MAX(CASE
          WHEN t.tag_type = 'LEVEL'
          THEN td.value
          ELSE 0
        END), 0) as level,
        COALESCE(MAX(CASE
          WHEN t.tag_type = 'FLOW_RATE'
          THEN td.value
          ELSE 0
        END), 0) as flow_rate,
        COALESCE(MAX(td.timestamp), UNIX_TIMESTAMP()) as last_updated
      FROM locations l
      JOIN zones z ON l.zone_id = z.id
      LEFT JOIN tags t ON l.id = t.location_id AND t.is_active = TRUE
      LEFT JOIN (
        SELECT tag_id, value, timestamp,
               ROW_NUMBER() OVER (PARTITION BY tag_id ORDER BY timestamp DESC) as rn
        FROM tag_data
      ) td ON t.id = td.tag_id AND td.rn = 1 AND t.is_active=1
      WHERE 1=1 ${zoneFilter.clause}
      GROUP BY l.id, l.name, l.zone_id, z.name, l.operator_name, l.operator_number, l.supervisor_number, 
               l.torrent_service_1, l.supervisor_name, l.torrent_contact
      ORDER BY z.name, l.name
    `;
    const locationData = await executeQuery(query, zoneFilter.params);

    // Get pump statuses for each location
    for (let location of locationData) {
      const pumpQuery = `
        SELECT
          t.tag_name,
          CASE WHEN td.value > 0 THEN 1 ELSE 0 END as is_active,
          td.timestamp as last_updated,
          CASE
            WHEN t.tag_name LIKE 'PUMP_%_RFB' THEN
              CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(t.tag_name, '_', 2), '_', -1) AS UNSIGNED)
            WHEN t.tag_name LIKE 'LT_%_RFB' THEN
              CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(t.tag_name, '_', 2), '_', -1) AS UNSIGNED)
            WHEN t.tag_name = 'HT_RFB' THEN 1
            ELSE 1
          END as pump_number
        FROM tags t
        LEFT JOIN (
          SELECT tag_id, value, timestamp,
                 ROW_NUMBER() OVER (PARTITION BY tag_id ORDER BY timestamp DESC) as rn
          FROM tag_data
        ) td ON t.id = td.tag_id AND td.rn = 1
        WHERE t.location_id = ? AND t.tag_type = 'PUMP_STATUS' AND t.is_active = 1
        ORDER BY pump_number, t.tag_name
      `;
      const pumpStatuses = await executeQuery(pumpQuery, [location.location_id]);
      location.pump_statuses = pumpStatuses.map(pump => ({
        pump_number: pump.pump_number || 1,
        tag_name: pump.tag_name,
        is_active: pump.is_active === 1,
        last_updated: pump.last_updated ? new Date(pump.last_updated * 1000).toISOString() : null
      }));
    }

    res.json(locationData);
  } catch (error) {
    console.error('Error fetching location data:', error);
    res.status(500).json({ error: 'Failed to fetch location data' });
  }
});

// Get locations by zone
router.get('/zone/:zoneId', async (req, res) => {
  try {
    const zoneId = req.params.zoneId;
    const query = `
      SELECT l.*,
             COUNT(t.id) as tag_count
      FROM locations l
      LEFT JOIN tags t ON l.id = t.location_id AND t.is_active = TRUE
      WHERE l.zone_id = ?
      GROUP BY l.id
      ORDER BY l.name
    `;
    const locations = await executeQuery(query, [zoneId]);
    res.json(locations);
  } catch (error) {
    console.error('Error fetching locations by zone:', error);
    res.status(500).json({ error: 'Failed to fetch locations' });
  }
});

// Get locations by type
router.get('/type/:locationtype', async (req, res) => {
  try {
    const locationType = req.params.locationtype.toUpperCase();
    const validLocationTypes = ['CSPS', 'SWPS', 'TSPS'];
    
    if (!validLocationTypes.includes(locationType)) {
      return res.status(400).json({ 
        error: 'Invalid location type. Must be one of: CSPS, SWPS, TSPS' 
      });
    }

    const query = `
      SELECT l.*, z.name as zone_name,
             COUNT(t.id) as tag_count
      FROM locations l
      JOIN zones z ON l.zone_id = z.id
      LEFT JOIN tags t ON l.id = t.location_id AND t.is_active = TRUE
      WHERE l.location_type = ?
      GROUP BY l.id
      ORDER BY z.name, l.name
    `;
    const locations = await executeQuery(query, [locationType]);
    res.json(locations);
  } catch (error) {
    console.error('Error fetching locations by type:', error);
    res.status(500).json({ error: 'Failed to fetch locations' });
  }
});

// Get location detailed data by type
router.get('/data/type/:locationtype', async (req, res) => {
  try {
    const locationType = req.params.locationtype.toUpperCase();
    const validLocationTypes = ['CSPS', 'SWPS', 'TSPS'];
    
    if (!validLocationTypes.includes(locationType)) {
      return res.status(400).json({ 
        error: 'Invalid location type. Must be one of: CSPS, SWPS, TSPS' 
      });
    }

    const query = `
      SELECT
        l.id as location_id,
        l.name as location_name,
        l.location_type as location_type,
        l.zone_id as zone_id,
        z.name as zone_name,
        l.operator_name,
        l.operator_number,
        l.supervisor_number,
        l.torrent_service_1,
        l.supervisor_name,
        l.torrent_contact,
        COALESCE(MAX(CASE
          WHEN t.tag_name LIKE '%CURRENT_DAY_1%'
          THEN td.value/1000
          ELSE 0
        END), 0) as current_day_qty,
        COALESCE(MAX(CASE
          WHEN t.tag_name LIKE '%PREVIOUS_DAY_TOTALIZER%'
          THEN td.value/1000
          ELSE 0
        END), 0) as previous_day_qty,
        COALESCE(MAX(CASE
          WHEN t.tag_name LIKE '%SEWAGE_TOTALIZER%'
          THEN td.value/1000
          ELSE 0
        END), 0) as till_today_qty,
        COALESCE(MAX(CASE
          WHEN t.tag_type = 'LEVEL'
          THEN td.value
          ELSE 0
        END), 0) as level,
        COALESCE(MAX(CASE
          WHEN t.tag_type = 'FLOW_RATE'
          THEN td.value
          ELSE 0
        END), 0) as flow_rate,
        COALESCE(MAX(td.timestamp), UNIX_TIMESTAMP()) as last_updated
      FROM locations l
      JOIN zones z ON l.zone_id = z.id
      LEFT JOIN tags t ON l.id = t.location_id AND t.is_active = TRUE
      LEFT JOIN (
        SELECT tag_id, value, timestamp,
               ROW_NUMBER() OVER (PARTITION BY tag_id ORDER BY timestamp DESC) as rn
        FROM tag_data
      ) td ON t.id = td.tag_id AND td.rn = 1 AND t.is_active=1
      WHERE l.location_type = ?
      GROUP BY l.id, l.name, l.location_type, l.zone_id, z.name, l.operator_name, l.operator_number, l.supervisor_number, 
               l.torrent_service_1, l.supervisor_name, l.torrent_contact
      ORDER BY z.name, l.name
    `;
    const locationData = await executeQuery(query, [locationType]);

    // Get pump statuses for each location
    for (let location of locationData) {
      const pumpQuery = `
        SELECT
          t.tag_name,
          CASE WHEN td.value > 0 THEN 1 ELSE 0 END as is_active,
          td.timestamp as last_updated,
          CASE
            WHEN t.tag_name LIKE 'PUMP_%_RFB' THEN
              CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(t.tag_name, '_', 2), '_', -1) AS UNSIGNED)
            WHEN t.tag_name LIKE 'LT_%_RFB' THEN
              CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(t.tag_name, '_', 2), '_', -1) AS UNSIGNED)
            WHEN t.tag_name = 'HT_RFB' THEN 1
            ELSE 1
          END as pump_number
        FROM tags t
        LEFT JOIN (
          SELECT tag_id, value, timestamp,
                 ROW_NUMBER() OVER (PARTITION BY tag_id ORDER BY timestamp DESC) as rn
          FROM tag_data
        ) td ON t.id = td.tag_id AND td.rn = 1
        WHERE t.location_id = ? AND t.tag_type = 'PUMP_STATUS' AND t.is_active = 1
        ORDER BY pump_number, t.tag_name
      `;
      const pumpStatuses = await executeQuery(pumpQuery, [location.location_id]);
      location.pump_statuses = pumpStatuses.map(pump => ({
        pump_number: pump.pump_number || 1,
        tag_name: pump.tag_name,
        is_active: pump.is_active === 1,
        last_updated: pump.last_updated ? new Date(pump.last_updated * 1000).toISOString() : null
      }));
    }

    res.json(locationData);
  } catch (error) {
    console.error('Error fetching location data by type:', error);
    res.status(500).json({ error: 'Failed to fetch location data' });
  }
});

// Get location types summary
router.get('/stats/types', async (req, res) => {
  try {
    const query = `
      SELECT 
        l.location_type,
        COUNT(*) as total_locations,
        SUM(l.pump_count) as total_pumps,
        COUNT(DISTINCT l.zone_id) as zones_count
      FROM locations l
      GROUP BY l.location_type
      ORDER BY l.location_type
    `;
    const stats = await executeQuery(query);
    res.json(stats);
  } catch (error) {
    console.error('Error fetching location type stats:', error);
    res.status(500).json({ error: 'Failed to fetch location type statistics' });
  }
});

// Get location by ID with tags
router.get('/:id', async (req, res) => {
  try {
    const locationId = req.params.id;

    // Get location details
    const locationQuery = `
      SELECT l.*, z.name as zone_name
      FROM locations l
      JOIN zones z ON l.zone_id = z.id
      WHERE l.id = ?
    `;
    const locations = await executeQuery(locationQuery, [locationId]);

    if (locations.length === 0) {
      return res.status(404).json({ error: 'Location not found' });
    }

    // Get tags for this location
    const tagsQuery = `
      SELECT t.*,
             td.value as latest_value,
             td.timestamp as latest_timestamp
      FROM tags t
      LEFT JOIN (
        SELECT tag_id, value, timestamp,
               ROW_NUMBER() OVER (PARTITION BY tag_id ORDER BY timestamp DESC) as rn
        FROM tag_data
      ) td ON t.id = td.tag_id AND td.rn = 1
      WHERE t.location_id = ? AND t.is_active = TRUE
      ORDER BY t.tag_type, t.tag_name
    `;
    const tags = await executeQuery(tagsQuery, [locationId]);

    // Get pump statuses for this location
    const pumpQuery = `
      SELECT
        t.tag_name,
        CASE WHEN td.value > 0 THEN 1 ELSE 0 END as is_active,
        td.timestamp as last_updated,
        CASE
          WHEN t.tag_name LIKE 'PUMP_%_RFB' THEN
            CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(t.tag_name, '_', 2), '_', -1) AS UNSIGNED)
          WHEN t.tag_name LIKE 'LT_%_RFB' THEN
            CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(t.tag_name, '_', 2), '_', -1) AS UNSIGNED)
          WHEN t.tag_name = 'HT_RFB' THEN 1
          ELSE 1
        END as pump_number
      FROM tags t
      LEFT JOIN (
        SELECT tag_id, value, timestamp,
               ROW_NUMBER() OVER (PARTITION BY tag_id ORDER BY timestamp DESC) as rn
        FROM tag_data
      ) td ON t.id = td.tag_id AND td.rn = 1
      WHERE t.location_id = ? AND t.tag_type = 'PUMP_STATUS' AND t.is_active = 1
      ORDER BY pump_number, t.tag_name
    `;
    const pumpStatuses = await executeQuery(pumpQuery, [locationId]);

    const location = locations[0];
    location.tags = tags;
    location.pump_statuses = pumpStatuses.map(pump => ({
      pump_number: pump.pump_number || 1,
      tag_name: pump.tag_name,
      is_active: pump.is_active === 1,
      last_updated: pump.last_updated ? new Date(pump.last_updated * 1000).toISOString() : null
    }));

    res.json(location);
  } catch (error) {
    console.error('Error fetching location:', error);
    res.status(500).json({ error: 'Failed to fetch location' });
  }
});

// Create new location
router.post('/', async (req, res) => {
  try {
    const { 
      zone_id, 
      name, 
      description, 
      pump_count,
      location_type,
      operator_name,
      operator_number,
      supervisor_number,
      torrent_service_1,
      supervisor_name,
      torrent_contact
    } = req.body;

    if (!zone_id || !name) {
      return res.status(400).json({ error: 'Zone ID and location name are required' });
    }

    // Validate location_type enum
    const validLocationTypes = ['CSPS', 'SWPS', 'TSPS'];
    if (location_type && !validLocationTypes.includes(location_type)) {
      return res.status(400).json({ 
        error: 'Invalid location type. Must be one of: CSPS, SWPS, TSPS' 
      });
    }

    // Verify zone exists
    const zoneCheck = await executeQuery('SELECT id FROM zones WHERE id = ?', [zone_id]);
    if (zoneCheck.length === 0) {
      return res.status(400).json({ error: 'Invalid zone ID' });
    }

    const query = `
      INSERT INTO locations (
        zone_id, name, description, pump_count, location_type,
        operator_name, operator_number, supervisor_number,
        torrent_service_1, supervisor_name, torrent_contact
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    const result = await executeQuery(query, [
      zone_id, name, description || null, pump_count || 0, location_type || 'SWPS',
      operator_name || null, operator_number || null, supervisor_number || null,
      torrent_service_1 || null, supervisor_name || null, torrent_contact || null
    ]);

    res.status(201).json({
      id: result.insertId,
      zone_id,
      name,
      description,
      pump_count,
      location_type: location_type || 'SWPS',
      operator_name,
      operator_number,
      supervisor_number,
      torrent_service_1,
      supervisor_name,
      torrent_contact,
      message: 'Location created successfully'
    });
  } catch (error) {
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(400).json({ error: 'Location name already exists in this zone' });
    }
    console.error('Error creating location:', error);
    res.status(500).json({ error: 'Failed to create location' });
  }
});

// Update location
router.put('/:id', async (req, res) => {
  try {
    const locationId = req.params.id;
    const { 
      zone_id, 
      name, 
      description, 
      pump_count,
      location_type,
      operator_name,
      operator_number,
      supervisor_number,
      torrent_service_1,
      supervisor_name,
      torrent_contact
    } = req.body;

    if (!zone_id || !name) {
      return res.status(400).json({ error: 'Zone ID and location name are required' });
    }

    // Validate location_type enum
    const validLocationTypes = ['CSPS', 'SWPS', 'TSPS'];
    if (location_type && !validLocationTypes.includes(location_type)) {
      return res.status(400).json({ 
        error: 'Invalid location type. Must be one of: CSPS, SWPS, TSPS' 
      });
    }

    // Verify zone exists
    const zoneCheck = await executeQuery('SELECT id FROM zones WHERE id = ?', [zone_id]);
    if (zoneCheck.length === 0) {
      return res.status(400).json({ error: 'Invalid zone ID' });
    }

    const query = `
      UPDATE locations 
      SET zone_id = ?, name = ?, description = ?, pump_count = ?, location_type = ?,
          operator_name = ?, operator_number = ?, supervisor_number = ?,
          torrent_service_1 = ?, supervisor_name = ?, torrent_contact = ?
      WHERE id = ?
    `;
    const result = await executeQuery(query, [
      zone_id, name, description || null, pump_count || 0, location_type || 'SWPS',
      operator_name || null, operator_number || null, supervisor_number || null,
      torrent_service_1 || null, supervisor_name || null, torrent_contact || null,
      locationId
    ]);

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Location not found' });
    }

    res.json({ message: 'Location updated successfully' });
  } catch (error) {
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(400).json({ error: 'Location name already exists in this zone' });
    }
    console.error('Error updating location:', error);
    res.status(500).json({ error: 'Failed to update location' });
  }
});

// Delete location
router.delete('/:id', async (req, res) => {
  try {
    const locationId = req.params.id;

    // Check if location has tags
    const tagCheck = await executeQuery(
      'SELECT COUNT(*) as count FROM tags WHERE location_id = ?',
      [locationId]
    );

    if (tagCheck[0].count > 0) {
      return res.status(400).json({
        error: 'Cannot delete location with existing tags. Please delete tags first.'
      });
    }

    const query = 'DELETE FROM locations WHERE id = ?';
    const result = await executeQuery(query, [locationId]);

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Location not found' });
    }

    res.json({ message: 'Location deleted successfully' });
  } catch (error) {
    console.error('Error deleting location:', error);
    res.status(500).json({ error: 'Failed to delete location' });
  }
});



export default router;





