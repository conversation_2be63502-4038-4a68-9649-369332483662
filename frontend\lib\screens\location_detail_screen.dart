import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';

import '../models/zone.dart';
import '../providers/data_provider.dart';
import '../utils/app_theme.dart';

class LocationDetailScreen extends StatefulWidget {
  final int locationId;
  final dynamic zoneId; // Can be int or String for special zones

  const LocationDetailScreen({
    super.key,
    required this.locationId,
    this.zoneId,
  });

  @override
  State<LocationDetailScreen> createState() => _LocationDetailScreenState();
}

class _LocationDetailScreenState extends State<LocationDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    // Background refresh when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<DataProvider>(context, listen: false).refreshAllBackground();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<DataProvider>(
      builder: (context, dataProvider, child) {
        // Get real location data from backend
        final locationData =
            dataProvider.getLocationDataById(widget.locationId);

        if (locationData == null) {
          return Scaffold(
            appBar: AppBar(
              title: const Text('Location Not Found'),
              backgroundColor: AppTheme.primaryColor,
            ),
            body: const Center(
              child: Text('Location data not available'),
            ),
          );
        }

        // Get zone data for this location
        final zoneData = _getZoneData(locationData.zoneName ?? '');

        return Scaffold(
          appBar: PreferredSize(
            preferredSize: const Size.fromHeight(120),
            child: Container(
              decoration: BoxDecoration(
                gradient: AppTheme.primaryLinearGradient,
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryColor.withOpacity(0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: SafeArea(
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 4, vertical: 6),
                      child: Row(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.arrow_back,
                                color: Colors.white, size: 26),
                            onPressed: () {
                              // Navigate back to zone if zoneId is provided, otherwise go to home
                              if (widget.zoneId != null) {
                                context.go('/zone/${widget.zoneId}');
                              } else {
                                context.go('/home');
                              }
                            },
                          ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Location Name with Type Badge
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        locationData.locationName,
                                        style: const TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                          letterSpacing: 0.3,
                                          height: 1.1,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    // Location Type Badge
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                      decoration: BoxDecoration(
                                        color: _getLocationTypeColor(locationData.locationType),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: Colors.white.withOpacity(0.3),
                                          width: 1,
                                        ),
                                      ),
                                      child: Text(
                                        locationData.locationType.code,
                                        style: const TextStyle(
                                          fontSize: 11,
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          letterSpacing: 0.5,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 2),
                                Row(
                                  children: [
                                    const Icon(Icons.access_time,
                                        color: Colors.white70, size: 14),
                                    const SizedBox(width: 4),
                                    Text(
                                      locationData.formattedTimestamp,
                                      style: const TextStyle(
                                        fontSize: 12,
                                        color: Colors.white70,
                                        fontWeight: FontWeight.w500,
                                        height: 1.0,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Consumer<DataProvider>(
                            builder: (context, dataProvider, child) {
                              return Container(
                                margin: const EdgeInsets.only(right: 12),
                                child: IconButton(
                                  icon: dataProvider.isBackgroundRefreshing
                                      ? const SizedBox(
                                          width: 22,
                                          height: 22,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2.5,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                    Colors.white),
                                          ),
                                        )
                                      : const Icon(Icons.refresh,
                                          color: Colors.white, size: 26),
                                  onPressed: dataProvider.isBackgroundRefreshing
                                      ? null
                                      : () {
                                          dataProvider.refreshAllBackground();
                                        },
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.fromLTRB(16, 0, 16, 6),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: TabBar(
                        controller: _tabController,
                        indicatorColor: Colors.white,
                        indicatorWeight: 2,
                        labelColor: Colors.white,
                        unselectedLabelColor: Colors.white60,
                        labelStyle: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 13,
                        ),
                        unselectedLabelStyle: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 13,
                        ),
                        tabs: const [
                          Tab(text: 'Details'),
                          Tab(text: 'Contact'),
                          Tab(text: 'Zone Info'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          backgroundColor: AppTheme.backgroundColor,
          body: SafeArea(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildDetailsTab(locationData),
                _buildContactTab(locationData),
                _buildZonalInformationTab(locationData),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDetailsTab(LocationData locationData) {
    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
      child: Column(
        children: [
          // Real-time Data Cards
          Row(
            children: [
              Expanded(
                child: _buildDataCard(
                    'Date Time', locationData.formattedTimestamp),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildDataCard(
                    'Level (Mtr.)', locationData.level.toStringAsFixed(2)),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildDataCard('Flow Rate (m3/Hr)',
                    locationData.flowRate.toStringAsFixed(2)),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildDataCard('Current Day Qty (MLD)',
                    locationData.currentDayQty.toStringAsFixed(2)),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildDataCard('Previous Day Qty (MLD)',
                    locationData.previousDayQty.toStringAsFixed(2)),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildDataCard('Till Today Qty (MLD)',
                    locationData.tillTodayQty.toStringAsFixed(2)),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Pump Status Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: AppTheme.cardShadow,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.settings,
                        color: AppTheme.primaryColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Pump Status',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                locationData.pumpStatuses.isEmpty
                    ? Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: AppTheme.surfaceColor,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'No pumps configured',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                            fontStyle: FontStyle.italic,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      )
                    : Wrap(
                        spacing: 12,
                        runSpacing: 12,
                        children: () {
                          final sortedPumps =
                              List<PumpStatus>.from(locationData.pumpStatuses);
                          sortedPumps.sort(PumpStatus.compare);
                          return sortedPumps
                              .map((pumpStatus) => Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 8),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: pumpStatus.isActive
                                            ? AppTheme.successGradient
                                            : AppTheme.errorGradient,
                                      ),
                                      borderRadius: BorderRadius.circular(25),
                                      boxShadow: [
                                        BoxShadow(
                                          color: (pumpStatus.isActive
                                                  ? AppTheme.successColor
                                                  : AppTheme.errorColor)
                                              .withOpacity(0.3),
                                          blurRadius: 6,
                                          offset: const Offset(0, 3),
                                        ),
                                      ],
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          pumpStatus.isActive
                                              ? Icons.play_circle
                                              : Icons.stop_circle,
                                          color: Colors.white,
                                          size: 18,
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          pumpStatus.displayName,
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 14,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ))
                              .toList();
                        }(),
                      ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactTab(LocationData locationData) {
    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildContactCard(
                    'Operator Name', locationData.operatorName ?? '-'),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildContactCard(
                    'Operator No', locationData.operatorNumber ?? '-'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
              child: _buildContactCard('Torrent Service No 1',
                    locationData.torrentService1 ?? '-'),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildContactCard('Torrent Contact No',
                    locationData.torrentContact ?? '-'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                
                    child: _buildContactCard('SuperVisor Name',
                    locationData.supervisorName ?? '-'),
              
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildContactCard('Supervisor No',
                    locationData.supervisorNumber ?? '-'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildZonalInformationTab(LocationData locationData) {
    final zoneData = _getZoneData(locationData.zoneName ?? '');
    final zoneType = zoneData.zoneType;

    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Zone Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.only(bottom: 20),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${(locationData.zoneName ?? '').toUpperCase()} TOTALS',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // Total Current Day Qty Tile
          _buildZonalTile(
            'Total Current Day Qty',
            zoneData.formattedCurrentDayQty,
            Icons.today,
            AppTheme.primaryColor,
          ),

          const SizedBox(height: 16),

          // Total Previous Day Qty Tile
          _buildZonalTile(
            'Total Previous Day Qty',
            zoneData.formattedPreviousDayQty,
            Icons.history,
            AppTheme.accentColor,
          ),

          if (zoneType != 'critical' && zoneType != 'tsps') ...[
            const SizedBox(height: 16),
            // Total Till Today Qty Tile
            _buildZonalTile(
              'Total Till Today Qty',
              zoneData.formattedTillTodayQty,
              Icons.analytics,
              AppTheme.successColor,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildZonalTile(
      String title, String value, IconData icon, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.white,
        boxShadow: AppTheme.cardShadow,
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              color.withOpacity(0.08),
              color.withOpacity(0.03),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(14),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [color, color.withOpacity(0.8)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: color.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 26,
              ),
            ),
            const SizedBox(width: 18),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.secondaryTextColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: color,
                      letterSpacing: 0.5,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.trending_up,
                color: color,
                size: 20,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods for zone data
  ZoneData _getZoneData(String zoneName) {
    final dataProvider = Provider.of<DataProvider>(context, listen: false);
    final zoneData = dataProvider.getZoneDataByName(zoneName);

    if (zoneData != null) {
      return zoneData;
    }

    // Fallback if zone data not found
    return ZoneData(
      zoneId: 0,
      zoneName: zoneName,
      zoneType: 'regular',
      totalCurrentDayQty: 0.0,
      totalPreviousDayQty: 0.0,
      totalTillTodayQty: 0.0,
      lastUpdated: DateTime.now(),
      locationCount: 0,
    );
  }

  Widget _buildDataCard(String title, String value) {
    return Card(
      elevation: 2,
      child: Column(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: const BoxDecoration(
              color: AppTheme.primaryColor,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactCard(String title, String value) {
    return Card(
      elevation: 2,
      child: Column(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: const BoxDecoration(
              color: AppTheme.primaryColor,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Color _getLocationTypeColor(LocationType locationType) {
    switch (locationType) {
      case LocationType.csps:
        return Colors.orange.shade600; // Combined Sewer Pumping Station
      case LocationType.swps:
        return Colors.blue.shade600;   // Storm Water Pumping Station
      case LocationType.tsps:
        return Colors.green.shade600;  // Treatment/Transfer Sewage Pumping Station
    }
  }
}
