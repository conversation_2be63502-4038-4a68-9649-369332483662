import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';

import '../providers/data_provider.dart';

import '../models/zone.dart';
import '../utils/app_theme.dart';


class TagDetailScreen extends StatefulWidget {
  final int tagId;

  const TagDetailScreen({
    super.key,
    required this.tagId,
  });

  @override
  State<TagDetailScreen> createState() => _TagDetailScreenState();
}

class _TagDetailScreenState extends State<TagDetailScreen> {
  Tag? tag;
  List<TagData> history = [];
  bool isLoading = true;
  int selectedHours = 24;

  @override
  void initState() {
    super.initState();
    _loadTagData();
    _subscribeToUpdates();
  }

  @override
  void dispose() {
    _unsubscribeFromUpdates();
    super.dispose();
  }

  void _subscribeToUpdates() {
    // WebSocket functionality removed - using periodic refresh instead
  }

  void _unsubscribeFromUpdates() {
    // WebSocket functionality removed
  }

  Future<void> _loadTagData() async {
    setState(() => isLoading = true);

    final dataProvider = Provider.of<DataProvider>(context, listen: false);

    // Get tag details
    tag = await dataProvider.fetchTagDetails(widget.tagId);

    // Fetch tag history
    history = await dataProvider.fetchTagHistory(widget.tagId, hours: selectedHours);

    setState(() => isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Loading...'),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (tag == null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Tag Not Found'),
        ),
        body: const Center(
          child: Text('Tag not found'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(tag!.tagName),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadTagData,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadTagData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Tag Info Card
              _buildTagInfoCard(),

              const SizedBox(height: 24),

              // Current Value Card
              _buildCurrentValueCard(),

              const SizedBox(height: 24),

              // Chart Section
              _buildChartSection(),

              const SizedBox(height: 24),

              // Recent Data
              _buildRecentDataSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTagInfoCard() {
    final statusColor = AppTheme.getValueColor(
      tag!.latestValue,
      tag!.minValue,
      tag!.maxValue,
    );

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getTagIcon(tag!.tagType),
                    color: statusColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        tag!.tagName,
                        style: AppTheme.headingMedium,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${tag!.zoneName} - ${tag!.locationName}',
                        style: AppTheme.bodyMedium.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: tag!.hasValidValue
                        ? AppTheme.successColor.withOpacity(0.1)
                        : AppTheme.errorColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    tag!.hasValidValue ? 'Active' : 'No Data',
                    style: TextStyle(
                      color: tag!.hasValidValue
                          ? AppTheme.successColor
                          : AppTheme.errorColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),

            // Tag Details
            _buildDetailRow('Type', tag!.tagType.displayName),
            _buildDetailRow('OPC Address', tag!.opcAddress),
            _buildDetailRow('MQTT Topic', tag!.mqttTopic),
            if (tag!.unit != null)
              _buildDetailRow('Unit', tag!.unit!),
            if (tag!.minValue != null)
              _buildDetailRow('Min Value', '${tag!.minValue} ${tag!.unit ?? ''}'),
            if (tag!.maxValue != null)
              _buildDetailRow('Max Value', '${tag!.maxValue} ${tag!.unit ?? ''}'),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: AppTheme.bodySmall.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentValueCard() {
    final statusColor = AppTheme.getValueColor(
      tag!.latestValue,
      tag!.minValue,
      tag!.maxValue,
    );

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Text(
              'Current Value',
              style: AppTheme.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              tag!.displayValue,
              style: AppTheme.headingLarge.copyWith(
                color: statusColor,
                fontWeight: FontWeight.bold,
                fontSize: 36,
              ),
            ),
            if (tag!.latestTimestamp != null) ...[
              const SizedBox(height: 8),
              Text(
                'Updated ${_formatTimestamp(tag!.latestTimestamp!)}',
                style: AppTheme.caption,
              ),
            ],

            if (!tag!.isInRange && tag!.hasValidValue) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppTheme.errorColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: AppTheme.errorColor.withOpacity(0.3)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.warning,
                      size: 16,
                      color: AppTheme.errorColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Value out of range',
                      style: AppTheme.bodySmall.copyWith(
                        color: AppTheme.errorColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildChartSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Historical Data',
                  style: AppTheme.headingSmall,
                ),
                DropdownButton<int>(
                  value: selectedHours,
                  items: const [
                    DropdownMenuItem(value: 1, child: Text('1 Hour')),
                    DropdownMenuItem(value: 6, child: Text('6 Hours')),
                    DropdownMenuItem(value: 24, child: Text('24 Hours')),
                    DropdownMenuItem(value: 168, child: Text('7 Days')),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedHours = value;
                      });
                      _loadTagData();
                    }
                  },
                ),
              ],
            ),

            const SizedBox(height: 16),

            if (history.isEmpty)
              Container(
                height: 200,
                child: const Center(
                  child: Text('No historical data available'),
                ),
              )
            else
              Container(
                height: 200,
                child: LineChart(
                  LineChartData(
                    gridData: FlGridData(show: true),
                    titlesData: FlTitlesData(
                      leftTitles: AxisTitles(
                        sideTitles: SideTitles(showTitles: true),
                      ),
                      bottomTitles: AxisTitles(
                        sideTitles: SideTitles(
                          showTitles: true,
                          getTitlesWidget: (value, meta) {
                            final dateTime = DateTime.fromMillisecondsSinceEpoch(
                              value.toInt() * 1000,
                            );
                            return Text(
                              '${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}',
                              style: AppTheme.caption,
                            );
                          },
                        ),
                      ),
                      topTitles: AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                      rightTitles: AxisTitles(
                        sideTitles: SideTitles(showTitles: false),
                      ),
                    ),
                    borderData: FlBorderData(show: true),
                    lineBarsData: [
                      LineChartBarData(
                        spots: history.map((data) {
                          return FlSpot(
                            data.timestamp.toDouble(),
                            data.value,
                          );
                        }).toList(),
                        isCurved: true,
                        color: AppTheme.primaryColor,
                        barWidth: 2,
                        dotData: FlDotData(show: false),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentDataSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Data Points',
              style: AppTheme.headingSmall,
            ),

            const SizedBox(height: 16),

            if (history.isEmpty)
              const Center(
                child: Text('No recent data available'),
              )
            else
              ...history.take(10).map((data) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _formatFullTimestamp(data.timestamp),
                        style: AppTheme.bodySmall,
                      ),
                      Text(
                        '${data.value.toStringAsFixed(2)} ${tag!.unit ?? ''}',
                        style: AppTheme.bodySmall.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
          ],
        ),
      ),
    );
  }

  IconData _getTagIcon(TagType tagType) {
    switch (tagType) {
      case TagType.level:
        return Icons.water_drop;
      case TagType.pumpStatus:
        return Icons.settings;
      case TagType.flowRate:
        return Icons.speed;
      case TagType.totalizer:
        return Icons.analytics;
      default:
        return Icons.sensors;
    }
  }

  String _formatTimestamp(int timestamp) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  String _formatFullTimestamp(int timestamp) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return '${dateTime.day}/${dateTime.month} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
