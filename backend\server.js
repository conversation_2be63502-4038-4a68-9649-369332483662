import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import { createServer } from 'http';
import { Server } from 'socket.io';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import jwt from 'jsonwebtoken';
import fs from 'fs';
import crypto from 'crypto';

// Import services and routes
import { testConnection } from './config/database.js';
import MQTTService from './services/mqttService.js';
import authRouter from './routes/auth.js';
import appAuthRouter from './routes/app-auth.js';
import zonesRouter from './routes/zones.js';
import locationsRouter from './routes/locations.js';
import tagsRouter from './routes/tags.js';
import dataRouter from './routes/data.js';
import adminRouter from './routes/admin.js';
import appUsersRouter from './routes/app-users.js';
import adminProfileRouter from './routes/admin-profile.js';
import { verifyToken } from './middleware/auth.js';
import { authenticateAppUser } from './middleware/app-auth.js';

// ES6 module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config();

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Make io globally available for MQTT service
global.io = io;

const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Authentication routes (public)
app.use('/api/auth', authRouter);
app.use('/api/app-auth', appAuthRouter);

// Serve login page (public)
app.get('/admin/login.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'login.html'));
});

// Serve static files first (for CSS, JS, images)
app.use('/admin', express.static(path.join(__dirname, 'public')));

// Authentication check for admin root
app.get('/admin/', (req, res) => {
  const token = req.headers.authorization?.split(' ')[1] ||
                req.cookies?.auth_token ||
                req.query.token;

  if (!token) {
    return res.redirect('/admin/login.html');
  }

  try {
    jwt.verify(token, process.env.JWT_SECRET || 'storm_app_secret_key');
    return res.sendFile(path.join(__dirname, 'public', 'index.html'));
  } catch (error) {
    return res.redirect('/admin/login.html');
  }
});

app.get('/admin', (req, res) => {
  res.redirect('/admin/');
});

// Protected API Routes (Admin Panel)
app.use('/api/zones', verifyToken, zonesRouter);
app.use('/api/locations', verifyToken, locationsRouter);
app.use('/api/tags', verifyToken, tagsRouter);
app.use('/api/admin', verifyToken, adminRouter);
app.use('/api/app-users', verifyToken, appUsersRouter);
app.use('/api/admin-profile', verifyToken, adminProfileRouter);

// Debug: Log all registered routes (remove this after testing)
console.log('Registered admin routes:');
adminRouter.stack.forEach(layer => {
  if (layer.route) {
    console.log(`${Object.keys(layer.route.methods)} ${layer.route.path}`);
  }
});

// Protected API Routes (Mobile App)
app.use('/api/app/zones', authenticateAppUser, zonesRouter);
app.use('/api/app/locations', authenticateAppUser, locationsRouter);
app.use('/api/app/tags', authenticateAppUser, tagsRouter);
app.use('/api/app/data', authenticateAppUser, dataRouter);


// Public API Routes (for mobile app development/testing)
//app.use('/api/public/zones', zonesRouter);
//app.use('/api/public/locations', locationsRouter);
app.use('/api/public/tags', tagsRouter);
//app.use('/api/public/data', dataRouter);

// Shared data endpoint (for backward compatibility)
app.use('/api/data', verifyToken, dataRouter);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'MonsoonApp Backend',
    version: '1.0.0',
    /*endpoints: {
      admin: '/admin',
      api: {
        zones: '/api/zones',
        locations: '/api/locations',
        tags: '/api/tags',
        data: '/api/data'
      },
      health: '/health'
    }*/
  });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log(`📱 Client connected: ${socket.id}`);

  // Send welcome message
  socket.emit('connected', {
    message: 'Connected to StormApp real-time data',
    timestamp: new Date().toISOString()
  });

  // Handle client disconnection
  socket.on('disconnect', () => {
    console.log(`📱 Client disconnected: ${socket.id}`);
  });

  // Handle subscription to specific tags
  socket.on('subscribe_tag', (tagId) => {
    socket.join(`tag_${tagId}`);
    console.log(`📱 Client ${socket.id} subscribed to tag ${tagId}`);
  });

  // Handle unsubscription from specific tags
  socket.on('unsubscribe_tag', (tagId) => {
    socket.leave(`tag_${tagId}`);
    console.log(`📱 Client ${socket.id} unsubscribed from tag ${tagId}`);
  });
});

// Global services
let mqttService = null;

// Initialize services
const initializeServices = async () => {
  try {
    // Test database connection
    const dbConnected = await testConnection();
    if (!dbConnected) {
      console.error('❌ Failed to connect to database. Please check your configuration.');
      process.exit(1);
    }

    // Initialize MQTT service
    mqttService = new MQTTService();
    await mqttService.connect();

    console.log('✅ All services initialized successfully');

  } catch (error) {
    console.error('❌ Failed to initialize services:', error);
    process.exit(1);
  }
};

// Enhanced error handling middleware
app.use((err, req, res, next) => {
  const errorDetails = {
    message: err.message,
    stack: err.stack,
    code: err.code,
    status: err.status || 500,
    path: req.path,
    method: req.method,
    query: req.query,
    body: req.body,
    headers: req.headers,
    timestamp: new Date().toISOString()
  };
  
  console.error('❌ Unhandled error:', errorDetails);
  
  // Log to a file for IISNode
  const logPath = path.join(__dirname, 'logs');
  if (!fs.existsSync(logPath)) {
    fs.mkdirSync(logPath, { recursive: true });
  }
  
  fs.appendFileSync(
    path.join(logPath, 'error.log'),
    JSON.stringify(errorDetails) + '\n'
  );
  
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong',
    requestId: req.id || crypto.randomUUID()
  });
});

// Add request ID middleware
app.use((req, res, next) => {
  req.id = crypto.randomUUID();
  next();
});

// Add request logging middleware
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.url} ${res.statusCode} ${duration}ms`);
  });
  
  next();
});

// Handle 404
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: `Route ${req.originalUrl} not found`
  });
});

// Start server
const startServer = async () => {
  await initializeServices();

  server.listen(PORT, () => {
    console.log(`🚀 StormApp Backend running on port ${PORT}`);
    console.log(`📊 Admin interface: http://localhost:${PORT}/admin`);
    console.log(`🔌 API endpoints: http://localhost:${PORT}/api`);
    console.log(`📡 WebSocket server ready for real-time data`);
  });
};

// Graceful shutdown
let isShuttingDown = false;

const gracefulShutdown = async (signal) => {
  if (isShuttingDown) {
    console.log(`🛑 ${signal} received again, forcing exit...`);
    process.exit(1);
  }

  isShuttingDown = true;
  console.log(`🛑 ${signal} received, shutting down gracefully...`);

  try {
    // Disconnect MQTT service first
    if (mqttService) {
      mqttService.disconnect();
      console.log('✅ MQTT service disconnected');
    }

    // Close all socket connections
    if (global.io) {
      global.io.close();
      console.log('✅ Socket.IO connections closed');
    }

    // Close HTTP server
    await new Promise((resolve) => {
      server.close((err) => {
        if (err) {
          console.error('❌ Error closing server:', err);
        } else {
          console.log('✅ HTTP server closed');
        }
        resolve();
      });
    });

    console.log('✅ Graceful shutdown completed');
    process.exit(0);

  } catch (error) {
    console.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Enhanced uncaught exception handling
process.on('uncaughtException', (error) => {
  const errorDetails = {
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString()
  };
  
  console.error('❌ Uncaught Exception:', errorDetails);
  
  // Log to a file for IISNode
  const logPath = path.join(__dirname, 'logs');
  if (!fs.existsSync(logPath)) {
    fs.mkdirSync(logPath, { recursive: true });
  }
  
  fs.appendFileSync(
    path.join(logPath, 'uncaught-exceptions.log'),
    JSON.stringify(errorDetails) + '\n'
  );
  
  gracefulShutdown('UNCAUGHT_EXCEPTION');
});

process.on('unhandledRejection', (reason, promise) => {
  const errorDetails = {
    reason: reason instanceof Error ? { message: reason.message, stack: reason.stack } : reason,
    promise: String(promise),
    timestamp: new Date().toISOString()
  };
  
  console.error('❌ Unhandled Rejection:', errorDetails);
  
  // Log to a file for IISNode
  const logPath = path.join(__dirname, 'logs');
  if (!fs.existsSync(logPath)) {
    fs.mkdirSync(logPath, { recursive: true });
  }
  
  fs.appendFileSync(
    path.join(logPath, 'unhandled-rejections.log'),
    JSON.stringify(errorDetails) + '\n'
  );
  
  gracefulShutdown('UNHANDLED_REJECTION');
});

// Start the application
startServer().catch(error => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});
