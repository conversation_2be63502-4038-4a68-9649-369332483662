{"name": "storm-backend", "version": "1.0.0", "description": "Backend for StormApp - OPC/MQTT monitoring system", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-database.js"}, "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mqtt": "^5.3.5", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "socket.io": "^4.7.4"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["opc", "mqtt", "monitoring", "iot", "scada"], "author": "StormApp Team", "license": "MIT"}