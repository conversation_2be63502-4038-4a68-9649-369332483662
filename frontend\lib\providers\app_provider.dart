import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../config/app_config.dart';

class AppProvider extends ChangeNotifier {
  static const String _themeModeKey = 'theme_mode';
  static const String _serverUrlKey = 'server_url';
  static const String _autoRefreshKey = 'auto_refresh';
  static const String _refreshIntervalKey = 'refresh_interval';

  ThemeMode _themeMode = ThemeMode.system;
  String _serverUrl = AppConfig.getDefaultServerUrl();
  bool _autoRefresh = AppConfig.defaultAutoRefresh;
  int _refreshInterval = AppConfig.defaultRefreshInterval;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  ThemeMode get themeMode => _themeMode;
  String get serverUrl => _serverUrl;
  bool get autoRefresh => _autoRefresh;
  int get refreshInterval => _refreshInterval;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  AppProvider() {
    _loadSettings();
  }

  // Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load theme mode
      final themeModeIndex = prefs.getInt(_themeModeKey) ?? 0;
      _themeMode = ThemeMode.values[themeModeIndex];

      // Load server URL
      _serverUrl = prefs.getString(_serverUrlKey) ?? AppConfig.getDefaultServerUrl();

      // Load auto refresh setting
      _autoRefresh = prefs.getBool(_autoRefreshKey) ?? AppConfig.defaultAutoRefresh;

      // Load refresh interval
      _refreshInterval = prefs.getInt(_refreshIntervalKey) ?? AppConfig.defaultRefreshInterval;

      notifyListeners();
    } catch (e) {
      _setError('Failed to load settings: $e');
    }
  }

  // Save settings to SharedPreferences
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setInt(_themeModeKey, _themeMode.index);
      await prefs.setString(_serverUrlKey, _serverUrl);
      await prefs.setBool(_autoRefreshKey, _autoRefresh);
      await prefs.setInt(_refreshIntervalKey, _refreshInterval);
    } catch (e) {
      _setError('Failed to save settings: $e');
    }
  }

  // Theme management
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode != mode) {
      _themeMode = mode;
      notifyListeners();
      await _saveSettings();
    }
  }

  // Server URL management
  Future<void> setServerUrl(String url) async {
    if (_serverUrl != url) {
      _serverUrl = url.endsWith('/') ? url.substring(0, url.length - 1) : url;
      notifyListeners();
      await _saveSettings();
    }
  }

  // Auto refresh management
  Future<void> setAutoRefresh(bool enabled) async {
    if (_autoRefresh != enabled) {
      _autoRefresh = enabled;
      notifyListeners();
      await _saveSettings();
    }
  }

  // Refresh interval management
  Future<void> setRefreshInterval(int seconds) async {
    if (_refreshInterval != seconds && seconds > 0) {
      _refreshInterval = seconds;
      notifyListeners();
      await _saveSettings();
    }
  }

  // Loading state management
  void setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  // Error management
  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  void clearError() {
    _setError(null);
  }

  // API URL helpers
  String get apiUrl => AppConfig.buildApiUrl(_serverUrl);


  // Validation
  bool isValidServerUrl(String url) {
    return AppConfig.isValidServerUrl(url);
  }

  // Build endpoint URL
  String buildEndpointUrl(String endpoint) {
    return AppConfig.buildEndpointUrl(_serverUrl, endpoint);
  }

  // Reset to defaults
  Future<void> resetToDefaults() async {
    _themeMode = ThemeMode.system;
    _serverUrl = AppConfig.getDefaultServerUrl();
    _autoRefresh = AppConfig.defaultAutoRefresh;
    _refreshInterval = AppConfig.defaultRefreshInterval;

    notifyListeners();
    await _saveSettings();
  }

  // Get predefined server configurations
  List<ServerConfig> get predefinedServers => AppConfig.predefinedServers;

  // App info
  String get appVersion => AppConfig.appVersion;
  String get appName => AppConfig.appName;
  String get appDescription => 'Real-time Monsoon Pumping monitoring application for AMC';
}
