import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/data_provider.dart';
import '../utils/app_theme.dart';

class BackgroundRefreshIndicator extends StatelessWidget {
  final Widget child;
  final bool showIndicator;

  const BackgroundRefreshIndicator({
    super.key,
    required this.child,
    this.showIndicator = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!showIndicator) {
      return child;
    }

    return Consumer<DataProvider>(
      builder: (context, dataProvider, _) {
        return Stack(
          children: [
            child,
            if (dataProvider.isBackgroundRefreshing)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 3,
                  child: const LinearProgressIndicator(
                    backgroundColor: Colors.transparent,
                    valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

class SubtleRefreshIndicator extends StatelessWidget {
  final bool isRefreshing;
  final String? lastUpdateText;

  const SubtleRefreshIndicator({
    super.key,
    required this.isRefreshing,
    this.lastUpdateText,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: isRefreshing ? 24 : 0,
      child: isRefreshing
          ? Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 12,
                    height: 12,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Updating...',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            )
          : const SizedBox.shrink(),
    );
  }
}

class LastUpdateIndicator extends StatelessWidget {
  final DateTime? lastUpdate;

  const LastUpdateIndicator({
    super.key,
    this.lastUpdate,
  });

  @override
  Widget build(BuildContext context) {
    if (lastUpdate == null) {
      return const SizedBox.shrink();
    }

    final now = DateTime.now();
    final difference = now.difference(lastUpdate!);
    String timeText;

    if (difference.inSeconds < 60) {
      timeText = 'Just now';
    } else if (difference.inMinutes < 60) {
      timeText = '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      timeText = '${difference.inHours}h ago';
    } else {
      timeText = '${difference.inDays}d ago';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.access_time,
            size: 12,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 4),
          Text(
            'Updated $timeText',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
