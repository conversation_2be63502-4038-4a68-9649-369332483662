import express from 'express';
import { executeQuery } from '../config/database.js';
import bcrypt from 'bcryptjs';

const router = express.Router();

// Change admin user's own password
router.put('/password', async (req, res) => {
  try {
    const { new_password, current_password } = req.body;
    const adminUserId = req.user.id; // Get from JWT token
    
    if (!new_password || new_password.length < 6) {
      return res.status(400).json({ error: 'New password must be at least 6 characters long' });
    }
    
    // Current password is always required for admin users
    if (!current_password) {
      return res.status(400).json({ error: 'Current password is required' });
    }
    
    // Verify current password
    const userQuery = 'SELECT password_hash FROM admin_users WHERE id = ?';
    const users = await executeQuery(userQuery, [adminUserId]);
    
    if (users.length === 0) {
      return res.status(404).json({ error: 'Admin user not found' });
    }
    
    const isCurrentPasswordValid = await bcrypt.compare(current_password, users[0].password_hash);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({ error: 'Current password is incorrect' });
    }
    
    // Hash the new password
    const saltRounds = 10;
    const password_hash = await bcrypt.hash(new_password, saltRounds);
    
    const query = `
      UPDATE admin_users 
      SET password_hash = ?, updated_at = NOW()
      WHERE id = ?
    `;
    
    await executeQuery(query, [password_hash, adminUserId]);
    
    res.json({ success: true, message: 'Admin password updated successfully' });
  } catch (error) {
    console.error('Error updating admin password:', error);
    res.status(500).json({ error: 'Failed to update admin password' });
  }
});

// Get current admin user info
router.get('/profile', async (req, res) => {
  try {
    const adminUserId = req.user.id;
    
    const query = `
      SELECT 
        id,
        username,
        email,
        is_active,
        created_at,
        updated_at
      FROM admin_users
      WHERE id = ?
    `;
    
    const users = await executeQuery(query, [adminUserId]);
    
    if (users.length === 0) {
      return res.status(404).json({ error: 'Admin user not found' });
    }
    
    res.json(users[0]);
  } catch (error) {
    console.error('Error fetching admin profile:', error);
    res.status(500).json({ error: 'Failed to fetch admin profile' });
  }
});

export default router;