# StormApp - OPC/MQTT Monitoring System

A comprehensive monitoring system for OPC/MQTT data with a Node.js backend, HTML admin interface, and Flutter mobile app.

## System Architecture

```
OPC Server → MQTT Broker → Node.js Backend → MySQL Database
                                ↓
                        HTML Admin Interface
                                ↓
                        Flutter Mobile App
```

## Features

### Backend (Node.js)
- MQTT client for receiving OPC data
- REST API for data management
- Real-time WebSocket communication
- MySQL database for data storage
- HTML admin interface for configuration

### Mobile App (Flutter)
- Real-time data monitoring
- Zone and location-based navigation
- Tag detail views with historical charts
- Alert notifications
- Dark/Light theme support

### Admin Interface (HTML)
- Zone, location, and tag management
- Real-time data monitoring
- Dashboard with statistics
- System configuration

## Data Flow

1. **OPC Address Format**: `ns=2;s=AKHBAR NAGAR.5 PUMP.LEVEL_1`
2. **MQTT Topic**: `AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/LEVEL_1`
3. **JSON Data**: `{"time":1748370722,"AKHBAR NAGAR.5 PUMP.LEVEL_1":"0.906"}`

## Setup Instructions

### Prerequisites

- Node.js (v16 or higher)
- MySQL Server
- Flutter SDK (for mobile app)
- MQTT Broker (e.g., Mosquitto)

### Backend Setup

1. **Navigate to backend directory**:
   ```bash
   cd backend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Configure environment**:
   ```bash
   # Edit .env file with your database and MQTT settings
   ```

4. **Initialize database** (creates zones, locations, and tags from CSV):
   ```bash
   npm run init-db
   ```

5. **Start the server**:
   ```bash
   npm start
   # or for development
   npm run dev
   ```

6. **Access admin interface**:
   Open `http://localhost:3000/admin` in your browser

### Mobile App Setup

1. **Navigate to frontend directory**:
   ```bash
   cd frontend
   ```

2. **Install Flutter dependencies**:
   ```bash
   flutter pub get
   ```

3. **Run the app**:
   ```bash
   # For Android
   flutter run

   # For iOS
   flutter run -d ios
   ```

### Database Schema

The system creates the following tables:

- **zones**: Zone definitions
- **locations**: Location definitions within zones
- **tags**: Tag configurations with OPC addresses
- **tag_data**: Real-time tag values with timestamps
- **admin_users**: Admin user accounts

### API Endpoints

#### Zones
- `GET /api/zones` - Get all zones
- `POST /api/zones` - Create new zone
- `PUT /api/zones/:id` - Update zone
- `DELETE /api/zones/:id` - Delete zone

#### Locations
- `GET /api/locations` - Get all locations
- `GET /api/locations/zone/:zoneId` - Get locations by zone
- `POST /api/locations` - Create new location
- `PUT /api/locations/:id` - Update location
- `DELETE /api/locations/:id` - Delete location

#### Tags
- `GET /api/tags` - Get all tags
- `GET /api/tags/location/:locationId` - Get tags by location
- `POST /api/tags` - Create new tag
- `PUT /api/tags/:id` - Update tag
- `DELETE /api/tags/:id` - Delete tag

#### Data
- `GET /api/data/latest` - Get latest data for all tags
- `GET /api/data/history/:tagId` - Get historical data for tag
- `GET /api/data/dashboard` - Get dashboard statistics

### Configuration

#### Environment Variables (.env)

```env
# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=storm_db

# MQTT Configuration
MQTT_BROKER=mqtt://localhost:1883
MQTT_TOPIC_PREFIX=AMC/Storm/amcstorm

# Server Configuration
PORT=3000
JWT_SECRET=your_jwt_secret_key_here

# Admin Configuration
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
```

#### MQTT Topic Structure

The system expects MQTT topics in the format:
```
AMC/Storm/amcstorm/{LOCATION}/{PUMP_INFO}/{TAG_NAME}
```

Example:
```
AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/LEVEL_1
```

#### OPC Address Mapping

OPC addresses are converted to MQTT topics:
- OPC: `ns=2;s=AKHBAR NAGAR.5 PUMP.LEVEL_1`
- MQTT: `AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/LEVEL_1`

### Sample Data

The system includes sample data for testing:

#### Zones
- North Zone
- South Zone
- East Zone
- West Zone
- South West Zone

#### Locations (Examples from CSV)
- AKHBAR NAGAR (West Zone, 5 pumps)
- SHREENAND NAGAR (South West Zone, 5 pumps)
- ODHAV AMBIKANAGAR (East Zone, 3 pumps)
- PALDI (West Zone, 9 pumps)
- VASTRAL (East Zone, 4 pumps)
- KUBERNAGAR (North Zone, 4 pumps)

The system automatically imports all locations from the provided OPC tag list and maps them to appropriate zones based on the zone mapping.

**Complete Location List (from actual CSV data):**
- **South West Zone**: SHREENAND NAGAR
- **West Zone**: JAYDEEP TOWER, AKHBAR NAGAR, DANI LIMDA, PIRANA NEW, PIRANA TERMINAL, VEJALPUR, VINZOAL TERMINAL
- **South Zone**: VYAYAMSHALA, GEBANSHAPIR, NOOR NAGAR, DEVIMATA, BHAIRAVNATH, NIRMANALA, AVAKAR HALL, SURELIYA, MATLA CIRCLE, GURUJI BRIDGE, SMRUTI MANDIR, NAVANA VATVA, NAVANAVATAVA, POOJA FARM, ARBUDA NAGAR
- **East Zone**: ODHAV AMBIKANAGAR, VASTRAL, VIRATNAGAR, ARBUDANAGAR, ODHAVFIRE STATION, HARIVILLA, JAY CHEMICAL, NEW NIKOL, NIKOL, ODHAV 100 MLD
- **North Zone**: NAVYUG, BAPA SITARAM MADHULI, KUBERNAGAR, KUBER NAGAR, RAJIV PARK, MALEKSBAN, TRIKAMLAL, NARODA HANSPURA, NARODA GAYATRI, DEHGAM ROAD, GAYTRINAGAR, JAMALPUR, NEW CHAMANPURA, NEW GOMTIPUR, TRIKAMPURA

**Total**: 44 locations with 400+ OPC tags automatically imported and categorized.

### Mobile App Features

#### Dashboard
- System statistics
- Zone overview
- Recent activity
- Alert notifications

#### Zone Management
- Browse zones and locations
- View location details
- Navigate to tag monitoring

#### Tag Monitoring
- Real-time value display
- Historical charts
- Alert status
- Value range validation

#### Settings
- Server URL configuration
- Theme selection
- Auto-refresh settings
- Connection status

### Admin Interface Features

#### Dashboard
- System overview
- Zone statistics
- Recent alerts
- Real-time data monitor

#### Management
- Zone CRUD operations
- Location CRUD operations
- Tag CRUD operations
- Real-time data viewing

### Troubleshooting

#### Common Issues

1. **Database Connection Failed**
   - Check MySQL server is running
   - Verify database credentials in .env
   - Ensure database exists

2. **MQTT Connection Failed**
   - Check MQTT broker is running
   - Verify broker URL in .env
   - Check network connectivity

3. **Mobile App Connection Issues**
   - Verify server URL in app settings
   - Check backend server is running
   - Ensure WebSocket port is accessible

4. **No Data Appearing**
   - Check MQTT topics match expected format
   - Verify tags are configured correctly
   - Check OPC address mapping

### Development

#### Backend Development
```bash
cd backend
npm run dev  # Starts with nodemon for auto-restart
```

#### Mobile App Development
```bash
cd frontend
flutter run  # Hot reload enabled
```

#### Database Reset
```bash
cd backend
npm run init-db  # Recreates all tables, zones, locations, and imports tags from CSV
```

#### Automatic Setup Process
The `npm run init-db` command performs a complete setup:

1. **Creates Database Schema**: All required tables (zones, locations, tags, tag_data)
2. **Zone Creation**: Creates 5 zones based on your site list
3. **Location Import**: Reads CSV file and creates locations with proper zone mapping
4. **Tag Import**: Automatically imports all OPC tags from CSV with:
   - **Smart Zone Mapping**: Locations mapped to zones based on provided zone list
   - **Tag Type Detection**: Tags categorized (LEVEL, PUMP_STATUS, FLOW_RATE, TOTALIZER)
   - **MQTT Topic Generation**: OPC addresses converted to MQTT topics
   - **Value Range Setting**: Appropriate min/max values set based on tag type

**Zone Mapping Rules**:
- **North**: NARODA, KUBER, GAYATRI, NAVYUG, BAPA SITARAM, TRIKAMLAL, DEHGAM, MALEKSBAN, PUSPKUNJ
- **South**: VYAYAMSHALA, GEBANSHA, NOOR NAGAR, DEVIMATA, BHAIRAVNATH, NIRMANALA, AVAKAR, SURELIYA, MATLA, GURUJI, SMRUTI, NAVANA VATVA, POOJA FARM
- **East**: ODHAV, AMBIKA, NIKOL, VASTRAL, VIRAT, ARBUDANAGAR, HARIVILLA, JAY CHEMICAL
- **West**: JAYDEEP, AKHBAR NAGAR, PALDI
- **South West**: SHREENAND NAGAR

**One Command Setup**: Everything is configured with a single `npm run init-db` command!

### Production Deployment

1. **Backend**:
   - Use PM2 or similar process manager
   - Configure reverse proxy (nginx)
   - Set up SSL certificates
   - Configure firewall rules

2. **Mobile App**:
   - Build release APK/IPA
   - Configure production server URLs
   - Test on target devices

3. **Database**:
   - Set up regular backups
   - Configure proper user permissions
   - Monitor performance

### Support

For issues and questions:
1. Check the troubleshooting section
2. Review logs in backend console
3. Check mobile app debug output
4. Verify MQTT message format

### License

This project is licensed under the MIT License.