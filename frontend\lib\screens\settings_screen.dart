import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../config/app_config.dart';
import '../providers/app_provider.dart';
import '../providers/auth_provider.dart';
import '../utils/app_theme.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _serverUrlController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    final appProvider = Provider.of<AppProvider>(context, listen: false);
    _serverUrlController.text = appProvider.serverUrl;
  }

  @override
  void dispose() {
    _serverUrlController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: Consumer<AppProvider>(
        builder: (context, appProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Connection Settings
              _buildSectionCard(
                title: 'Connection Settings',
                icon: Icons.wifi,
                children: [
                  Form(
                    key: _formKey,
                    child: TextFormField(
                      controller: _serverUrlController,
                      decoration: InputDecoration(
                        labelText: 'Server URL',
                        hintText: AppConfig.getDefaultServerUrl(),
                        border: const OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a server URL';
                        }
                        if (!appProvider.isValidServerUrl(value)) {
                          return 'Please enter a valid URL';
                        }
                        return null;
                      },
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Predefined Servers
                  const Text(
                    'Quick Select:',
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: appProvider.predefinedServers.map((server) {
                      final isSelected = _serverUrlController.text == server.url;
                      return FilterChip(
                        label: Text(server.name),
                        selected: isSelected,
                        onSelected: (selected) {
                          if (selected) {
                            _serverUrlController.text = server.url;
                          }
                        },
                        tooltip: '${server.description}\n${server.url}',
                      );
                    }).toList(),
                  ),

                  const SizedBox(height: 16),

                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => _saveServerUrl(appProvider),
                      child: const Text('Save Settings'),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // App Settings
              _buildSectionCard(
                title: 'App Settings',
                icon: Icons.settings,
                children: [
                  // Theme Setting
                  ListTile(
                    contentPadding: EdgeInsets.zero,
                    leading: const Icon(Icons.palette),
                    title: const Text('Theme'),
                    subtitle: Text(_getThemeModeText(appProvider.themeMode)),
                    trailing: DropdownButton<ThemeMode>(
                      value: appProvider.themeMode,
                      items: const [
                        DropdownMenuItem(
                          value: ThemeMode.system,
                          child: Text('System'),
                        ),
                        DropdownMenuItem(
                          value: ThemeMode.light,
                          child: Text('Light'),
                        ),
                        DropdownMenuItem(
                          value: ThemeMode.dark,
                          child: Text('Dark'),
                        ),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          appProvider.setThemeMode(value);
                        }
                      },
                    ),
                  ),

                  const Divider(),

                  // Auto Refresh Setting
                  SwitchListTile(
                    contentPadding: EdgeInsets.zero,
                    secondary: const Icon(Icons.refresh),
                    title: const Text('Auto Refresh'),
                    subtitle: const Text('Automatically refresh data'),
                    value: appProvider.autoRefresh,
                    onChanged: (value) {
                      appProvider.setAutoRefresh(value);
                    },
                  ),

                  if (appProvider.autoRefresh) ...[
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(Icons.timer, size: 20),
                        const SizedBox(width: 16),
                        const Text('Refresh Interval'),
                        const Spacer(),
                        DropdownButton<int>(
                          value: appProvider.refreshInterval,
                          items: const [
                            DropdownMenuItem(value: 1, child: Text('1s')),
                            DropdownMenuItem(value: 5, child: Text('5s')),
                            DropdownMenuItem(value: 10, child: Text('10s')),
                            DropdownMenuItem(value: 30, child: Text('30s')),
                            DropdownMenuItem(value: 60, child: Text('1m')),
                          ],
                          onChanged: (value) {
                            if (value != null) {
                              appProvider.setRefreshInterval(value);
                            }
                          },
                        ),
                      ],
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 16),

              // Account Section
              Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  if (authProvider.isAuthenticated) {
                    return Column(
                      children: [
                        _buildSectionCard(
                          title: 'Account',
                          icon: Icons.account_circle,
                          children: [
                            _buildInfoRow('Username', authProvider.user?.username ?? 'Unknown'),
                            _buildInfoRow('Role', authProvider.user?.role ?? 'Unknown'),
                            if (authProvider.user?.fullName != null)
                              _buildInfoRow('Full Name', authProvider.user!.fullName!),
                            if (authProvider.user?.email != null)
                              _buildInfoRow('Email', authProvider.user!.email!),

                            const SizedBox(height: 16),

                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: () => _logout(authProvider),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppTheme.errorColor,
                                  foregroundColor: Colors.white,
                                ),
                                icon: const Icon(Icons.logout),
                                label: const Text('Logout'),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                      ],
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),

              // About Section
              _buildSectionCard(
                title: 'About',
                icon: Icons.info,
                children: [
                  _buildInfoRow('App Name', appProvider.appName),
                  _buildInfoRow('Version', appProvider.appVersion),
                  _buildInfoRow('Description', appProvider.appDescription),

                  const SizedBox(height: 16),

                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => _showAboutDialog(context, appProvider),
                          child: const Text('About App'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => _resetSettings(appProvider),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppTheme.errorColor,
                            side: const BorderSide(color: AppTheme.errorColor),
                          ),
                          child: const Text('Reset Settings'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppTheme.primaryColor),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: AppTheme.headingSmall,
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: AppTheme.bodySmall.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  String _getThemeModeText(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.system:
        return 'Follow system setting';
      case ThemeMode.light:
        return 'Light theme';
      case ThemeMode.dark:
        return 'Dark theme';
    }
  }

  Future<void> _saveServerUrl(AppProvider appProvider) async {
    if (_formKey.currentState!.validate()) {
      await appProvider.setServerUrl(_serverUrlController.text.trim());

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Server URL saved successfully'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    }
  }

  void _showAboutDialog(BuildContext context, AppProvider appProvider) {
    showAboutDialog(
      context: context,
      applicationName: appProvider.appName,
      applicationVersion: appProvider.appVersion,
      applicationIcon: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          color: AppTheme.primaryColor,
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Icon(
          Icons.water_drop,
          color: Colors.white,
          size: 32,
        ),
      ),
      children: [
        Text(appProvider.appDescription),
        const SizedBox(height: 16),
        const Text(
          'This app provides real-time monitoring of Monsoon Pumping data for AMC',
        ),
      ],
    );
  }

  Future<void> _resetSettings(AppProvider appProvider) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text(
          'Are you sure you want to reset all settings to their default values?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.errorColor,
            ),
            child: const Text('Reset'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await appProvider.resetToDefaults();
      _serverUrlController.text = appProvider.serverUrl;

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Settings reset to defaults'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    }
  }

  Future<void> _logout(AuthProvider authProvider) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.errorColor,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await authProvider.logout();

      if (mounted) {
        // Navigation will be handled automatically by the router
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Logged out successfully'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    }
  }
}
