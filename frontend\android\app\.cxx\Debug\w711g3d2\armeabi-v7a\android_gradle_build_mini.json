{"buildFiles": ["D:\\Dvlpmnt\\flutterEdu\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Development\\StroamApp\\frontend\\android\\app\\.cxx\\Debug\\w711g3d2\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Development\\StroamApp\\frontend\\android\\app\\.cxx\\Debug\\w711g3d2\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}