import mqtt from 'mqtt';
import dotenv from 'dotenv';
import { executeQuery } from '../config/database.js';

dotenv.config();

class MQTTService {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.brokerUrl = process.env.MQTT_BROKER || 'mqtt://localhost:1883';
    this.topicPrefix = process.env.MQTT_TOPIC_PREFIX || 'AMC/Storm/amcstorm';
    this.username = process.env.MQTT_USERNAME || 'admin';
    this.password = process.env.MQTT_PASSWORD || 'admin123';
    this.cleanupInterval = null;
    this.lastCleanupTime = 0;
  }

  async connect() {
    try {
      this.client = mqtt.connect(this.brokerUrl, {
        username: this.username,
        password: this.password,
        clientId: `storm_app_${Math.random().toString(16).slice(3)}`
      });

      this.client.on('connect', () => {
        console.log('✅ MQTT Client connected');
        this.isConnected = true;
        this.subscribeToTopics();
        this.startPeriodicCleanup();
      });

      this.client.on('message', (topic, message) => {
        this.handleMessage(topic, message);
      });

      this.client.on('error', (error) => {
        console.error('❌ MQTT Error:', error);
        this.isConnected = false;
      });

      this.client.on('close', () => {
        console.log('🔌 MQTT Connection closed');
        this.isConnected = false;
      });

    } catch (error) {
      console.error('❌ MQTT Connection failed:', error);
    }
  }

  async subscribeToTopics() {
    try {
      // Subscribe to all topics under the prefix
      const wildcardTopic = `${this.topicPrefix}/+/+/+`;
      this.client.subscribe(wildcardTopic, (err) => {
        if (err) {
          console.error('❌ MQTT Subscription failed:', err);
        } else {
          console.log(`✅ Subscribed to MQTT topic: ${wildcardTopic}`);
        }
      });
    } catch (error) {
      console.error('❌ Error subscribing to topics:', error);
    }
  }

  async handleMessage(topic, message) {
    try {
      const data = JSON.parse(message.toString());
     // console.log(`📨 Received MQTT message on ${topic}:`, data);

      // Extract location and tag info from topic
      // Topic format: AMC/Storm/amcstorm/LOCATION/PUMP_INFO/TAG_NAME
      const topicParts = topic.split('/');
      if (topicParts.length >= 6) {
        const location = topicParts[3];
        const pumpInfo = topicParts[4];
        const tagName = topicParts[5];

        // Create OPC address format
        const opcAddress = `ns=2;s=${location}.${pumpInfo}.${tagName}`;

        // Find the tag in database with zone and location information
        const tagQuery = `
          SELECT t.id, t.tag_name, t.unit, l.name as location_name, z.name as zone_name
          FROM tags t
          JOIN locations l ON t.location_id = l.id
          JOIN zones z ON l.zone_id = z.id
          WHERE t.opc_address = ? AND t.is_active = TRUE
        `;

        const tagResults = await executeQuery(tagQuery, [opcAddress]);

        if (tagResults.length > 0) {
          const tag = tagResults[0];

          // Extract value and timestamp from data
          const timestamp = data.time || Math.floor(Date.now() / 1000);
          const tagKey = Object.keys(data).find(key => key !== 'time');
          const value = data[tagKey];

          if (value !== undefined) {
            // Store the data
            await this.storeTagData(tag.id, value, timestamp);

            // Emit to WebSocket clients if needed
            this.emitToClients(tag.id, {
              tagId: tag.id,
              tagName: tag.tag_name,
              zoneName: tag.zone_name,
              locationName: tag.location_name,
              value: value,
              unit: tag.unit,
              timestamp: timestamp,
              topic: topic
            });
          }
        } else {
          console.log(`⚠️ Tag not found for OPC address: ${opcAddress}`);
        }
      }
    } catch (error) {
      console.error('❌ Error handling MQTT message:', error);
    }
  }

  async storeTagData(tagId, value, timestamp) {
    try {
      // Always store the new reading
      const insertQuery = `
        INSERT INTO tag_data (tag_id, value, timestamp)
        VALUES (?, ?, ?)
      `;
      await executeQuery(insertQuery, [tagId, value, timestamp]);

      // Trigger periodic cleanup if enough time has passed (every 10 minutes)
      const now = Math.floor(Date.now() / 1000);
      if (now - this.lastCleanupTime > 600) { // 10 minutes
        this.triggerDataCleanup();
        this.lastCleanupTime = now;
      }

    } catch (error) {
      console.error('❌ Error storing tag data:', error);
    }
  }

  emitToClients(tagId, data) {
    // This will be connected to Socket.IO in the main server
    if (global.io) {
      global.io.emit('tagData', data);
      global.io.emit(`tag_${tagId}`, data);
    }
  }

  startPeriodicCleanup() {
    // Run cleanup every 30 minutes
    this.cleanupInterval = setInterval(() => {
      this.triggerDataCleanup();
    }, 30 * 60 * 1000); // 30 minutes

    console.log('🧹 Periodic data cleanup started (every 30 minutes)');
  }

  async triggerDataCleanup() {
    try {
      console.log('🧹 Starting data cleanup...');
      const currentTimestamp = Math.floor(Date.now() / 1000);

      // Get all unique tag IDs with their types
      const tagsQuery = `
        SELECT DISTINCT t.id, t.tag_type
        FROM tags t
        INNER JOIN tag_data td ON t.id = td.tag_id
      `;
      const tags = await executeQuery(tagsQuery);

      let cleanedCount = 0;
      for (const tag of tags) {
        const cleaned = await this.applyDataRetentionRules(tag.id, tag.tag_type, currentTimestamp);
        cleanedCount += cleaned;
      }

      console.log(`🧹 Data cleanup completed. Cleaned ${cleanedCount} records from ${tags.length} tags.`);

    } catch (error) {
      console.error('❌ Error during data cleanup:', error);
    }
  }

  async applyDataRetentionRules(tagId, tagType, currentTimestamp) {
    try {
      const oneDayAgo = currentTimestamp - (24 * 60 * 60); // 24 hours ago
      let deletedCount = 0;

      // Rule 1: Always delete readings older than 24 hours
      const deleteOldQuery = `
        DELETE FROM tag_data
        WHERE tag_id = ? AND timestamp < ?
      `;
      const oldResult = await executeQuery(deleteOldQuery, [tagId, oneDayAgo]);
      deletedCount += oldResult.affectedRows || 0;

      // Rule 2: For non-PUMP_STATUS tags, apply 5-minute interval cleanup
      if (tagType !== 'PUMP_STATUS') {
        // Get all readings for this tag within the last 24 hours, ordered by timestamp
        const readingsQuery = `
          SELECT id, timestamp, value
          FROM tag_data
          WHERE tag_id = ? AND timestamp >= ?
          ORDER BY timestamp ASC
        `;
        const readings = await executeQuery(readingsQuery, [tagId, oneDayAgo]);

        if (readings.length > 2) { // Keep at least 2 readings
          const idsToDelete = [];
          let lastKeptTimestamp = readings[0].timestamp;
          let lastKeptValue = readings[0].value;

          // Always keep the first and last readings
          for (let i = 1; i < readings.length - 1; i++) {
            const current = readings[i];
            const timeDiff = current.timestamp - lastKeptTimestamp;
            const valueDiff = Math.abs(current.value - lastKeptValue);

            // Delete if:
            // 1. Time difference < 5 minutes AND
            // 2. Value didn't change significantly (< 1% or < 0.01 absolute)
            if (timeDiff < 300 && valueDiff < Math.max(0.01, Math.abs(lastKeptValue) * 0.01)) {
              idsToDelete.push(current.id);
            } else {
              // Keep this reading
              lastKeptTimestamp = current.timestamp;
              lastKeptValue = current.value;
            }
          }

          // Delete the identified readings in batches
          if (idsToDelete.length > 0) {
            const deleteQuery = `DELETE FROM tag_data WHERE id IN (${idsToDelete.map(() => '?').join(',')})`;
            const result = await executeQuery(deleteQuery, idsToDelete);
            deletedCount += result.affectedRows || 0;
          }
        }
      }

      return deletedCount;

    } catch (error) {
      console.error(`❌ Error applying data retention rules for tag ${tagId}:`, error);
      return 0;
    }
  }

  disconnect() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
      console.log('🧹 Periodic cleanup stopped');
    }

    if (this.client) {
      this.client.end();
      this.isConnected = false;
    }
  }
}

export default MQTTService;
