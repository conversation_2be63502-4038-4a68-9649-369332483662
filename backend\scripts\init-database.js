import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const createDatabase = async () => {
  // First connection without database name to create it
  const initialConnection = await mysql.createConnection({
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || ''
  });

  try {
    // Create database if not exists
    await initialConnection.execute(`CREATE DATABASE IF NOT EXISTS ${process.env.DB_NAME || 'storm_db'}`);
    console.log('✅ Database created successfully');

    // Close the initial connection
    await initialConnection.end();

    // Create a new connection with the database name
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'storm_db'
    });

    // Create zones table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS zones (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Create locations table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS locations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        zone_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        pump_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (zone_id) REFERENCES zones(id) ON DELETE CASCADE,
        UNIQUE KEY unique_location_per_zone (zone_id, name)
      )
    `);

    // Create tags table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS tags (
        id INT AUTO_INCREMENT PRIMARY KEY,
        location_id INT NOT NULL,
        opc_address VARCHAR(500) NOT NULL UNIQUE,
        mqtt_topic VARCHAR(500) NOT NULL,
        tag_name VARCHAR(255) NOT NULL,
        tag_type ENUM('LEVEL', 'PUMP_STATUS', 'FLOW_RATE', 'TOTALIZER', 'OTHER') DEFAULT 'OTHER',
        unit VARCHAR(50),
        min_value DECIMAL(10,4),
        max_value DECIMAL(10,4),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE CASCADE
      )
    `);

    // Create tag_data table for storing real-time data
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS tag_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        tag_id INT NOT NULL,
        value DECIMAL(15,6),
        timestamp BIGINT NOT NULL,
        received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE,
        INDEX idx_tag_timestamp (tag_id, timestamp),
        INDEX idx_received_at (received_at)
      )
    `);

    // Create admin users table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100) NOT NULL UNIQUE,
        password_hash VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
 
    // Create app users table for mobile app authentication
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS app_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        email VARCHAR(100),
        full_name VARCHAR(100),
        role VARCHAR(20) DEFAULT 'user',
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Create user sessions table for managing app user sessions
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token_hash VARCHAR(255) NOT NULL,
        device_info TEXT,
        ip_address VARCHAR(45),
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES app_users(id) ON DELETE CASCADE,
        INDEX idx_token_hash (token_hash),
        INDEX idx_expires_at (expires_at)
      )
    `);

    console.log('✅ All tables created successfully');

    // Insert sample data
    await insertSampleData(connection);

    // Create default users
    await createDefaultUsers(connection);

    // Close the connection
    await connection.end();

  } catch (error) {
    console.error('❌ Error creating database:', error);
    if (initialConnection) await initialConnection.end();
  }
};

const insertSampleData = async (connection) => {
  try {
    // Insert zones based on the provided zone list
    const zones = [
      ['North', 'Northern area pumping stations'],
      ['South', 'Southern area pumping stations'],
      ['East', 'Eastern area pumping stations'],
      ['West', 'Western area pumping stations'],
      ['South West', 'South Western area pumping stations']
    ];

    for (const [name, description] of zones) {
      await connection.execute(
        'INSERT IGNORE INTO zones (name, description) VALUES (?, ?)',
        [name, description]
      );
    }

    // Get zone IDs
    const [zoneResults] = await connection.execute('SELECT id, name FROM zones');
    const zoneMap = {};
    zoneResults.forEach(zone => {
      zoneMap[zone.name] = zone.id;
    });

    // Zone mapping based on the provided site list and actual CSV data
    const zoneMapping = {
      // South West Zone
      'SHREENAND NAGAR': 'South West',

      // West Zone
      'JAYDEEP TOWER': 'West',
      'AKHBAR NAGAR': 'West',

      // South Zone
      'VYAYAMSHALA': 'South',
      'GEBANSHAPIR': 'South',
      'NOOR NAGAR': 'South',
      'DEVIMATA': 'South',
      'BHAIRAVNATH': 'South',
      'NIRMANALA': 'South',
      'AVAKAR HALL': 'South',
      'SURELIYA': 'South',
      'MATLA CIRCLE': 'South',
      'GURUJI BRIDGE': 'South',
      'SMRUTI MANDIR': 'South',
      'NAVANA VATVA': 'South',
      'NAVANAVATAVA': 'South',
      'POOJA FARM': 'South',
      'ARBUDA NAGAR': 'South',

      // East Zone
      'ODHAV AMBIKANAGAR': 'East',
      'VASTRAL': 'East',
      'VIRATNAGAR': 'East',
      'ODHAVFIRE STATION': 'East',
      'HARIVILLA': 'East',
      'JAY CHEMICAL': 'East',
      'NEW NIKOL': 'East',
      'ODHAV 100 MLD': 'East',

      // North Zone
      'NAVYUG': 'North',
      'BAPA SITARAM MADHULI': 'North',
      'KUBERNAGAR': 'North',
      'KUBER NAGAR': 'North',
      'RAJIV PARK': 'North',
      'MALEKSBAN': 'North',
      'TRIKAMLAL': 'North',
      'NARODA HANSPURA': 'North',
      'NARODA GAYATRI': 'North',
      'DEHGAM ROAD': 'North',
      'GAYTRINAGAR': 'North',

      // Additional locations from CSV that need zone assignment
      'DANI LIMDA': 'West',
      'JAMALPUR': 'North',
      'NEW CHAMANPURA': 'North',
      'NEW GOMTIPUR': 'North',
      'PIRANA NEW': 'West',
      'PIRANA TERMINAL': 'West',
      'TRIKAMPURA': 'North',
      'VEJALPUR': 'West',
      'VINZOAL TERMINAL': 'West'
    };

    // Create locations for each site with pump counts based on actual tags in the file
    const locationPumpCounts = {
      'AKHBAR NAGAR': 5,           // PUMP_1_RFB to PUMP_5_RFB
      'ARBUDA NAGAR': 2,           // PUMP_1_RFB to PUMP_2_RFB
      'AVAKAR HALL': 3,            // PUMP_1_RFB to PUMP_3_RFB
      'BAPA SITARAM MADHULI': 1,   // PUMP_1_RFB only
      'BHAIRAVNATH': 4,            // PUMP_1_RFB to PUMP_4_RFB
      'DANI LIMDA': 8,             // PUMP_1_RFB to PUMP_8_RFB
      'DEHGAM ROAD': 2,            // PUMP_1_RFB to PUMP_2_RFB
      'DEVIMATA': 3,               // PUMP_1_RFB to PUMP_3_RFB
      'GAYTRINAGAR': 3,            // PUMP_1_RFB to PUMP_3_RFB
      'GEBANSHAPIR': 1,            // PUMP_1_RFB only
      'GURUJI BRIDGE': 3,          // PUMP_1_RFB to PUMP_3_RFB (note: OPC shows 2 PUMP but has 3 pumps)
      'HARIVILLA': 3,              // PUMP_1_RFB to PUMP_3_RFB
      'JAMALPUR': 9,               // PUMP_1_RFB to PUMP_9_RFB
      'JAY CHEMICAL': 4,           // PUMP_1_RFB to PUMP_4_RFB
      'JAYDEEP TOWER': 2,          // PUMP_1_RFB to PUMP_2_RFB
      'KUBER NAGAR': 2,            // PUMP_1_RFB to PUMP_2_RFB
      'KUBERNAGAR': 6,             // PUMP_1_RFB to PUMP_6_RFB
      'MALEKSBAN': 3,              // PUMP_1_RFB to PUMP_3_RFB
      'MATLA CIRCLE': 2,           // PUMP_1_RFB to PUMP_2_RFB
      'NARODA GAYATRI': 6,         // PUMP_1_RFB to PUMP_6_RFB
      'NARODA HANSPURA': 3,        // PUMP_1_RFB to PUMP_3_RFB
      'NAVANA VATVA': 10,          // PUMP_1_RFB to PUMP_10_RFB
      'NAVANAVATAVA': 4,           // PUMP_1_RFB to PUMP_4_RFB
      'NAVYUG': 3,                 // PUMP_1_RFB to PUMP_3_RFB
      'NEW CHAMANPURA': 7,         // PUMP_1_RFB to PUMP_7_RFB
      'NEW GOMTIPUR': 8,           // PUMP_1_RFB to PUMP_8_RFB
      'NEW NIKOL': 7,              // PUMP_1_RFB to PUMP_7_RFB
      'NIRMANALA': 2,              // PUMP_1_RFB to PUMP_2_RFB
      'NOOR NAGAR': 2,             // PUMP_1_RFB to PUMP_2_RFB
      'ODHAV 100 MLD': 6,          // PUMP_1_RFB to PUMP_6_RFB
      'ODHAV AMBIKANAGAR': 3,      // PUMP_1_RFB to PUMP_3_RFB
      'ODHAVFIRE STATION': 6,      // PUMP_1_RFB to PUMP_6_RFB
      'PIRANA NEW': 8,             // PUMP_1_RFB to PUMP_8_RFB
      'PIRANA TERMINAL': 15,       // PUMP_1_RFB to PUMP_15_RFB
      'POOJA FARM': 4,             // PUMP_1_RFB to PUMP_4_RFB
      'RAJIV PARK': 1,             // PUMP_1_RFB only
      'SHREENAND NAGAR': 5,        // PUMP_1_RFB to PUMP_5_RFB
      'SMRUTI MANDIR': 1,          // PUMP_1_RFB only
      'SURELIYA': 3,               // PUMP_1_RFB to PUMP_3_RFB
      'TRIKAMLAL': 3,              // PUMP_1_RFB to PUMP_3_RFB
      'TRIKAMPURA': 0,             // No pump status tags, only level and flow
      'VASTRAL': 4,                // PUMP_1_RFB to PUMP_4_RFB
      'VEJALPUR': 9,               // PUMP_1_RFB to PUMP_9_RFB
      'VINZOAL TERMINAL': 10,      // PUMP_1_RFB to PUMP_10_RFB
      'VIRATNAGAR': 8,             // PUMP_1_RFB to PUMP_8_RFB
      'VYAYAMSHALA': 1             // PUMP_1_RFB only
    };

    for (const [locationName, zoneName] of Object.entries(zoneMapping)) {
      const zoneId = zoneMap[zoneName];
      if (zoneId) {
        const pumpCount = locationPumpCounts[locationName] || 5; // Default to 5 if not specified
        await connection.execute(
          'INSERT IGNORE INTO locations (zone_id, name, description, pump_count) VALUES (?, ?, ?, ?)',
          [zoneId, locationName, `${locationName} Pumping Station`, pumpCount]
        );
      }
    }

    console.log('✅ Locations created successfully');

    // Now create tags from static list
    await createStaticTags(connection);

  } catch (error) {
    console.error('❌ Error inserting sample data:', error);
  }
};

const createStaticTags = async (connection) => {
  try {
    console.log('🏷️ Creating tags from static list...');

    // Get all locations from database
    const [locations] = await connection.execute(`
      SELECT l.id, l.name, z.name as zone_name
      FROM locations l
      JOIN zones z ON l.zone_id = z.id
    `);

    const locationMap = {};
    locations.forEach(location => {
      locationMap[location.name] = location.id;
    });

    console.log(`📍 Found ${locations.length} locations in database`);

    let importedCount = 0;

    // AKHBAR NAGAR tags
    const akhbarNagarId = locationMap['AKHBAR NAGAR'];
    if (akhbarNagarId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [akhbarNagarId, "ns=2;s=AKHBAR NAGAR.5 PUMP.LEVEL_1", "AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [akhbarNagarId, "ns=2;s=AKHBAR NAGAR.5 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [akhbarNagarId, "ns=2;s=AKHBAR NAGAR.5 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [akhbarNagarId, "ns=2;s=AKHBAR NAGAR.5 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [akhbarNagarId, "ns=2;s=AKHBAR NAGAR.5 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [akhbarNagarId, "ns=2;s=AKHBAR NAGAR.5 PUMP.PUMP_5_RFB", "AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/PUMP_5_RFB", "PUMP_5_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [akhbarNagarId, "ns=2;s=AKHBAR NAGAR.5 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [akhbarNagarId, "ns=2;s=AKHBAR NAGAR.5 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [akhbarNagarId, "ns=2;s=AKHBAR NAGAR.5 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/AKHBAR NAGAR/5 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 9;
    }

    // ARBUDA NAGAR tags
    const arbudaNagarId = locationMap['ARBUDA NAGAR'];
    if (arbudaNagarId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [arbudaNagarId, "ns=2;s=ARBUDA NAGAR.2 PUMP.LEVEL_1", "AMC/Storm/amcstorm/ARBUDA NAGAR/2 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [arbudaNagarId, "ns=2;s=ARBUDA NAGAR.2 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/ARBUDA NAGAR/2 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [arbudaNagarId, "ns=2;s=ARBUDA NAGAR.2 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/ARBUDA NAGAR/2 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [arbudaNagarId, "ns=2;s=ARBUDA NAGAR.2 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/ARBUDA NAGAR/2 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [arbudaNagarId, "ns=2;s=ARBUDA NAGAR.2 PUMP.SEWAGE_LAST_TOTALIZER", "AMC/Storm/amcstorm/ARBUDA NAGAR/2 PUMP/SEWAGE_LAST_TOTALIZER", "SEWAGE_LAST_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [arbudaNagarId, "ns=2;s=ARBUDA NAGAR.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/ARBUDA NAGAR/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [arbudaNagarId, "ns=2;s=ARBUDA NAGAR.2 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/ARBUDA NAGAR/2 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 7;
    }

    // AVAKAR HALL tags
    const avakarHallId = locationMap['AVAKAR HALL'];
    if (avakarHallId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [avakarHallId, "ns=2;s=AVAKAR HALL.3 PUMP.LEVEL_1", "AMC/Storm/amcstorm/AVAKAR HALL/3 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [avakarHallId, "ns=2;s=AVAKAR HALL.3 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/AVAKAR HALL/3 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [avakarHallId, "ns=2;s=AVAKAR HALL.3 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/AVAKAR HALL/3 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [avakarHallId, "ns=2;s=AVAKAR HALL.3 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/AVAKAR HALL/3 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [avakarHallId, "ns=2;s=AVAKAR HALL.3 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/AVAKAR HALL/3 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [avakarHallId, "ns=2;s=AVAKAR HALL.3 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/AVAKAR HALL/3 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [avakarHallId, "ns=2;s=AVAKAR HALL.3 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/AVAKAR HALL/3 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 7;
    }

    // BAPA SITARAM MADHULI tags
    const bapaSitaramId = locationMap['BAPA SITARAM MADHULI'];
    if (bapaSitaramId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [bapaSitaramId, "ns=2;s=BAPA SITARAM MADHULI.1 PUMP.LEVEL_1", "AMC/Storm/amcstorm/BAPA SITARAM MADHULI/1 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [bapaSitaramId, "ns=2;s=BAPA SITARAM MADHULI.1 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/BAPA SITARAM MADHULI/1 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [bapaSitaramId, "ns=2;s=BAPA SITARAM MADHULI.1 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/BAPA SITARAM MADHULI/1 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [bapaSitaramId, "ns=2;s=BAPA SITARAM MADHULI.1 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/BAPA SITARAM MADHULI/1 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [bapaSitaramId, "ns=2;s=BAPA SITARAM MADHULI.1 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/BAPA SITARAM MADHULI/1 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 5;
    }

    // BHAIRAVNATH tags
    const bhairavnathId = locationMap['BHAIRAVNATH'];
    if (bhairavnathId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [bhairavnathId, "ns=2;s=BHAIRAVNATH.4 PUMP.LEVEL_1", "AMC/Storm/amcstorm/BHAIRAVNATH/4 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [bhairavnathId, "ns=2;s=BHAIRAVNATH.4 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/BHAIRAVNATH/4 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [bhairavnathId, "ns=2;s=BHAIRAVNATH.4 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/BHAIRAVNATH/4 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [bhairavnathId, "ns=2;s=BHAIRAVNATH.4 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/BHAIRAVNATH/4 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [bhairavnathId, "ns=2;s=BHAIRAVNATH.4 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/BHAIRAVNATH/4 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [bhairavnathId, "ns=2;s=BHAIRAVNATH.4 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/BHAIRAVNATH/4 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [bhairavnathId, "ns=2;s=BHAIRAVNATH.4 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/BHAIRAVNATH/4 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [bhairavnathId, "ns=2;s=BHAIRAVNATH.4 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/BHAIRAVNATH/4 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 8;
    }

    // DANI LIMDA tags
    const daniLimdaId = locationMap['DANI LIMDA'];
    if (daniLimdaId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [daniLimdaId, "ns=2;s=DANI LIMDA.5 PUMP.HT_RFB", "AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/HT_RFB", "HT_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [daniLimdaId, "ns=2;s=DANI LIMDA.5 PUMP.LEVEL_1", "AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [daniLimdaId, "ns=2;s=DANI LIMDA.5 PUMP.LT_RFB", "AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/LT_RFB", "LT_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [daniLimdaId, "ns=2;s=DANI LIMDA.5 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [daniLimdaId, "ns=2;s=DANI LIMDA.5 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [daniLimdaId, "ns=2;s=DANI LIMDA.5 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [daniLimdaId, "ns=2;s=DANI LIMDA.5 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [daniLimdaId, "ns=2;s=DANI LIMDA.5 PUMP.PUMP_5_RFB", "AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/PUMP_5_RFB", "PUMP_5_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [daniLimdaId, "ns=2;s=DANI LIMDA.5 PUMP.PUMP_6_RFB", "AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/PUMP_6_RFB", "PUMP_6_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [daniLimdaId, "ns=2;s=DANI LIMDA.5 PUMP.PUMP_7_RFB", "AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/PUMP_7_RFB", "PUMP_7_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [daniLimdaId, "ns=2;s=DANI LIMDA.5 PUMP.PUMP_8_RFB", "AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/PUMP_8_RFB", "PUMP_8_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [daniLimdaId, "ns=2;s=DANI LIMDA.5 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [daniLimdaId, "ns=2;s=DANI LIMDA.5 PUMP.SEWAGE_TOTALIZER_1", "AMC/Storm/amcstorm/DANI LIMDA/5 PUMP/SEWAGE_TOTALIZER_1", "SEWAGE_TOTALIZER_1", "TOTALIZER", "ML", 0, null]);
      importedCount += 13;
    }

    // DEHGAM ROAD tags
    const dehgamRoadId = locationMap['DEHGAM ROAD'];
    if (dehgamRoadId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [dehgamRoadId, "ns=2;s=DEHGAM ROAD.2 PUMP.LEVEL_1", "AMC/Storm/amcstorm/DEHGAM ROAD/2 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [dehgamRoadId, "ns=2;s=DEHGAM ROAD.2 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/DEHGAM ROAD/2 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [dehgamRoadId, "ns=2;s=DEHGAM ROAD.2 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/DEHGAM ROAD/2 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [dehgamRoadId, "ns=2;s=DEHGAM ROAD.2 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/DEHGAM ROAD/2 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [dehgamRoadId, "ns=2;s=DEHGAM ROAD.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/DEHGAM ROAD/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [dehgamRoadId, "ns=2;s=DEHGAM ROAD.2 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/DEHGAM ROAD/2 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 6;
    }

    // DEVIMATA tags
    const devimataId = locationMap['DEVIMATA'];
    if (devimataId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [devimataId, "ns=2;s=DEVIMATA.3 PUMP.LEVEL_1", "AMC/Storm/amcstorm/DEVIMATA/3 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [devimataId, "ns=2;s=DEVIMATA.3 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/DEVIMATA/3 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [devimataId, "ns=2;s=DEVIMATA.3 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/DEVIMATA/3 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [devimataId, "ns=2;s=DEVIMATA.3 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/DEVIMATA/3 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [devimataId, "ns=2;s=DEVIMATA.3 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/DEVIMATA/3 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [devimataId, "ns=2;s=DEVIMATA.3 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/DEVIMATA/3 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [devimataId, "ns=2;s=DEVIMATA.3 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/DEVIMATA/3 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 7;
    }

    // GAYTRINAGAR tags
    const gaytrinagarId = locationMap['GAYTRINAGAR'];
    if (gaytrinagarId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gaytrinagarId, "ns=2;s=GAYTRINAGAR.3 PUMP.LEVEL_1", "AMC/Storm/amcstorm/GAYTRINAGAR/3 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gaytrinagarId, "ns=2;s=GAYTRINAGAR.3 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/GAYTRINAGAR/3 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gaytrinagarId, "ns=2;s=GAYTRINAGAR.3 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/GAYTRINAGAR/3 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gaytrinagarId, "ns=2;s=GAYTRINAGAR.3 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/GAYTRINAGAR/3 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gaytrinagarId, "ns=2;s=GAYTRINAGAR.3 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/GAYTRINAGAR/3 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gaytrinagarId, "ns=2;s=GAYTRINAGAR.3 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/GAYTRINAGAR/3 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gaytrinagarId, "ns=2;s=GAYTRINAGAR.3 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/GAYTRINAGAR/3 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 7;
    }

    // GEBANSHAPIR tags
    const gebanshapirId = locationMap['GEBANSHAPIR'];
    if (gebanshapirId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gebanshapirId, "ns=2;s=GEBANSHAPIR.1 PUMP.LEVEL_1", "AMC/Storm/amcstorm/GEBANSHAPIR/1 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gebanshapirId, "ns=2;s=GEBANSHAPIR.1 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/GEBANSHAPIR/1 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gebanshapirId, "ns=2;s=GEBANSHAPIR.1 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/GEBANSHAPIR/1 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gebanshapirId, "ns=2;s=GEBANSHAPIR.1 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/GEBANSHAPIR/1 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gebanshapirId, "ns=2;s=GEBANSHAPIR.1 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/GEBANSHAPIR/1 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 5;
    }

    // GURUJI BRIDGE tags
    const gurujiBridgeId = locationMap['GURUJI BRIDGE'];
    if (gurujiBridgeId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gurujiBridgeId, "ns=2;s=GURUJI BRIDGE.2 PUMP.LEVEL_1", "AMC/Storm/amcstorm/GURUJI BRIDGE/2 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gurujiBridgeId, "ns=2;s=GURUJI BRIDGE.2 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/GURUJI BRIDGE/2 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gurujiBridgeId, "ns=2;s=GURUJI BRIDGE.2 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/GURUJI BRIDGE/2 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gurujiBridgeId, "ns=2;s=GURUJI BRIDGE.2 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/GURUJI BRIDGE/2 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gurujiBridgeId, "ns=2;s=GURUJI BRIDGE.2 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/GURUJI BRIDGE/2 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gurujiBridgeId, "ns=2;s=GURUJI BRIDGE.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/GURUJI BRIDGE/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [gurujiBridgeId, "ns=2;s=GURUJI BRIDGE.2 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/GURUJI BRIDGE/2 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 7;
    }

    // HARIVILLA tags
    const harivillaId = locationMap['HARIVILLA'];
    if (harivillaId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [harivillaId, "ns=2;s=HARIVILLA.1 PUMP.LEVEL_1", "AMC/Storm/amcstorm/HARIVILLA/1 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [harivillaId, "ns=2;s=HARIVILLA.1 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/HARIVILLA/1 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [harivillaId, "ns=2;s=HARIVILLA.1 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/HARIVILLA/1 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [harivillaId, "ns=2;s=HARIVILLA.1 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/HARIVILLA/1 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [harivillaId, "ns=2;s=HARIVILLA.1 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/HARIVILLA/1 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [harivillaId, "ns=2;s=HARIVILLA.1 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/HARIVILLA/1 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [harivillaId, "ns=2;s=HARIVILLA.1 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/HARIVILLA/1 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 7;
    }

    // JAMALPUR tags
    const jamalpurId = locationMap['JAMALPUR'];
    if (jamalpurId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.LEVEL_1", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.LEVEL_2", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/LEVEL_2", "LEVEL_2", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.LT_RFB", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/LT_RFB", "LT_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.PUMP_5_RFB", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_5_RFB", "PUMP_5_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.PUMP_6_RFB", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_6_RFB", "PUMP_6_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.PUMP_7_RFB", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_7_RFB", "PUMP_7_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.PUMP_8_RFB", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_8_RFB", "PUMP_8_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.PUMP_9_RFB", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/PUMP_9_RFB", "PUMP_9_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.SEWAGE_CURRENT_DAY_2", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/SEWAGE_CURRENT_DAY_2", "SEWAGE_CURRENT_DAY_2", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.SEWAGE_CURRENT_DAY_3", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/SEWAGE_CURRENT_DAY_3", "SEWAGE_CURRENT_DAY_3", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.SEWAGE_TOTALIZER_1", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/SEWAGE_TOTALIZER_1", "SEWAGE_TOTALIZER_1", "TOTALIZER", "ML", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.SEWAGE_TOTALIZER_2", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/SEWAGE_TOTALIZER_2", "SEWAGE_TOTALIZER_2", "TOTALIZER", "ML", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jamalpurId, "ns=2;s=JAMALPUR.9 PUMP.SEWAGE_TOTALIZER_3", "AMC/Storm/amcstorm/JAMALPUR/9 PUMP/SEWAGE_TOTALIZER_3", "SEWAGE_TOTALIZER_3", "TOTALIZER", "ML", 0, null]);
      importedCount += 18;
    }

    // JAY CHEMICAL tags
    const jayChemicalId = locationMap['JAY CHEMICAL'];
    if (jayChemicalId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jayChemicalId, "ns=2;s=JAY CHEMICAL.2 PUMP.LEVEL_1", "AMC/Storm/amcstorm/JAY CHEMICAL/2 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jayChemicalId, "ns=2;s=JAY CHEMICAL.2 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/JAY CHEMICAL/2 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jayChemicalId, "ns=2;s=JAY CHEMICAL.2 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/JAY CHEMICAL/2 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jayChemicalId, "ns=2;s=JAY CHEMICAL.2 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/JAY CHEMICAL/2 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jayChemicalId, "ns=2;s=JAY CHEMICAL.2 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/JAY CHEMICAL/2 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jayChemicalId, "ns=2;s=JAY CHEMICAL.2 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/JAY CHEMICAL/2 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jayChemicalId, "ns=2;s=JAY CHEMICAL.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/JAY CHEMICAL/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jayChemicalId, "ns=2;s=JAY CHEMICAL.2 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/JAY CHEMICAL/2 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 8;
    }

    // JAYDEEP TOWER tags
    const jaydeepTowerId = locationMap['JAYDEEP TOWER'];
    if (jaydeepTowerId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jaydeepTowerId, "ns=2;s=JAYDEEP TOWER.2 PUMP.LEVEL_1", "AMC/Storm/amcstorm/JAYDEEP TOWER/2 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jaydeepTowerId, "ns=2;s=JAYDEEP TOWER.2 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/JAYDEEP TOWER/2 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jaydeepTowerId, "ns=2;s=JAYDEEP TOWER.2 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/JAYDEEP TOWER/2 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jaydeepTowerId, "ns=2;s=JAYDEEP TOWER.2 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/JAYDEEP TOWER/2 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jaydeepTowerId, "ns=2;s=JAYDEEP TOWER.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/JAYDEEP TOWER/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [jaydeepTowerId, "ns=2;s=JAYDEEP TOWER.2 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/JAYDEEP TOWER/2 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 6;
    }

    // KUBER NAGAR tags
    const kuberNagarId = locationMap['KUBER NAGAR'];
    if (kuberNagarId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [kuberNagarId, "ns=2;s=KUBER NAGAR.2 PUMP.LEVEL_1", "AMC/Storm/amcstorm/KUBER NAGAR/2 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [kuberNagarId, "ns=2;s=KUBER NAGAR.2 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/KUBER NAGAR/2 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [kuberNagarId, "ns=2;s=KUBER NAGAR.2 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/KUBER NAGAR/2 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [kuberNagarId, "ns=2;s=KUBER NAGAR.2 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/KUBER NAGAR/2 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [kuberNagarId, "ns=2;s=KUBER NAGAR.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/KUBER NAGAR/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [kuberNagarId, "ns=2;s=KUBER NAGAR.2 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/KUBER NAGAR/2 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 6;
    }

    // KUBERNAGAR tags
    const kubernagarId = locationMap['KUBERNAGAR'];
    if (kubernagarId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [kubernagarId, "ns=2;s=KUBERNAGAR.4 PUMP.LEVEL_1", "AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [kubernagarId, "ns=2;s=KUBERNAGAR.4 PUMP.LT_RFB", "AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/LT_RFB", "LT_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [kubernagarId, "ns=2;s=KUBERNAGAR.4 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [kubernagarId, "ns=2;s=KUBERNAGAR.4 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [kubernagarId, "ns=2;s=KUBERNAGAR.4 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [kubernagarId, "ns=2;s=KUBERNAGAR.4 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [kubernagarId, "ns=2;s=KUBERNAGAR.4 PUMP.PUMP_5_RFB", "AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/PUMP_5_RFB", "PUMP_5_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [kubernagarId, "ns=2;s=KUBERNAGAR.4 PUMP.PUMP_6_RFB", "AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/PUMP_6_RFB", "PUMP_6_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [kubernagarId, "ns=2;s=KUBERNAGAR.4 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [kubernagarId, "ns=2;s=KUBERNAGAR.4 PUMP.SEWAGE_TOTALIZER_1", "AMC/Storm/amcstorm/KUBERNAGAR/4 PUMP/SEWAGE_TOTALIZER_1", "SEWAGE_TOTALIZER_1", "TOTALIZER", "ML", 0, null]);
      importedCount += 10;
    }

    // MALEKSBAN tags
    const maleksbanId = locationMap['MALEKSBAN'];
    if (maleksbanId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [maleksbanId, "ns=2;s=MALEKSBAN.3 PUMP.LEVEL_1", "AMC/Storm/amcstorm/MALEKSBAN/3 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [maleksbanId, "ns=2;s=MALEKSBAN.3 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/MALEKSBAN/3 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [maleksbanId, "ns=2;s=MALEKSBAN.3 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/MALEKSBAN/3 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [maleksbanId, "ns=2;s=MALEKSBAN.3 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/MALEKSBAN/3 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [maleksbanId, "ns=2;s=MALEKSBAN.3 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/MALEKSBAN/3 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [maleksbanId, "ns=2;s=MALEKSBAN.3 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/MALEKSBAN/3 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [maleksbanId, "ns=2;s=MALEKSBAN.3 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/MALEKSBAN/3 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 7;
    }

    // MATLA CIRCLE tags
    const matlaCircleId = locationMap['MATLA CIRCLE'];
    if (matlaCircleId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [matlaCircleId, "ns=2;s=MATLA CIRCLE.2 PUMP.LEVEL_1", "AMC/Storm/amcstorm/MATLA CIRCLE/2 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [matlaCircleId, "ns=2;s=MATLA CIRCLE.2 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/MATLA CIRCLE/2 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [matlaCircleId, "ns=2;s=MATLA CIRCLE.2 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/MATLA CIRCLE/2 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [matlaCircleId, "ns=2;s=MATLA CIRCLE.2 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/MATLA CIRCLE/2 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [matlaCircleId, "ns=2;s=MATLA CIRCLE.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/MATLA CIRCLE/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [matlaCircleId, "ns=2;s=MATLA CIRCLE.2 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/MATLA CIRCLE/2 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 6;
    }

    // NARODA GAYATRI tags
    const narodaGayatriId = locationMap['NARODA GAYATRI'];
    if (narodaGayatriId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [narodaGayatriId, "ns=2;s=NARODA GAYATRI.6 PUMP.LEVEL_1", "AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [narodaGayatriId, "ns=2;s=NARODA GAYATRI.6 PUMP.LT_RFB", "AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/LT_RFB", "LT_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [narodaGayatriId, "ns=2;s=NARODA GAYATRI.6 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [narodaGayatriId, "ns=2;s=NARODA GAYATRI.6 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [narodaGayatriId, "ns=2;s=NARODA GAYATRI.6 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [narodaGayatriId, "ns=2;s=NARODA GAYATRI.6 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [narodaGayatriId, "ns=2;s=NARODA GAYATRI.6 PUMP.PUMP_5_RFB", "AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/PUMP_5_RFB", "PUMP_5_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [narodaGayatriId, "ns=2;s=NARODA GAYATRI.6 PUMP.PUMP_6_RFB", "AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/PUMP_6_RFB", "PUMP_6_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [narodaGayatriId, "ns=2;s=NARODA GAYATRI.6 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [narodaGayatriId, "ns=2;s=NARODA GAYATRI.6 PUMP.SEWAGE_TOTALIZER_1", "AMC/Storm/amcstorm/NARODA GAYATRI/6 PUMP/SEWAGE_TOTALIZER_1", "SEWAGE_TOTALIZER_1", "TOTALIZER", "ML", 0, null]);
      importedCount += 10;
    }

    // NARODA HANSPURA tags
    const narodaHanspuraId = locationMap['NARODA HANSPURA'];
    if (narodaHanspuraId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [narodaHanspuraId, "ns=2;s=NARODA HANSPURA.3PUMP.LEVEL_1", "AMC/Storm/amcstorm/NARODA HANSPURA/3PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [narodaHanspuraId, "ns=2;s=NARODA HANSPURA.3PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/NARODA HANSPURA/3PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [narodaHanspuraId, "ns=2;s=NARODA HANSPURA.3PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/NARODA HANSPURA/3PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [narodaHanspuraId, "ns=2;s=NARODA HANSPURA.3PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/NARODA HANSPURA/3PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [narodaHanspuraId, "ns=2;s=NARODA HANSPURA.3PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/NARODA HANSPURA/3PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [narodaHanspuraId, "ns=2;s=NARODA HANSPURA.3PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/NARODA HANSPURA/3PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [narodaHanspuraId, "ns=2;s=NARODA HANSPURA.3PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/NARODA HANSPURA/3PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 7;
    }

    // NAVANA VATVA tags (complex location with 16 tags)
    const navanaVatvaId = locationMap['NAVANA VATVA'];
    if (navanaVatvaId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanaVatvaId, "ns=2;s=NAVANA VATVA.5 PUMP.HT_RFB", "AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/HT_RFB", "HT_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanaVatvaId, "ns=2;s=NAVANA VATVA.5 PUMP.LEVEL_1", "AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanaVatvaId, "ns=2;s=NAVANA VATVA.5 PUMP.LT_RFB", "AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/LT_RFB", "LT_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanaVatvaId, "ns=2;s=NAVANA VATVA.5 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanaVatvaId, "ns=2;s=NAVANA VATVA.5 PUMP.PUMP_10_RFB", "AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_10_RFB", "PUMP_10_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanaVatvaId, "ns=2;s=NAVANA VATVA.5 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanaVatvaId, "ns=2;s=NAVANA VATVA.5 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanaVatvaId, "ns=2;s=NAVANA VATVA.5 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanaVatvaId, "ns=2;s=NAVANA VATVA.5 PUMP.PUMP_5_RFB", "AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_5_RFB", "PUMP_5_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanaVatvaId, "ns=2;s=NAVANA VATVA.5 PUMP.PUMP_6_RFB", "AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_6_RFB", "PUMP_6_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanaVatvaId, "ns=2;s=NAVANA VATVA.5 PUMP.PUMP_7_RFB", "AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_7_RFB", "PUMP_7_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanaVatvaId, "ns=2;s=NAVANA VATVA.5 PUMP.PUMP_8_RFB", "AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_8_RFB", "PUMP_8_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanaVatvaId, "ns=2;s=NAVANA VATVA.5 PUMP.PUMP_9_RFB", "AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/PUMP_9_RFB", "PUMP_9_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanaVatvaId, "ns=2;s=NAVANA VATVA.5 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanaVatvaId, "ns=2;s=NAVANA VATVA.5 PUMP.SEWAGE_CURRENT_DAY_2", "AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/SEWAGE_CURRENT_DAY_2", "SEWAGE_CURRENT_DAY_2", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanaVatvaId, "ns=2;s=NAVANA VATVA.5 PUMP.SEWAGE_TOTALIZER_1", "AMC/Storm/amcstorm/NAVANA VATVA/5 PUMP/SEWAGE_TOTALIZER_1", "SEWAGE_TOTALIZER_1", "TOTALIZER", "ML", 0, null]);
      importedCount += 16;
    }

    // NAVANAVATAVA tags
    const navanavatavaId = locationMap['NAVANAVATAVA'];
    if (navanavatavaId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanavatavaId, "ns=2;s=NAVANAVATAVA.4 PUMP.LEVEL_1", "AMC/Storm/amcstorm/NAVANAVATAVA/4 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanavatavaId, "ns=2;s=NAVANAVATAVA.4 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/NAVANAVATAVA/4 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanavatavaId, "ns=2;s=NAVANAVATAVA.4 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/NAVANAVATAVA/4 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanavatavaId, "ns=2;s=NAVANAVATAVA.4 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/NAVANAVATAVA/4 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanavatavaId, "ns=2;s=NAVANAVATAVA.4 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/NAVANAVATAVA/4 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanavatavaId, "ns=2;s=NAVANAVATAVA.4 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/NAVANAVATAVA/4 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanavatavaId, "ns=2;s=NAVANAVATAVA.4 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/NAVANAVATAVA/4 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navanavatavaId, "ns=2;s=NAVANAVATAVA.4 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/NAVANAVATAVA/4 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 8;
    }

    // NAVYUG tags
    const navyugId = locationMap['NAVYUG'];
    if (navyugId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navyugId, "ns=2;s=NAVYUG.3 PUMP.LEVEL_1", "AMC/Storm/amcstorm/NAVYUG/3 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navyugId, "ns=2;s=NAVYUG.3 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/NAVYUG/3 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navyugId, "ns=2;s=NAVYUG.3 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/NAVYUG/3 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navyugId, "ns=2;s=NAVYUG.3 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/NAVYUG/3 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navyugId, "ns=2;s=NAVYUG.3 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/NAVYUG/3 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navyugId, "ns=2;s=NAVYUG.3 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/NAVYUG/3 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [navyugId, "ns=2;s=NAVYUG.3 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/NAVYUG/3 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 7;
    }

    // NEW CHAMANPURA tags (complex location with 16 tags)
    const newChamanpuraId = locationMap['NEW CHAMANPURA'];
    if (newChamanpuraId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newChamanpuraId, "ns=2;s=NEW CHAMANPURA.6 PUMP.HT_RFB", "AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/HT_RFB", "HT_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newChamanpuraId, "ns=2;s=NEW CHAMANPURA.6 PUMP.LEVEL_1", "AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newChamanpuraId, "ns=2;s=NEW CHAMANPURA.6 PUMP.LEVEL_VAL_DL", "AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/LEVEL_VAL_DL", "LEVEL_VAL_DL", "OTHER", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newChamanpuraId, "ns=2;s=NEW CHAMANPURA.6 PUMP.LT_1_RFB", "AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/LT_1_RFB", "LT_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newChamanpuraId, "ns=2;s=NEW CHAMANPURA.6 PUMP.LT_2_RFB", "AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/LT_2_RFB", "LT_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newChamanpuraId, "ns=2;s=NEW CHAMANPURA.6 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newChamanpuraId, "ns=2;s=NEW CHAMANPURA.6 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newChamanpuraId, "ns=2;s=NEW CHAMANPURA.6 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newChamanpuraId, "ns=2;s=NEW CHAMANPURA.6 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newChamanpuraId, "ns=2;s=NEW CHAMANPURA.6 PUMP.PUMP_5_RFB", "AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/PUMP_5_RFB", "PUMP_5_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newChamanpuraId, "ns=2;s=NEW CHAMANPURA.6 PUMP.PUMP_6_RFB", "AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/PUMP_6_RFB", "PUMP_6_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newChamanpuraId, "ns=2;s=NEW CHAMANPURA.6 PUMP.PUMP_7_RFB", "AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/PUMP_7_RFB", "PUMP_7_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newChamanpuraId, "ns=2;s=NEW CHAMANPURA.6 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newChamanpuraId, "ns=2;s=NEW CHAMANPURA.6 PUMP.SEWAGE_CURRENT_DAY_2", "AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/SEWAGE_CURRENT_DAY_2", "SEWAGE_CURRENT_DAY_2", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newChamanpuraId, "ns=2;s=NEW CHAMANPURA.6 PUMP.SEWAGE_TOTALIZER_1", "AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/SEWAGE_TOTALIZER_1", "SEWAGE_TOTALIZER_1", "TOTALIZER", "ML", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newChamanpuraId, "ns=2;s=NEW CHAMANPURA.6 PUMP.SEWAGE_TOTALIZER_2", "AMC/Storm/amcstorm/NEW CHAMANPURA/6 PUMP/SEWAGE_TOTALIZER_2", "SEWAGE_TOTALIZER_2", "TOTALIZER", "ML", 0, null]);
      importedCount += 16;
    }

    // NEW GOMTIPUR tags (complex location with 12 tags)
    const newGomtipurId = locationMap['NEW GOMTIPUR'];
    if (newGomtipurId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newGomtipurId, "ns=2;s=NEW GOMTIPUR.8 PUMP.LEVEL_1", "AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newGomtipurId, "ns=2;s=NEW GOMTIPUR.8 PUMP.LEVEL_VAL_DL", "AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/LEVEL_VAL_DL", "LEVEL_VAL_DL", "OTHER", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newGomtipurId, "ns=2;s=NEW GOMTIPUR.8 PUMP.LT_RFB", "AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/LT_RFB", "LT_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newGomtipurId, "ns=2;s=NEW GOMTIPUR.8 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newGomtipurId, "ns=2;s=NEW GOMTIPUR.8 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newGomtipurId, "ns=2;s=NEW GOMTIPUR.8 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newGomtipurId, "ns=2;s=NEW GOMTIPUR.8 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newGomtipurId, "ns=2;s=NEW GOMTIPUR.8 PUMP.PUMP_5_RFB", "AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/PUMP_5_RFB", "PUMP_5_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newGomtipurId, "ns=2;s=NEW GOMTIPUR.8 PUMP.PUMP_6_RFB", "AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/PUMP_6_RFB", "PUMP_6_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newGomtipurId, "ns=2;s=NEW GOMTIPUR.8 PUMP.PUMP_7_RFB", "AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/PUMP_7_RFB", "PUMP_7_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newGomtipurId, "ns=2;s=NEW GOMTIPUR.8 PUMP.PUMP_8_RFB", "AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/PUMP_8_RFB", "PUMP_8_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newGomtipurId, "ns=2;s=NEW GOMTIPUR.8 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newGomtipurId, "ns=2;s=NEW GOMTIPUR.8 PUMP.SEWAGE_TOTALIZER_1", "AMC/Storm/amcstorm/NEW GOMTIPUR/8 PUMP/SEWAGE_TOTALIZER_1", "SEWAGE_TOTALIZER_1", "TOTALIZER", "ML", 0, null]);
      importedCount += 12;
    }

    // NEW NIKOL tags (11 tags)
    const newNikolId = locationMap['NEW NIKOL'];
    if (newNikolId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newNikolId, "ns=2;s=NEW NIKOL.7 PUMP.LEVEL_1", "AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newNikolId, "ns=2;s=NEW NIKOL.7 PUMP.LT_RFB", "AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/LT_RFB", "LT_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newNikolId, "ns=2;s=NEW NIKOL.7 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newNikolId, "ns=2;s=NEW NIKOL.7 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newNikolId, "ns=2;s=NEW NIKOL.7 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newNikolId, "ns=2;s=NEW NIKOL.7 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newNikolId, "ns=2;s=NEW NIKOL.7 PUMP.PUMP_5_RFB", "AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/PUMP_5_RFB", "PUMP_5_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newNikolId, "ns=2;s=NEW NIKOL.7 PUMP.PUMP_6_RFB", "AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/PUMP_6_RFB", "PUMP_6_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newNikolId, "ns=2;s=NEW NIKOL.7 PUMP.PUMP_7_RFB", "AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/PUMP_7_RFB", "PUMP_7_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newNikolId, "ns=2;s=NEW NIKOL.7 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [newNikolId, "ns=2;s=NEW NIKOL.7 PUMP.SEWAGE_TOTALIZER_1", "AMC/Storm/amcstorm/NEW NIKOL/7 PUMP/SEWAGE_TOTALIZER_1", "SEWAGE_TOTALIZER_1", "TOTALIZER", "ML", 0, null]);
      importedCount += 11;
    }

    // NIRMANALA tags
    const nirmanalaId = locationMap['NIRMANALA'];
    if (nirmanalaId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [nirmanalaId, "ns=2;s=NIRMANALA.2 PUMP.LEVEL_1", "AMC/Storm/amcstorm/NIRMANALA/2 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [nirmanalaId, "ns=2;s=NIRMANALA.2 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/NIRMANALA/2 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [nirmanalaId, "ns=2;s=NIRMANALA.2 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/NIRMANALA/2 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [nirmanalaId, "ns=2;s=NIRMANALA.2 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/NIRMANALA/2 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [nirmanalaId, "ns=2;s=NIRMANALA.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/NIRMANALA/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [nirmanalaId, "ns=2;s=NIRMANALA.2 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/NIRMANALA/2 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 6;
    }

    // NOOR NAGAR tags
    const noorNagarId = locationMap['NOOR NAGAR'];
    if (noorNagarId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [noorNagarId, "ns=2;s=NOOR NAGAR.2 PUMP.LEVEL_1", "AMC/Storm/amcstorm/NOOR NAGAR/2 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [noorNagarId, "ns=2;s=NOOR NAGAR.2 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/NOOR NAGAR/2 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [noorNagarId, "ns=2;s=NOOR NAGAR.2 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/NOOR NAGAR/2 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [noorNagarId, "ns=2;s=NOOR NAGAR.2 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/NOOR NAGAR/2 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [noorNagarId, "ns=2;s=NOOR NAGAR.2 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/NOOR NAGAR/2 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [noorNagarId, "ns=2;s=NOOR NAGAR.2 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/NOOR NAGAR/2 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 6;
    }

    // ODHAV 100 MLD tags
    const odhav100MLDId = locationMap['ODHAV 100 MLD'];
    if (odhav100MLDId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhav100MLDId, "ns=2;s=ODHAV 100 MLD.6 PUMP.LEVEL_1", "AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhav100MLDId, "ns=2;s=ODHAV 100 MLD.6 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhav100MLDId, "ns=2;s=ODHAV 100 MLD.6 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhav100MLDId, "ns=2;s=ODHAV 100 MLD.6 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhav100MLDId, "ns=2;s=ODHAV 100 MLD.6 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhav100MLDId, "ns=2;s=ODHAV 100 MLD.6 PUMP.PUMP_5_RFB", "AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/PUMP_5_RFB", "PUMP_5_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhav100MLDId, "ns=2;s=ODHAV 100 MLD.6 PUMP.PUMP_6_RFB", "AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/PUMP_6_RFB", "PUMP_6_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhav100MLDId, "ns=2;s=ODHAV 100 MLD.6 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhav100MLDId, "ns=2;s=ODHAV 100 MLD.6 PUMP.SEWAGE_TOTALIZER_1", "AMC/Storm/amcstorm/ODHAV 100 MLD/6 PUMP/SEWAGE_TOTALIZER_1", "SEWAGE_TOTALIZER_1", "TOTALIZER", "ML", 0, null]);
      importedCount += 9;
    }

    // ODHAV AMBIKANAGAR tags
    const odhavAmbikaId = locationMap['ODHAV AMBIKANAGAR'];
    if (odhavAmbikaId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavAmbikaId, "ns=2;s=ODHAV AMBIKANAGAR.3 PUMP.LEVEL_1", "AMC/Storm/amcstorm/ODHAV AMBIKANAGAR/3 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavAmbikaId, "ns=2;s=ODHAV AMBIKANAGAR.3 PUMP.LEVEL_2", "AMC/Storm/amcstorm/ODHAV AMBIKANAGAR/3 PUMP/LEVEL_2", "LEVEL_2", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavAmbikaId, "ns=2;s=ODHAV AMBIKANAGAR.3 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/ODHAV AMBIKANAGAR/3 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavAmbikaId, "ns=2;s=ODHAV AMBIKANAGAR.3 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/ODHAV AMBIKANAGAR/3 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavAmbikaId, "ns=2;s=ODHAV AMBIKANAGAR.3 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/ODHAV AMBIKANAGAR/3 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavAmbikaId, "ns=2;s=ODHAV AMBIKANAGAR.3 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/ODHAV AMBIKANAGAR/3 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavAmbikaId, "ns=2;s=ODHAV AMBIKANAGAR.3 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/ODHAV AMBIKANAGAR/3 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavAmbikaId, "ns=2;s=ODHAV AMBIKANAGAR.3 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/ODHAV AMBIKANAGAR/3 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 8;
    }

    // ODHAVFIRE STATION tags
    const odhavFireId = locationMap['ODHAVFIRE STATION'];
    if (odhavFireId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavFireId, "ns=2;s=ODHAVFIRE STATION.5 PUMP.LEVEL_1", "AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavFireId, "ns=2;s=ODHAVFIRE STATION.5 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavFireId, "ns=2;s=ODHAVFIRE STATION.5 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavFireId, "ns=2;s=ODHAVFIRE STATION.5 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavFireId, "ns=2;s=ODHAVFIRE STATION.5 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavFireId, "ns=2;s=ODHAVFIRE STATION.5 PUMP.PUMP_5_RFB", "AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/PUMP_5_RFB", "PUMP_5_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavFireId, "ns=2;s=ODHAVFIRE STATION.5 PUMP.PUMP_6_RFB", "AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/PUMP_6_RFB", "PUMP_6_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavFireId, "ns=2;s=ODHAVFIRE STATION.5 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavFireId, "ns=2;s=ODHAVFIRE STATION.5 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [odhavFireId, "ns=2;s=ODHAVFIRE STATION.5 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/ODHAVFIRE STATION/5 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 10;
    }

    // PIRANA NEW tags (complex location with 15 tags)
    const piranaNewId = locationMap['PIRANA NEW'];
    if (piranaNewId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaNewId, "ns=2;s=PIRANA NEW.8 PUMP.HT_RFB", "AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/HT_RFB", "HT_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaNewId, "ns=2;s=PIRANA NEW.8 PUMP.LEVEL_1", "AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaNewId, "ns=2;s=PIRANA NEW.8 PUMP.LT_1_RFB", "AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/LT_1_RFB", "LT_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaNewId, "ns=2;s=PIRANA NEW.8 PUMP.LT_2_RFB", "AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/LT_2_RFB", "LT_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaNewId, "ns=2;s=PIRANA NEW.8 PUMP.PREVEOUS DAY TOTALIZER", "AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PREVEOUS DAY TOTALIZER", "PREVEOUS DAY TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaNewId, "ns=2;s=PIRANA NEW.8 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaNewId, "ns=2;s=PIRANA NEW.8 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaNewId, "ns=2;s=PIRANA NEW.8 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaNewId, "ns=2;s=PIRANA NEW.8 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaNewId, "ns=2;s=PIRANA NEW.8 PUMP.PUMP_5_RFB", "AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PUMP_5_RFB", "PUMP_5_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaNewId, "ns=2;s=PIRANA NEW.8 PUMP.PUMP_6_RFB", "AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PUMP_6_RFB", "PUMP_6_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaNewId, "ns=2;s=PIRANA NEW.8 PUMP.PUMP_7_RFB", "AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PUMP_7_RFB", "PUMP_7_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaNewId, "ns=2;s=PIRANA NEW.8 PUMP.PUMP_8_RFB", "AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/PUMP_8_RFB", "PUMP_8_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaNewId, "ns=2;s=PIRANA NEW.8 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaNewId, "ns=2;s=PIRANA NEW.8 PUMP.SEWAGE_TOTALIZER_1", "AMC/Storm/amcstorm/PIRANA NEW/8 PUMP/SEWAGE_TOTALIZER_1", "SEWAGE_TOTALIZER_1", "TOTALIZER", "ML", 0, null]);
      importedCount += 15;
    }

    // PIRANA TERMINAL tags (largest location with 29 tags)
    const piranaTerminalId = locationMap['PIRANA TERMINAL'];
    if (piranaTerminalId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaTerminalId, "ns=2;s=PIRANA TERMINAL.10 PUMP.HT_RFB", "AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/HT_RFB", "HT_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaTerminalId, "ns=2;s=PIRANA TERMINAL.10 PUMP.LEVEL_1", "AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaTerminalId, "ns=2;s=PIRANA TERMINAL.10 PUMP.LEVEL_2", "AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/LEVEL_2", "LEVEL_2", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaTerminalId, "ns=2;s=PIRANA TERMINAL.10 PUMP.LT_1_RFB", "AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/LT_1_RFB", "LT_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaTerminalId, "ns=2;s=PIRANA TERMINAL.10 PUMP.LT_2_RFB", "AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/LT_2_RFB", "LT_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaTerminalId, "ns=2;s=PIRANA TERMINAL.10 PUMP.LT_3_RFB", "AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/LT_3_RFB", "LT_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaTerminalId, "ns=2;s=PIRANA TERMINAL.10 PUMP.PREVEOUS DAY TOTALIZER", "AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PREVEOUS DAY TOTALIZER", "PREVEOUS DAY TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      // Add all 15 pumps
      for (let i = 1; i <= 15; i++) {
        await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaTerminalId, `ns=2;s=PIRANA TERMINAL.10 PUMP.PUMP_${i}_RFB`, `AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/PUMP_${i}_RFB`, `PUMP_${i}_RFB`, "PUMP_STATUS", null, 0, 1]);
      }
      // Add flow and totalizer tags
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaTerminalId, "ns=2;s=PIRANA TERMINAL.10 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaTerminalId, "ns=2;s=PIRANA TERMINAL.10 PUMP.SEWAGE_CURRENT_DAY_2", "AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/SEWAGE_CURRENT_DAY_2", "SEWAGE_CURRENT_DAY_2", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaTerminalId, "ns=2;s=PIRANA TERMINAL.10 PUMP.SEWAGE_CURRENT_DAY_3", "AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/SEWAGE_CURRENT_DAY_3", "SEWAGE_CURRENT_DAY_3", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaTerminalId, "ns=2;s=PIRANA TERMINAL.10 PUMP.SEWAGE_CURRENT_DAY_4", "AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/SEWAGE_CURRENT_DAY_4", "SEWAGE_CURRENT_DAY_4", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaTerminalId, "ns=2;s=PIRANA TERMINAL.10 PUMP.SEWAGE_TOTALIZER_1", "AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/SEWAGE_TOTALIZER_1", "SEWAGE_TOTALIZER_1", "TOTALIZER", "ML", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaTerminalId, "ns=2;s=PIRANA TERMINAL.10 PUMP.SEWAGE_TOTALIZER_2", "AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/SEWAGE_TOTALIZER_2", "SEWAGE_TOTALIZER_2", "TOTALIZER", "ML", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaTerminalId, "ns=2;s=PIRANA TERMINAL.10 PUMP.SEWAGE_TOTALIZER_3", "AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/SEWAGE_TOTALIZER_3", "SEWAGE_TOTALIZER_3", "TOTALIZER", "ML", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [piranaTerminalId, "ns=2;s=PIRANA TERMINAL.10 PUMP.SEWAGE_TOTALIZER_4", "AMC/Storm/amcstorm/PIRANA TERMINAL/10 PUMP/SEWAGE_TOTALIZER_4", "SEWAGE_TOTALIZER_4", "TOTALIZER", "ML", 0, null]);
      importedCount += 29; // 7 + 15 pumps + 7 flow/totalizer tags
    }

    // Add remaining critical locations with simplified approach
    // POOJA FARM, RAJIV PARK, SHREENAND NAGAR, SMRUTI MANDIR, SURELIYA, TRIKAMLAL, TRIKAMPURA, VASTRAL, VEJALPUR, VINZOAL TERMINAL, VIRATNAGAR, VYAYAMSHALA

    // POOJA FARM tags
    const poojaFarmId = locationMap['POOJA FARM'];
    if (poojaFarmId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [poojaFarmId, "ns=2;s=POOJA FARM.4 PUMP.LEVEL_1", "AMC/Storm/amcstorm/POOJA FARM/4 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [poojaFarmId, "ns=2;s=POOJA FARM.4 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/POOJA FARM/4 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [poojaFarmId, "ns=2;s=POOJA FARM.4 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/POOJA FARM/4 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [poojaFarmId, "ns=2;s=POOJA FARM.4 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/POOJA FARM/4 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [poojaFarmId, "ns=2;s=POOJA FARM.4 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/POOJA FARM/4 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [poojaFarmId, "ns=2;s=POOJA FARM.4 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/POOJA FARM/4 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [poojaFarmId, "ns=2;s=POOJA FARM.4 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/POOJA FARM/4 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [poojaFarmId, "ns=2;s=POOJA FARM.4 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/POOJA FARM/4 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 8;
    }

    // SHREENAND NAGAR tags
    const shreenandNagarId = locationMap['SHREENAND NAGAR'];
    if (shreenandNagarId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [shreenandNagarId, "ns=2;s=SHREENAND NAGAR.5 PUMP.LEVEL_1", "AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [shreenandNagarId, "ns=2;s=SHREENAND NAGAR.5 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [shreenandNagarId, "ns=2;s=SHREENAND NAGAR.5 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [shreenandNagarId, "ns=2;s=SHREENAND NAGAR.5 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [shreenandNagarId, "ns=2;s=SHREENAND NAGAR.5 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [shreenandNagarId, "ns=2;s=SHREENAND NAGAR.5 PUMP.PUMP_5_RFB", "AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/PUMP_5_RFB", "PUMP_5_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [shreenandNagarId, "ns=2;s=SHREENAND NAGAR.5 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [shreenandNagarId, "ns=2;s=SHREENAND NAGAR.5 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [shreenandNagarId, "ns=2;s=SHREENAND NAGAR.5 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/SHREENAND NAGAR/5 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 9;
    }

    // RAJIV PARK tags
    const rajivParkId = locationMap['RAJIV PARK'];
    if (rajivParkId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [rajivParkId, "ns=2;s=RAJIV PARK.1 PUMP.LEVEL_1", "AMC/Storm/amcstorm/RAJIV PARK/1 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [rajivParkId, "ns=2;s=RAJIV PARK.1 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/RAJIV PARK/1 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [rajivParkId, "ns=2;s=RAJIV PARK.1 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/RAJIV PARK/1 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [rajivParkId, "ns=2;s=RAJIV PARK.1 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/RAJIV PARK/1 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [rajivParkId, "ns=2;s=RAJIV PARK.1 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/RAJIV PARK/1 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 5;
    }

    // SMRUTI MANDIR tags
    const smrutiMandirId = locationMap['SMRUTI MANDIR'];
    if (smrutiMandirId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [smrutiMandirId, "ns=2;s=SMRUTI MANDIR.1PUMP.LEVEL_1", "AMC/Storm/amcstorm/SMRUTI MANDIR/1PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [smrutiMandirId, "ns=2;s=SMRUTI MANDIR.1PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/SMRUTI MANDIR/1PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [smrutiMandirId, "ns=2;s=SMRUTI MANDIR.1PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/SMRUTI MANDIR/1PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [smrutiMandirId, "ns=2;s=SMRUTI MANDIR.1PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/SMRUTI MANDIR/1PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [smrutiMandirId, "ns=2;s=SMRUTI MANDIR.1PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/SMRUTI MANDIR/1PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 5;
    }

    // SURELIYA tags
    const sureliyaId = locationMap['SURELIYA'];
    if (sureliyaId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [sureliyaId, "ns=2;s=SURELIYA.3 PUMP.LEVEL_1", "AMC/Storm/amcstorm/SURELIYA/3 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [sureliyaId, "ns=2;s=SURELIYA.3 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/SURELIYA/3 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [sureliyaId, "ns=2;s=SURELIYA.3 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/SURELIYA/3 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [sureliyaId, "ns=2;s=SURELIYA.3 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/SURELIYA/3 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [sureliyaId, "ns=2;s=SURELIYA.3 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/SURELIYA/3 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [sureliyaId, "ns=2;s=SURELIYA.3 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/SURELIYA/3 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [sureliyaId, "ns=2;s=SURELIYA.3 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/SURELIYA/3 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 7;
    }

    // TRIKAMLAL tags
    const trikamlalId = locationMap['TRIKAMLAL'];
    if (trikamlalId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [trikamlalId, "ns=2;s=TRIKAMLAL.3 PUMP.LEVEL_1", "AMC/Storm/amcstorm/TRIKAMLAL/3 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [trikamlalId, "ns=2;s=TRIKAMLAL.3 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/TRIKAMLAL/3 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [trikamlalId, "ns=2;s=TRIKAMLAL.3 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/TRIKAMLAL/3 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [trikamlalId, "ns=2;s=TRIKAMLAL.3 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/TRIKAMLAL/3 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [trikamlalId, "ns=2;s=TRIKAMLAL.3 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/TRIKAMLAL/3 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [trikamlalId, "ns=2;s=TRIKAMLAL.3 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/TRIKAMLAL/3 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [trikamlalId, "ns=2;s=TRIKAMLAL.3 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/TRIKAMLAL/3 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 7;
    }

    // TRIKAMPURA tags
    const trikampuraId = locationMap['TRIKAMPURA'];
    if (trikampuraId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [trikampuraId, "ns=2;s=TRIKAMPURA.4 PUMP.LEVEL_1", "AMC/Storm/amcstorm/TRIKAMPURA/4 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [trikampuraId, "ns=2;s=TRIKAMPURA.4 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/TRIKAMPURA/4 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [trikampuraId, "ns=2;s=TRIKAMPURA.4 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/TRIKAMPURA/4 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [trikampuraId, "ns=2;s=TRIKAMPURA.4 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/TRIKAMPURA/4 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 4;
    }

    // VASTRAL tags
    const vastralId = locationMap['VASTRAL'];
    if (vastralId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vastralId, "ns=2;s=VASTRAL.4 PUMP.LEVEL_1", "AMC/Storm/amcstorm/VASTRAL/4 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vastralId, "ns=2;s=VASTRAL.4 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/VASTRAL/4 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vastralId, "ns=2;s=VASTRAL.4 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/VASTRAL/4 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vastralId, "ns=2;s=VASTRAL.4 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/VASTRAL/4 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vastralId, "ns=2;s=VASTRAL.4 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/VASTRAL/4 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vastralId, "ns=2;s=VASTRAL.4 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/VASTRAL/4 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vastralId, "ns=2;s=VASTRAL.4 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/VASTRAL/4 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vastralId, "ns=2;s=VASTRAL.4 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/VASTRAL/4 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 8;
    }

    // VEJALPUR tags (complex location with 13 tags)
    const vejalpurId = locationMap['VEJALPUR'];
    if (vejalpurId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vejalpurId, "ns=2;s=VEJALPUR.8 PUMP.LEVEL_1", "AMC/Storm/amcstorm/VEJALPUR/8 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vejalpurId, "ns=2;s=VEJALPUR.8 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vejalpurId, "ns=2;s=VEJALPUR.8 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vejalpurId, "ns=2;s=VEJALPUR.8 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vejalpurId, "ns=2;s=VEJALPUR.8 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vejalpurId, "ns=2;s=VEJALPUR.8 PUMP.PUMP_5_RFB", "AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_5_RFB", "PUMP_5_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vejalpurId, "ns=2;s=VEJALPUR.8 PUMP.PUMP_6_RFB", "AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_6_RFB", "PUMP_6_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vejalpurId, "ns=2;s=VEJALPUR.8 PUMP.PUMP_7_RFB", "AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_7_RFB", "PUMP_7_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vejalpurId, "ns=2;s=VEJALPUR.8 PUMP.PUMP_8_RFB", "AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_8_RFB", "PUMP_8_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vejalpurId, "ns=2;s=VEJALPUR.8 PUMP.PUMP_9_RFB", "AMC/Storm/amcstorm/VEJALPUR/8 PUMP/PUMP_9_RFB", "PUMP_9_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vejalpurId, "ns=2;s=VEJALPUR.8 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/VEJALPUR/8 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vejalpurId, "ns=2;s=VEJALPUR.8 PUMP.SEWAGE_CURRENT_DAY_2", "AMC/Storm/amcstorm/VEJALPUR/8 PUMP/SEWAGE_CURRENT_DAY_2", "SEWAGE_CURRENT_DAY_2", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vejalpurId, "ns=2;s=VEJALPUR.8 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/VEJALPUR/8 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vejalpurId, "ns=2;s=VEJALPUR.8 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/VEJALPUR/8 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 13;
    }

    // VINZOAL TERMINAL tags (complex location with 18 tags)
    const vinzoalTerminalId = locationMap['VINZOAL TERMINAL'];
    if (vinzoalTerminalId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.LEVEL_1", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.PREVEOUS DAY TOTALIZER", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PREVEOUS DAY TOTALIZER", "PREVEOUS DAY TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_10_RFB", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_10_RFB", "PUMP_10_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_5_RFB", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_5_RFB", "PUMP_5_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_6_RFB", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_6_RFB", "PUMP_6_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_7_RFB", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_7_RFB", "PUMP_7_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_8_RFB", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_8_RFB", "PUMP_8_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.PUMP_9_RFB", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/PUMP_9_RFB", "PUMP_9_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.SEWAGE_CURRENT_DAY_2", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/SEWAGE_CURRENT_DAY_2", "SEWAGE_CURRENT_DAY_2", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.SEWAGE_CURRENT_DAY_3", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/SEWAGE_CURRENT_DAY_3", "SEWAGE_CURRENT_DAY_3", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.SEWAGE_CURRENT_DAY_4", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/SEWAGE_CURRENT_DAY_4", "SEWAGE_CURRENT_DAY_4", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.SEWAGE_TOTALIZER_1", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/SEWAGE_TOTALIZER_1", "SEWAGE_TOTALIZER_1", "TOTALIZER", "ML", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.SEWAGE_TOTALIZER_2", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/SEWAGE_TOTALIZER_2", "SEWAGE_TOTALIZER_2", "TOTALIZER", "ML", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.SEWAGE_TOTALIZER_3", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/SEWAGE_TOTALIZER_3", "SEWAGE_TOTALIZER_3", "TOTALIZER", "ML", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vinzoalTerminalId, "ns=2;s=VINZOAL TERMINAL.6 PUMP.SEWAGE_TOTALIZER_4", "AMC/Storm/amcstorm/VINZOAL TERMINAL/6 PUMP/SEWAGE_TOTALIZER_4", "SEWAGE_TOTALIZER_4", "TOTALIZER", "ML", 0, null]);
      importedCount += 18;
    }

    // VIRATNAGAR tags (complex location with 16 tags)
    const viratnagarId = locationMap['VIRATNAGAR'];
    if (viratnagarId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [viratnagarId, "ns=2;s=VIRATNAGAR.8 PUMP.HT_RFB", "AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/HT_RFB", "HT_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [viratnagarId, "ns=2;s=VIRATNAGAR.8 PUMP.LEVEL_1", "AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [viratnagarId, "ns=2;s=VIRATNAGAR.8 PUMP.LT_1_RFB", "AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/LT_1_RFB", "LT_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [viratnagarId, "ns=2;s=VIRATNAGAR.8 PUMP.LT_2_RFB", "AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/LT_2_RFB", "LT_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [viratnagarId, "ns=2;s=VIRATNAGAR.8 PUMP.LT_3_RFB", "AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/LT_3_RFB", "LT_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [viratnagarId, "ns=2;s=VIRATNAGAR.8 PUMP.PREVEOUS DAY TOTALIZER", "AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PREVEOUS DAY TOTALIZER", "PREVEOUS DAY TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [viratnagarId, "ns=2;s=VIRATNAGAR.8 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [viratnagarId, "ns=2;s=VIRATNAGAR.8 PUMP.PUMP_2_RFB", "AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PUMP_2_RFB", "PUMP_2_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [viratnagarId, "ns=2;s=VIRATNAGAR.8 PUMP.PUMP_3_RFB", "AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PUMP_3_RFB", "PUMP_3_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [viratnagarId, "ns=2;s=VIRATNAGAR.8 PUMP.PUMP_4_RFB", "AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PUMP_4_RFB", "PUMP_4_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [viratnagarId, "ns=2;s=VIRATNAGAR.8 PUMP.PUMP_5_RFB", "AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PUMP_5_RFB", "PUMP_5_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [viratnagarId, "ns=2;s=VIRATNAGAR.8 PUMP.PUMP_6_RFB", "AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PUMP_6_RFB", "PUMP_6_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [viratnagarId, "ns=2;s=VIRATNAGAR.8 PUMP.PUMP_7_RFB", "AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PUMP_7_RFB", "PUMP_7_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [viratnagarId, "ns=2;s=VIRATNAGAR.8 PUMP.PUMP_8_RFB", "AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/PUMP_8_RFB", "PUMP_8_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [viratnagarId, "ns=2;s=VIRATNAGAR.8 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [viratnagarId, "ns=2;s=VIRATNAGAR.8 PUMP.SEWAGE_CURRENT_DAY_2", "AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/SEWAGE_CURRENT_DAY_2", "SEWAGE_CURRENT_DAY_2", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [viratnagarId, "ns=2;s=VIRATNAGAR.8 PUMP.SEWAGE_TOTALIZER_1", "AMC/Storm/amcstorm/VIRATNAGAR/8 PUMP/SEWAGE_TOTALIZER_1", "SEWAGE_TOTALIZER_1", "TOTALIZER", "ML", 0, null]);
      importedCount += 16;
    }

    // VYAYAMSHALA tags
    const vyayamshalaId = locationMap['VYAYAMSHALA'];
    if (vyayamshalaId) {
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vyayamshalaId, "ns=2;s=VYAYAMSHALA.1 PUMP.LEVEL_1", "AMC/Storm/amcstorm/VYAYAMSHALA/1 PUMP/LEVEL_1", "LEVEL_1", "LEVEL", "m", 0, 10]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vyayamshalaId, "ns=2;s=VYAYAMSHALA.1 PUMP.PUMP_1_RFB", "AMC/Storm/amcstorm/VYAYAMSHALA/1 PUMP/PUMP_1_RFB", "PUMP_1_RFB", "PUMP_STATUS", null, 0, 1]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vyayamshalaId, "ns=2;s=VYAYAMSHALA.1 PUMP.SEWAGE_CURRENT_DAY_1", "AMC/Storm/amcstorm/VYAYAMSHALA/1 PUMP/SEWAGE_CURRENT_DAY_1", "SEWAGE_CURRENT_DAY_1", "FLOW_RATE", "MLD", 0, 1000]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vyayamshalaId, "ns=2;s=VYAYAMSHALA.1 PUMP.SEWAGE_PREVIOUS_DAY_TOTALIZER", "AMC/Storm/amcstorm/VYAYAMSHALA/1 PUMP/SEWAGE_PREVIOUS_DAY_TOTALIZER", "SEWAGE_PREVIOUS_DAY_TOTALIZER", "TOTALIZER", "MLD", 0, null]);
      await connection.execute(`INSERT IGNORE INTO tags (location_id, opc_address, mqtt_topic, tag_name, tag_type, unit, min_value, max_value) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [vyayamshalaId, "ns=2;s=VYAYAMSHALA.1 PUMP.SEWAGE_TOTALIZER", "AMC/Storm/amcstorm/VYAYAMSHALA/1 PUMP/SEWAGE_TOTALIZER", "SEWAGE_TOTALIZER", "TOTALIZER", "ML", 0, null]);
      importedCount += 5;
    }

    console.log(`✅ Tags created successfully: ${importedCount} tags imported`);

  } catch (error) {
    console.error('❌ Error creating static tags:', error);
  }
};

const createDefaultUsers = async (connection) => {
  try {
    console.log('👤 Creating default users...');

    // Import bcrypt for password hashing
    const bcrypt = await import('bcryptjs');

    // Create default admin user
    const adminPasswordHash = await bcrypt.hash('admin123', 10);
    await connection.execute(`
      INSERT IGNORE INTO admin_users (username, password_hash, email, is_active)
      VALUES (?, ?, ?, ?)
    `, ['admin', adminPasswordHash, '<EMAIL>', true]);

    // Create default app users
    const appUsers = [
      {
        username: 'operator1',
        password: 'operator123',
        email: '<EMAIL>',
        full_name: 'System Operator 1',
        role: 'operator'
      },
      {
        username: 'viewer1',
        password: 'viewer123',
        email: '<EMAIL>',
        full_name: 'System Viewer 1',
        role: 'viewer'
      },
      {
        username: 'manager1',
        password: 'manager123',
        email: '<EMAIL>',
        full_name: 'System Manager 1',
        role: 'manager'
      }
    ];

    for (const user of appUsers) {
      const passwordHash = await bcrypt.hash(user.password, 10);
      await connection.execute(`
        INSERT IGNORE INTO app_users (username, password_hash, email, full_name, role, is_active)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [user.username, passwordHash, user.email, user.full_name, user.role, true]);
    }

    console.log('✅ Default users created successfully');
    console.log('📋 Default login credentials:');
    console.log('   Admin Panel: admin / admin123');
    console.log('   Mobile App: operator1 / operator123');
    console.log('   Mobile App: viewer1 / viewer123');
    console.log('   Mobile App: manager1 / manager123');

  } catch (error) {
    console.error('❌ Error creating default users:', error);
  }
};

// Run the database initialization
createDatabase();
