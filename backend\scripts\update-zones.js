import { executeQuery } from '../config/database.js';

/**
 * <PERSON><PERSON><PERSON> to update zones table for new architecture
 * - Removes Terminal SPS and Critical Pumping Station zone entries
 * - Updates any locations that were assigned to these zones to use location_type instead
 */

async function updateZones() {
  try {
    console.log('Starting zone update process...');

    // First, check if we have any locations assigned to Terminal SPS or Critical Pumping Station zones
    const specialZoneQuery = `
      SELECT id, name, zone_type FROM zones 
      WHERE zone_type IN ('tsps', 'critical')
    `;
    const specialZones = await executeQuery(specialZoneQuery);
    
    console.log('Found special zones:', specialZones);

    // Update locations that were in these special zones
    for (const zone of specialZones) {
      console.log(`Processing zone: ${zone.name} (${zone.zone_type})`);
      
      // Get locations in this zone
      const locationsQuery = `
        SELECT id, name, location_type FROM locations 
        WHERE zone_id = ?
      `;
      const locations = await executeQuery(locationsQuery, [zone.id]);
      
      console.log(`Found ${locations.length} locations in zone ${zone.name}`);
      
      // Update location_type based on zone_type
      const newLocationType = zone.zone_type === 'tsps' ? 'TSPS' : 'CSPS';
      
      for (const location of locations) {
        console.log(`Updating location ${location.name} to type ${newLocationType}`);
        
        // Update location type and move to a regular zone (we'll use the first regular zone)
        const firstRegularZoneQuery = `
          SELECT id FROM zones WHERE zone_type = 'regular' ORDER BY id LIMIT 1
        `;
        const firstRegularZone = await executeQuery(firstRegularZoneQuery);
        
        if (firstRegularZone.length > 0) {
          const updateLocationQuery = `
            UPDATE locations 
            SET location_type = ?, zone_id = ?
            WHERE id = ?
          `;
          await executeQuery(updateLocationQuery, [
            newLocationType,
            firstRegularZone[0].id,
            location.id
          ]);
          
          console.log(`Updated location ${location.name} - moved to zone ${firstRegularZone[0].id} with type ${newLocationType}`);
        } else {
          console.error('No regular zones found! Cannot update location.');
        }
      }
    }

    // Now delete the special zones
    const deleteZonesQuery = `
      DELETE FROM zones 
      WHERE zone_type IN ('tsps', 'critical')
    `;
    const deleteResult = await executeQuery(deleteZonesQuery);
    
    console.log(`Deleted ${deleteResult.affectedRows} special zones`);
    
    console.log('Zone update process completed successfully!');
    
  } catch (error) {
    console.error('Error updating zones:', error);
    throw error;
  }
}

// Run the update if this script is executed directly
updateZones()
  .then(() => {
    console.log('Update completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Update failed:', error);
    process.exit(1);
  });

export { updateZones };