# Dependencies
node_modules/
backend/node_modules/
OPCtoMQTT/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.example
backend/.env
backend/.env.local
backend/.env.development.local
backend/.env.test.local
backend/.env.production.local
OPCtoMQTT/.env
OPCtoMQTT/.env.local
OPCtoMQTT/.env.development.local
OPCtoMQTT/.env.test.local
OPCtoMQTT/.env.production.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
#public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Logs
logs/
backend/logs/
OPCtoMQTT/logs/
*.log
backend/*.log
OPCtoMQTT/*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm
backend/.npm
OPCtoMQTT/.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
backend/.env
OPCtoMQTT/.env

# Database files
*.db
*.sqlite
*.sqlite3
backend/*.db
backend/*.sqlite
backend/*.sqlite3
OPCtoMQTT/*.db
OPCtoMQTT/*.sqlite
OPCtoMQTT/*.sqlite3

# Flutter specific
frontend/.dart_tool/
frontend/.flutter-plugins
frontend/.flutter-plugins-dependencies
frontend/.packages
frontend/.pub-cache/
frontend/.pub/
frontend/build/
frontend/ios/Flutter/flutter_export_environment.sh
frontend/ios/Flutter/Generated.xcconfig
frontend/ios/Runner/GeneratedPluginRegistrant.*
frontend/android/app/src/main/java/io/flutter/plugins/GeneratedPluginRegistrant.java
frontend/android/.gradle/
frontend/android/gradle/
frontend/android/local.properties
frontend/android/key.properties
frontend/android/app/upload-keystore.jks
frontend/android/app/key.jks
frontend/ios/Pods/
frontend/ios/.symlinks/
frontend/ios/Flutter/App.framework
frontend/ios/Flutter/Flutter.framework
frontend/ios/Flutter/Flutter.podspec
frontend/ios/ServiceDefinitions.json
frontend/ios/Runner/GoogleService-Info.plist
frontend/web/
frontend/linux/
frontend/macos/
frontend/windows/

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.lnk

# Linux
*~

# Backup files
*.bak
*.backup
*.old

# Application specific
uploads/
public/uploads/
storage/
data/
backups/
backend/uploads/
backend/public/uploads/
backend/storage/
backend/data/
backend/backups/
OPCtoMQTT/uploads/
OPCtoMQTT/storage/
OPCtoMQTT/data/
OPCtoMQTT/backups/

# SSL certificates
*.pem
*.key
*.crt
*.csr
backend/*.pem
backend/*.key
backend/*.crt
backend/*.csr
OPCtoMQTT/*.pem
OPCtoMQTT/*.key
OPCtoMQTT/*.crt
OPCtoMQTT/*.csr

# Configuration files with sensitive data
config/database.json
config/production.json
config/secrets.json
backend/config/database.json
backend/config/production.json
backend/config/secrets.json
OPCtoMQTT/config/database.json
OPCtoMQTT/config/production.json
OPCtoMQTT/config/secrets.json
OPCtoMQTT/config/opc.json
OPCtoMQTT/config/mqtt.json

# Build artifacts
build/
dist/
out/
backend/build/
backend/dist/
backend/out/
OPCtoMQTT/build/
OPCtoMQTT/dist/
OPCtoMQTT/out/

# Test coverage
coverage/
.nyc_output/

# Linting
.eslintcache

# Package manager lock files (choose one)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Local development
.local
.cache

# Documentation build
docs/_build/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Docker
.dockerignore
docker-compose.override.yml

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

# Crash log files
crash.log

# Exclude all .tfvars files, which are likely to contain sentitive data
*.tfvars

# Ignore override files as they are usually used to override resources locally
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Include override files you do wish to add to version control using negated pattern
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
*tfplan*

# Ignore CLI configuration files
.terraformrc
terraform.rc

# StormApp specific
# MQTT certificates and keys
mqtt-certs/
backend/mqtt-certs/
OPCtoMQTT/mqtt-certs/
OPCtoMQTT/certificates/
*.p12
backend/*.p12
OPCtoMQTT/*.p12

# Database backups
db-backups/
backend/db-backups/
OPCtoMQTT/db-backups/
*.sql.gz
*.sql.bak
backend/*.sql.gz
backend/*.sql.bak
OPCtoMQTT/*.sql.gz
OPCtoMQTT/*.sql.bak

# Log files
logs/
backend/logs/
OPCtoMQTT/logs/
*.log.*
backend/*.log.*
OPCtoMQTT/*.log.*

# Temporary CSV files
temp-data/
import-data/
export-data/
backend/temp-data/
backend/import-data/
backend/export-data/
backend/scripts/temp-data/
OPCtoMQTT/temp-data/
OPCtoMQTT/import-data/
OPCtoMQTT/export-data/

# Generated documentation
api-docs/
docs/generated/
backend/api-docs/
backend/docs/generated/
OPCtoMQTT/api-docs/
OPCtoMQTT/docs/generated/

# Performance monitoring
newrelic_agent.log
backend/newrelic_agent.log
OPCtoMQTT/newrelic_agent.log
.pm2/
backend/.pm2/
OPCtoMQTT/.pm2/

# Security
secrets/
private/
.secrets
backend/secrets/
backend/private/
backend/.secrets
OPCtoMQTT/secrets/
OPCtoMQTT/private/
OPCtoMQTT/.secrets

# Local configuration overrides
config.local.js
config.local.json
backend/config.local.js
backend/config.local.json
OPCtoMQTT/config.local.js
OPCtoMQTT/config.local.json

# Development tools
.vscode/settings.json
.idea/workspace.xml

# Compiled assets
public/js/compiled/
public/css/compiled/
backend/public/js/compiled/
backend/public/css/compiled/

# Cache directories
.cache/
.tmp/
backend/.cache/
backend/.tmp/
OPCtoMQTT/.cache/
OPCtoMQTT/.tmp/

# Backend specific
backend/scripts/fix-tag-names.js
backend/scripts/temp-*.js
backend/package-lock.json.backup
backend/npm-debug.log*
backend/yarn-debug.log*
backend/yarn-error.log*

# OPCtoMQTT specific
OPCtoMQTT/scripts/temp-*.js
OPCtoMQTT/package-lock.json.backup
OPCtoMQTT/npm-debug.log*
OPCtoMQTT/yarn-debug.log*
OPCtoMQTT/yarn-error.log*
OPCtoMQTT/opc-data/
OPCtoMQTT/mqtt-data/
OPCtoMQTT/connection-logs/

# Flutter specific files
frontend/pubspec.lock
frontend/.metadata
frontend/.flutter-plugins-dependencies
frontend/analysis_options.yaml

# Package manager lock files (uncomment to ignore)
# backend/package-lock.json
# backend/yarn.lock
# backend/pnpm-lock.yaml
# OPCtoMQTT/package-lock.json
# OPCtoMQTT/yarn.lock
# OPCtoMQTT/pnpm-lock.yaml

# Test coverage
backend/coverage/
backend/.nyc_output/
OPCtoMQTT/coverage/
OPCtoMQTT/.nyc_output/
frontend/coverage/

# Uploads and temporary files
backend/uploads/
backend/temp/
backend/tmp/
OPCtoMQTT/uploads/
OPCtoMQTT/temp/
OPCtoMQTT/tmp/
frontend/assets/temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Additional Flutter patterns
frontend/.fvm/
frontend/coverage/
frontend/test/.test_coverage.dart
frontend/integration_test/screenshots/
frontend/android/app/debug/
frontend/android/app/profile/
frontend/android/app/release/
frontend/ios/Runner.xcworkspace/xcuserdata/
frontend/ios/Runner.xcodeproj/xcuserdata/
frontend/ios/Runner.xcodeproj/project.xcworkspace/xcuserdata/
frontend/ios/Flutter/flutter_assets/
frontend/macos/Runner.xcworkspace/xcuserdata/
frontend/macos/Runner.xcodeproj/xcuserdata/

# Flutter build outputs
frontend/build/app/outputs/
frontend/build/ios/
frontend/build/macos/
frontend/build/linux/
frontend/build/windows/
frontend/build/web/

# Flutter environment
frontend/.env
frontend/.env.local
frontend/.env.development
frontend/.env.production

# Flutter generated files
frontend/lib/generated/
frontend/lib/l10n/generated/
