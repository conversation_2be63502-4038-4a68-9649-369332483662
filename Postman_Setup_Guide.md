# StormApp API Testing - Postman Setup Guide

## 📁 Files to Import

### 1. Collection File
- **File**: `StormApp_Mobile_API.postman_collection.json`
- **Contains**: All API endpoints organized in folders
- **Import**: File → Import → Upload Files

### 2. Environment File
- **File**: `StormApp_Environment.postman_environment.json`
- **Contains**: Variables for server URL, tokens, IDs
- **Import**: Manage Environments → Import

### 3. Test Data Reference
- **File**: `StormApp_Test_Data.json`
- **Contains**: Sample IDs and test scenarios
- **Usage**: Reference for testing

## 🚀 Quick Start

### Step 1: Import Files
1. Open Postman
2. Click **Import** button
3. Upload `StormApp_Mobile_API.postman_collection.json`
4. Go to **Environments** → **Import**
5. Upload `StormApp_Environment.postman_environment.json`

### Step 2: Set Environment
1. Select **"StormApp Environment"** from environment dropdown
2. Verify `base_url` is set to your server IP
3. Update IP address if needed: `http://YOUR_SERVER_IP:3000/api`

### Step 3: Test Authentication
1. Open **Authentication** folder
2. Run **"Login"** request
3. Check if `access_token` is automatically saved
4. Verify login success in response

### Step 4: Test Main Endpoints
1. Run **"Get All Zones"** (should work with saved token)
2. Run **"Get Location Data"** (main app data)
3. Run **"Get Latest Data"** (real-time data)

## 🔧 Environment Variables

| Variable | Default Value | Description |
|----------|---------------|-------------|
| `base_url` | `http://************:3000/api` | API base URL |
| `access_token` | (auto-filled) | JWT access token |
| `refresh_token` | (auto-filled) | JWT refresh token |
| `zone_id` | `1` | Test zone ID |
| `location_id` | `1` | Test location ID |
| `tag_id` | `3291` | Test tag ID |
| `username` | `operator1` | Test username |
| `password` | `password123` | Test password |

## 📋 Collection Structure

### 🔐 Authentication
- **Login** - Get access token
- **Logout** - End session
- **Refresh Token** - Renew token

### 🗺️ Zones
- **Get All Zones** - List all zones
- **Get Zone by ID** - Single zone details

### 📍 Locations
- **Get All Locations** - List all locations
- **Get Location by ID** - Single location
- **Get Locations by Zone** - Filter by zone
- **Get Location Data (All)** - Main dashboard data
- **Get Location Data by ID** - Single location data

### 🏷️ Tags
- **Get All Tags** - List all tags
- **Get Tag by ID** - Single tag details
- **Get Tags by Location** - Filter by location

### 📊 Data
- **Get Latest Data** - Current readings
- **Get Tag History (24h)** - Historical data
- **Get Tag History (1h)** - Recent data
- **Get Dashboard Data** - Summary data
- **Get Zone Data** - Zone aggregations

### 🧪 Test Scenarios
- **Test Invalid Login** - Error handling
- **Test Without Authorization** - Security
- **Test Invalid Zone ID** - Not found cases

## ⚡ Testing Workflow

### 1. Authentication Flow
```
1. Login → Save tokens
2. Test protected endpoints
3. Refresh token (if needed)
4. Logout
```

### 2. Data Flow Testing
```
1. Get Zones → Note zone IDs
2. Get Locations by Zone → Note location IDs
3. Get Location Data → Verify dashboard data
4. Get Tags by Location → Note tag IDs
5. Get Tag History → Test charts data
```

### 3. Error Testing
```
1. Invalid credentials
2. Missing authorization
3. Invalid resource IDs
4. Expired tokens
```

## 🔍 Response Validation

### Successful Login Response
```json
{
  "message": "Login successful",
  "user": {
    "id": 1,
    "username": "operator1",
    "role": "operator"
  },
  "access_token": "eyJ...",
  "refresh_token": "eyJ..."
}
```

### Location Data Response
```json
[
  {
    "location_id": 1,
    "location_name": "AKHBAR NAGAR",
    "zone_name": "South West",
    "current_day_qty": 12.45,
    "previous_day_qty": 11.23,
    "till_today_qty": 156.78,
    "level": 4.56,
    "pump_statuses": [...]
  }
]
```

## 🛠️ Troubleshooting

### Common Issues
1. **Connection Error**: Check server IP and port
2. **401 Unauthorized**: Run login first
3. **404 Not Found**: Check endpoint URL
4. **Token Expired**: Use refresh token

### Server Configuration
- Ensure backend server is running on port 3000
- Check firewall settings for port access
- Verify database connection

## 📱 Mobile App Endpoints

These endpoints match exactly what the Flutter mobile app uses:
- Dashboard data loading
- Zone and location browsing
- Real-time tag monitoring
- User authentication
- Historical data charts

## 🎯 Testing Priorities

### High Priority
1. Authentication (login/logout)
2. Location data (main app functionality)
3. Zone data (dashboard)

### Medium Priority
1. Tag details and history
2. Error scenarios
3. Token refresh

### Low Priority
1. Edge cases
2. Performance testing
3. Load testing

---

**Ready to test!** Import the files and start with the Login request in the Authentication folder.
