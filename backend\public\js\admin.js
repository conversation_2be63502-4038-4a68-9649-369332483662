// Global variables
let socket;
let currentSection = 'dashboard';
let authToken = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication first
    checkAuthentication().then(isAuthenticated => {
        if (!isAuthenticated) {
            return; // Will redirect to login
        }

        initializeWebSocket();
        loadDashboard();
        
        // Load current user info
        loadCurrentUserInfo();

        // Load initial data
        refreshData();

        // Add logout functionality
        setupLogout();
    });
});

// Authentication functions
async function checkAuthentication() {
    authToken = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');

    if (!authToken) {
        redirectToLogin();
        return false;
    }

    // Verify token with server
    return await verifyToken();
}

async function verifyToken() {
    try {
        const response = await fetch('/api/auth/verify', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ token: authToken })
        });

        const data = await response.json();

        if (!data.success) {
            redirectToLogin();
            return false;
        }

        // Update user info in UI if needed
        updateUserInfo(data.user);
        return true;
    } catch (error) {
        console.error('Token verification failed:', error);
        redirectToLogin();
        return false;
    }
}

function redirectToLogin() {
    localStorage.removeItem('auth_token');
    sessionStorage.removeItem('auth_token');
    window.location.href = '/admin/login.html';
}

function updateUserInfo(user) {
    // Update user info in the UI
    const userElement = document.getElementById('user-info');
    if (userElement) {
        userElement.textContent = `Welcome, ${user.username}`;
    }

    // Show logout button
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.style.display = 'block';
    }
}

function setupLogout() {
    // Logout button is already in HTML, just ensure it's visible after auth
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.style.display = 'block';
    }
}

async function logout() {
    try {
        await fetch('/api/auth/logout', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });
    } catch (error) {
        console.error('Logout error:', error);
    } finally {
        localStorage.removeItem('auth_token');
        sessionStorage.removeItem('auth_token');
        window.location.href = '/admin/login.html';
    }
}

// Enhanced fetch function with authentication
async function authenticatedFetch(url, options = {}) {
    if (!authToken) {
        redirectToLogin();
        return;
    }

    const defaultOptions = {
        headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
            ...options.headers
        }
    };

    const response = await fetch(url, { ...options, ...defaultOptions });

    if (response.status === 401) {
        redirectToLogin();
        return;
    }

    return response;
}

// WebSocket initialization
function initializeWebSocket() {
    socket = io();

    socket.on('connect', function() {
        console.log('Connected to server');
        updateWebSocketStatus(true);
    });

    socket.on('disconnect', function() {
        console.log('Disconnected from server');
        updateWebSocketStatus(false);
    });

    socket.on('tagData', function(data) {
        handleRealtimeData(data);
    });

    socket.on('connected', function(data) {
        console.log('Server message:', data.message);
    });
}

function updateWebSocketStatus(connected) {
    const statusElement = document.getElementById('ws-status');
    const statusText = document.getElementById('ws-status-text');

    if (statusElement && statusText) {
        if (connected) {
            statusElement.className = 'status-indicator status-online';
            statusText.textContent = 'Connected';
        } else {
            statusElement.className = 'status-indicator status-offline';
            statusText.textContent = 'Disconnected';
        }
    }
}

// Navigation functions
function showSection(sectionName) {
    // Close mobile sidebar when navigating
    closeMobileSidebar();

    // Hide all sections
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => section.style.display = 'none');

    // Show selected section
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
        targetSection.style.display = 'block';
    }

    // Update navigation
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => link.classList.remove('active'));
    if (event && event.target) {
        event.target.classList.add('active');
    }

    // Update page title
    const pageTitle = document.getElementById('page-title');
    pageTitle.textContent = sectionName.charAt(0).toUpperCase() + sectionName.slice(1);

    currentSection = sectionName;

    // Load section-specific data
    switch(sectionName) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'zones':
            loadZones();
            break;
        case 'locations':
            loadLocations();
            break;
        case 'tags':
            loadTags();
            break;
        case 'users':
            loadUsers();
            break;
        case 'realtime':
            loadRealtimeData();
            break;
    }
}

// Dashboard functions
async function loadDashboard() {
    try {
        const response = await authenticatedFetch('/api/data/dashboard');
        if (!response) return;
        const data = await response.json();

        // Update statistics
        document.getElementById('total-zones').textContent = data.statistics.total_zones;
        document.getElementById('total-locations').textContent = data.statistics.total_locations;
        document.getElementById('active-tags').textContent = data.statistics.total_tags;
        document.getElementById('data-points').textContent = data.statistics.active_tags_last_hour;

        // Update zones overview table
        updateZonesOverviewTable(data.zones);

        // Update alerts
        updateAlertsContainer(data.alerts);

    } catch (error) {
        console.error('Error loading dashboard:', error);
        showAlert('Error loading dashboard data', 'danger');
    }
}

function updateZonesOverviewTable(zones) {
    const tbody = document.querySelector('#zones-overview-table tbody');
    tbody.innerHTML = '';

    zones.forEach(zone => {
        const row = document.createElement('tr');
        const statusClass = zone.active_tags > 0 ? 'status-online' : 'status-offline';
        const statusText = zone.active_tags > 0 ? 'Active' : 'Inactive';

        row.innerHTML = `
            <td>${zone.name}</td>
            <td>${zone.location_count}</td>
            <td>${zone.tag_count}</td>
            <td>${zone.active_tags}</td>
            <td>
                <span class="status-indicator ${statusClass}"></span>
                ${statusText}
            </td>
        `;
        tbody.appendChild(row);
    });
}

function updateAlertsContainer(alerts) {
    const container = document.getElementById('alerts-container');
    container.innerHTML = '';

    if (alerts.length === 0) {
        container.innerHTML = '<p class="text-muted">No recent alerts</p>';
        return;
    }

    alerts.forEach(alert => {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-warning alert-sm mb-2';
        alertDiv.innerHTML = `
            <small>
                <strong>${alert.zone_name} - ${alert.location_name}</strong><br>
                ${alert.tag_name}: ${alert.value}
                ${alert.value < alert.min_value ? '(Below minimum)' : '(Above maximum)'}
            </small>
        `;
        container.appendChild(alertDiv);
    });
}

// Zones management
async function loadZones() {
    try {
        const response = await authenticatedFetch('/api/zones');
        if (!response) return;
        const zones = await response.json();
        updateZonesTable(zones);
    } catch (error) {
        console.error('Error loading zones:', error);
        showAlert('Error loading zones', 'danger');
    }
}

function updateZonesTable(zones) {
    const tbody = document.querySelector('#zones-table tbody');
    tbody.innerHTML = '';

    zones.forEach(zone => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${zone.id}</td>
            <td>${zone.name}</td>
            <td>${zone.description || '-'}</td>
            <td>${zone.location_count}</td>
            <td>${new Date(zone.created_at).toLocaleDateString()}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editZone(${zone.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteZone(${zone.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function showAddZoneModal() {
    const modal = new bootstrap.Modal(document.getElementById('addZoneModal'));
    document.getElementById('addZoneForm').reset();
    modal.show();
}

async function saveZone() {
    const form = document.getElementById('addZoneForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    try {
        const response = await authenticatedFetch('/api/zones', {
            method: 'POST',
            body: JSON.stringify(data)
        });

        if (!response) return;

        if (response.ok) {
            showAlert('Zone created successfully', 'success');
            bootstrap.Modal.getInstance(document.getElementById('addZoneModal')).hide();
            loadZones();
        } else {
            const error = await response.json();
            showAlert(error.error || 'Error creating zone', 'danger');
        }
    } catch (error) {
        console.error('Error saving zone:', error);
        showAlert('Error saving zone', 'danger');
    }
}

// Locations management
async function loadLocations() {
    try {
        const response = await authenticatedFetch('/api/locations');
        if (!response) return;
        const locations = await response.json();
        locationsData = locations; // Store for filtering
        updateLocationsTable(locations);

        // Also load zones for the modal
        const zonesResponse = await authenticatedFetch('/api/zones');
        if (!zonesResponse) return;
        const zones = await zonesResponse.json();
        updateZoneSelect(zones);

        // Populate location filter dropdowns
        populateLocationFilters(locations);

        // Update count
        const countElement = document.getElementById('locations-count');
        if (countElement) {
            countElement.textContent = `${locations.length} locations`;
        }
    } catch (error) {
        console.error('Error loading locations:', error);
        showAlert('Error loading locations', 'danger');
    }
}

function populateLocationFilters(locations) {
    // Populate zone filter for locations page
    const zones = [...new Set(locations.map(l => l.zone_name))].sort();
    const zoneFilter = document.getElementById('locations-zone-filter');
    if (zoneFilter) {
        zoneFilter.innerHTML = '<option value="">All Zones</option>';
        zones.forEach(zone => {
            const option = document.createElement('option');
            option.value = zone;
            option.textContent = zone;
            zoneFilter.appendChild(option);
        });
    }
}

function updateLocationsTable(locations) {
    const tbody = document.querySelector('#locations-table tbody');
    tbody.innerHTML = '';

    locations.forEach(location => {
        const row = document.createElement('tr');

        // Format operator info
        const operatorInfo = location.operator_name || '-';
        const contactInfo = [
            location.operator_number ? `Op: ${location.operator_number}` : '',
            location.supervisor_number ? `Sup: ${location.supervisor_number}` : '',
            location.torrent_contact ? `Torrent: ${location.torrent_contact}` : ''
        ].filter(info => info).join('<br>') || '-';

        // Format location type with badge
        const locationTypeColor = {
            'CSPS': 'danger',
            'SWPS': 'primary', 
            'TSPS': 'warning'
        }[location.location_type] || 'secondary';

        row.innerHTML = `
            <td>${location.id}</td>
            <td>${location.zone_name}</td>
            <td>${location.name}</td>
            <td>${location.description || '-'}</td>
            <td><span class="badge bg-${locationTypeColor}">${location.location_type || 'SWPS'}</span></td>
            <td>${location.pump_count}</td>
            <td>${location.tag_count}</td>
            <td>${operatorInfo}</td>
            <td><small>${contactInfo}</small></td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editLocation(${location.id})" title="Edit Location">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="viewLocationDetails(${location.id})" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteLocation(${location.id})" title="Delete Location">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function updateZoneSelect(zones) {
    const select = document.querySelector('#addLocationForm select[name="zone_id"]');
    select.innerHTML = '<option value="">Select Zone</option>';

    zones.forEach(zone => {
        const option = document.createElement('option');
        option.value = zone.id;
        option.textContent = zone.name;
        select.appendChild(option);
    });
}

function showAddLocationModal() {
    const modal = new bootstrap.Modal(document.getElementById('addLocationModal'));
    document.getElementById('addLocationForm').reset();
    modal.show();
}

async function saveLocation() {
    const form = document.getElementById('addLocationForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    try {
        const response = await authenticatedFetch('/api/locations', {
            method: 'POST',
            body: JSON.stringify(data)
        });

        if (!response) return;

        if (response.ok) {
            showAlert('Location created successfully', 'success');
            bootstrap.Modal.getInstance(document.getElementById('addLocationModal')).hide();
            loadLocations();
        } else {
            const error = await response.json();
            showAlert(error.error || 'Error creating location', 'danger');
        }
    } catch (error) {
        console.error('Error saving location:', error);
        showAlert('Error saving location', 'danger');
    }
}

// Tags management
async function loadTags() {
    try {
        const response = await authenticatedFetch('/api/tags');
        if (!response) return;
        const tags = await response.json();
        tagsData = tags; // Store for filtering
        updateTagsTable(tags);

        // Also load locations for the modal
        const locationsResponse = await authenticatedFetch('/api/locations');
        if (!locationsResponse) return;
        const locations = await locationsResponse.json();
        locationsData = locations; // Store for filtering
        updateLocationSelect(locations);

        // Populate tag filter dropdowns
        populateTagFilters(tags, locations);

        // Update count
        const countElement = document.getElementById('tags-count');
        if (countElement) {
            countElement.textContent = `${tags.length} tags`;
        }
    } catch (error) {
        console.error('Error loading tags:', error);
        showAlert('Error loading tags', 'danger');
    }
}

function populateTagFilters(tags, locations) {
    // Populate zone filter for tags page
    const zones = [...new Set(tags.map(t => t.zone_name))].sort();
    const zoneFilter = document.getElementById('tags-zone-filter');
    if (zoneFilter) {
        zoneFilter.innerHTML = '<option value="">All Zones</option>';
        zones.forEach(zone => {
            const option = document.createElement('option');
            option.value = zone;
            option.textContent = zone;
            zoneFilter.appendChild(option);
        });
    }

    // Populate location filter for tags page
    const locationFilter = document.getElementById('tags-location-filter');
    if (locationFilter) {
        locationFilter.innerHTML = '<option value="">All Locations</option>';
        const tagLocations = [...new Set(tags.map(t => t.location_name))].sort();
        tagLocations.forEach(location => {
            const option = document.createElement('option');
            option.value = location;
            option.textContent = location;
            locationFilter.appendChild(option);
        });
    }

    // Populate realtime filters
    populateRealtimeFilters(tags, locations);
}

function populateRealtimeFilters(tags, locations) {
    // Populate zone filter for realtime page
    const zones = [...new Set(tags.map(t => t.zone_name))].sort();
    const realtimeZoneFilter = document.getElementById('realtime-zone-filter');
    if (realtimeZoneFilter) {
        realtimeZoneFilter.innerHTML = '<option value="">All Zones</option>';
        zones.forEach(zone => {
            const option = document.createElement('option');
            option.value = zone;
            option.textContent = zone;
            realtimeZoneFilter.appendChild(option);
        });
    }

    // Populate location filter for realtime page
    const realtimeLocationFilter = document.getElementById('realtime-location-filter');
    if (realtimeLocationFilter) {
        const allLocations = [...new Set(tags.map(t => t.location_name))].sort();
        realtimeLocationFilter.innerHTML = '<option value="">All Locations</option>';
        allLocations.forEach(location => {
            const option = document.createElement('option');
            option.value = location;
            option.textContent = location;
            realtimeLocationFilter.appendChild(option);
        });
    }
}

function updateTagsTable(tags) {
    const tbody = document.querySelector('#tags-table tbody');
    tbody.innerHTML = '';

    tags.forEach(tag => {
        const row = document.createElement('tr');
        const statusClass = tag.latest_value !== null ? 'status-online' : 'status-offline';
        const statusText = tag.latest_value !== null ? 'Active' : 'No Data';
        const latestValue = tag.latest_value !== null ?
            `${tag.latest_value} ${tag.unit || ''}` : '-';

        row.innerHTML = `
            <td>${tag.id}</td>
            <td>${tag.zone_name}</td>
            <td>${tag.location_name}</td>
            <td>${tag.tag_name}</td>
            <td><span class="badge bg-secondary">${tag.tag_type}</span></td>
            <td><small>${tag.opc_address}</small></td>
            <td>${latestValue}</td>
            <td>
                <span class="status-indicator ${statusClass}"></span>
                ${statusText}
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editTag(${tag.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    ${tag.is_active ? `
                    <button class="btn btn-outline-warning" onclick="disableTag(${tag.id})">
                        <i class="fas fa-ban"></i>
                    </button>
                    ` : `
                    <button class="btn btn-outline-success" onclick="enableTag(${tag.id})">
                        <i class="fas fa-check"></i>
                    </button>
                    `}
                    <button class="btn btn-outline-danger" onclick="deleteTag(${tag.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function updateLocationSelect(locations) {
    const select = document.querySelector('#addTagForm select[name="location_id"]');
    select.innerHTML = '<option value="">Select Location</option>';

    locations.forEach(location => {
        const option = document.createElement('option');
        option.value = location.id;
        option.textContent = `${location.zone_name} - ${location.name}`;
        select.appendChild(option);
    });
}

function showAddTagModal() {
    const modal = new bootstrap.Modal(document.getElementById('addTagModal'));
    document.getElementById('addTagForm').reset();
    modal.show();
}

async function saveTag() {
    const form = document.getElementById('addTagForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // Convert empty strings to null for numeric fields
    if (data.min_value === '') data.min_value = null;
    if (data.max_value === '') data.max_value = null;

    try {
        const response = await authenticatedFetch('/api/tags', {
            method: 'POST',
            body: JSON.stringify(data)
        });

        if (!response) return;

        if (response.ok) {
            showAlert('Tag created successfully', 'success');
            bootstrap.Modal.getInstance(document.getElementById('addTagModal')).hide();
            loadTags();
        } else {
            const error = await response.json();
            showAlert(error.error || 'Error creating tag', 'danger');
        }
    } catch (error) {
        console.error('Error saving tag:', error);
        showAlert('Error saving tag', 'danger');
    }
}

// Real-time data functions
function loadRealtimeData() {
    // Clear existing data
    const tbody = document.querySelector('#realtime-table tbody');
    tbody.innerHTML = '';
}

function handleRealtimeData(data) {
    if (currentSection === 'realtime') {
        const tbody = document.querySelector('#realtime-table tbody');

        // Add new row at the top
        const row = document.createElement('tr');
        const timestamp = new Date(data.timestamp * 1000).toLocaleTimeString();

        row.innerHTML = `
            <td>${timestamp}</td>
            <td>${data.zoneName || '-'}</td>
            <td>${data.locationName || '-'}</td>
            <td>${data.tagName}</td>
            <td>${data.value}</td>
            <td>${data.unit || '-'}</td>
        `;

        tbody.insertBefore(row, tbody.firstChild);

        // Store data
        realtimeData.unshift({
            timestamp: timestamp,
            zone: data.zoneName || '-',
            location: data.locationName || '-',
            tag: data.tagName,
            value: data.value,
            unit: data.unit || '-'
        });

        // Apply current filters
        filterRealtimeData();
    }
}

// Utility functions
function refreshData() {
    switch(currentSection) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'zones':
            loadZones();
            break;
        case 'locations':
            loadLocations();
            break;
        case 'tags':
            loadTags();
            break;
        case 'realtime':
            loadRealtimeData();
            break;
    }
}

function showAlert(message, type = 'info') {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// ===== FILTERING AND SORTING FUNCTIONALITY =====

// Global variables for filtering and sorting
let locationsData = [];
let tagsData = [];
let realtimeData = [];
let realtimePaused = false;

// Initialize filters when sections are loaded
function initializeFilters() {
    // Add event listeners for all filter controls
    setupLocationFilters();
    setupTagFilters();
    setupRealtimeFilters();
    setupTableSorting();
}

// Call initialize filters when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeFilters, 1000); // Delay to ensure elements are loaded
});

// ===== LOCATIONS FILTERING =====
function setupLocationFilters() {
    const zoneFilter = document.getElementById('locations-zone-filter');
    const searchInput = document.getElementById('locations-search');
    const minPumpsInput = document.getElementById('locations-min-pumps');

    if (zoneFilter) zoneFilter.addEventListener('change', filterLocations);
    if (searchInput) searchInput.addEventListener('input', filterLocations);
    if (minPumpsInput) minPumpsInput.addEventListener('input', filterLocations);
}

async function filterLocations() {
    const zoneFilter = document.getElementById('locations-zone-filter')?.value || '';
    const searchTerm = document.getElementById('locations-search')?.value.toLowerCase() || '';
    const minPumps = parseInt(document.getElementById('locations-min-pumps')?.value) || 0;

    let filteredData = [...locationsData];

    // Apply filters
    if (zoneFilter) {
        filteredData = filteredData.filter(location => location.zone_name === zoneFilter);
    }
    if (searchTerm) {
        filteredData = filteredData.filter(location =>
            location.name.toLowerCase().includes(searchTerm) ||
            location.description?.toLowerCase().includes(searchTerm)
        );
    }
    if (minPumps > 0) {
        filteredData = filteredData.filter(location => location.pump_count >= minPumps);
    }

    // Update table
    updateLocationsTable(filteredData);

    // Update count
    const countElement = document.getElementById('locations-count');
    if (countElement) {
        countElement.textContent = `${filteredData.length} locations`;
    }
}

function clearLocationsFilters() {
    document.getElementById('locations-zone-filter').value = '';
    document.getElementById('locations-search').value = '';
    document.getElementById('locations-min-pumps').value = '';
    filterLocations();
}

// ===== TAGS FILTERING =====
function setupTagFilters() {
    const zoneFilter = document.getElementById('tags-zone-filter');
    const locationFilter = document.getElementById('tags-location-filter');
    const typeFilter = document.getElementById('tags-type-filter');
    const statusFilter = document.getElementById('tags-status-filter');
    const searchInput = document.getElementById('tags-search');

    if (zoneFilter) zoneFilter.addEventListener('change', () => {
        updateTagLocationFilter();
        filterTags();
    });
    if (locationFilter) locationFilter.addEventListener('change', filterTags);
    if (typeFilter) typeFilter.addEventListener('change', filterTags);
    if (statusFilter) statusFilter.addEventListener('change', filterTags);
    if (searchInput) searchInput.addEventListener('input', filterTags);
}

function updateTagLocationFilter() {
    const zoneFilter = document.getElementById('tags-zone-filter')?.value || '';
    const locationFilter = document.getElementById('tags-location-filter');

    if (!locationFilter || !tagsData.length) return;

    locationFilter.innerHTML = '<option value="">All Locations</option>';

    // Get locations from tags data, filtered by zone if selected
    let filteredLocations;
    if (zoneFilter) {
        filteredLocations = [...new Set(tagsData
            .filter(tag => tag.zone_name === zoneFilter)
            .map(tag => tag.location_name))].sort();
    } else {
        filteredLocations = [...new Set(tagsData.map(tag => tag.location_name))].sort();
    }

    filteredLocations.forEach(location => {
        const option = document.createElement('option');
        option.value = location;
        option.textContent = location;
        locationFilter.appendChild(option);
    });
}

async function filterTags() {
    const zoneFilter = document.getElementById('tags-zone-filter')?.value || '';
    const locationFilter = document.getElementById('tags-location-filter')?.value || '';
    const typeFilter = document.getElementById('tags-type-filter')?.value || '';
    const statusFilter = document.getElementById('tags-status-filter')?.value || '';
    const searchTerm = document.getElementById('tags-search')?.value.toLowerCase() || '';

    let filteredData = [...tagsData];

    // Apply filters
    if (zoneFilter) {
        filteredData = filteredData.filter(tag => tag.zone_name === zoneFilter);
    }
    if (locationFilter) {
        filteredData = filteredData.filter(tag => tag.location_name === locationFilter);
    }
    if (typeFilter) {
        filteredData = filteredData.filter(tag => tag.tag_type === typeFilter);
    }
    if (statusFilter) {
        if (statusFilter === 'active') {
            filteredData = filteredData.filter(tag => tag.is_active);
        } else if (statusFilter === 'inactive') {
            filteredData = filteredData.filter(tag => !tag.is_active);
        }
    }
    if (searchTerm) {
        filteredData = filteredData.filter(tag =>
            tag.tag_name.toLowerCase().includes(searchTerm) ||
            tag.opc_address.toLowerCase().includes(searchTerm)
        );
    }

    // Update table
    updateTagsTable(filteredData);

    // Update count
    const countElement = document.getElementById('tags-count');
    if (countElement) {
        countElement.textContent = `${filteredData.length} tags`;
    }
}

function clearTagsFilters() {
    document.getElementById('tags-zone-filter').value = '';
    document.getElementById('tags-location-filter').value = '';
    document.getElementById('tags-type-filter').value = '';
    document.getElementById('tags-status-filter').value = '';
    document.getElementById('tags-search').value = '';
    updateTagLocationFilter();
    filterTags();
}

// ===== REAL-TIME FILTERING =====
function setupRealtimeFilters() {
    const zoneFilter = document.getElementById('realtime-zone-filter');
    const locationFilter = document.getElementById('realtime-location-filter');
    const tagFilter = document.getElementById('realtime-tag-filter');
    const searchInput = document.getElementById('realtime-search');
    const maxRowsSelect = document.getElementById('realtime-max-rows');

    if (zoneFilter) zoneFilter.addEventListener('change', () => {
        updateRealtimeLocationFilter();
        filterRealtimeData();
    });
    if (locationFilter) locationFilter.addEventListener('change', filterRealtimeData);
    if (tagFilter) tagFilter.addEventListener('change', filterRealtimeData);
    if (searchInput) searchInput.addEventListener('input', filterRealtimeData);
    if (maxRowsSelect) maxRowsSelect.addEventListener('change', filterRealtimeData);
}

function updateRealtimeLocationFilter() {
    const zoneFilter = document.getElementById('realtime-zone-filter')?.value || '';
    const locationFilter = document.getElementById('realtime-location-filter');

    if (!locationFilter || !tagsData.length) return;

    locationFilter.innerHTML = '<option value="">All Locations</option>';

    // Get locations from tags data, filtered by zone if selected
    let filteredLocations;
    if (zoneFilter) {
        filteredLocations = [...new Set(tagsData
            .filter(tag => tag.zone_name === zoneFilter)
            .map(tag => tag.location_name))].sort();
    } else {
        filteredLocations = [...new Set(tagsData.map(tag => tag.location_name))].sort();
    }

    filteredLocations.forEach(location => {
        const option = document.createElement('option');
        option.value = location;
        option.textContent = location;
        locationFilter.appendChild(option);
    });
}

function filterRealtimeData() {
    const zoneFilter = document.getElementById('realtime-zone-filter')?.value || '';
    const locationFilter = document.getElementById('realtime-location-filter')?.value || '';
    const tagFilter = document.getElementById('realtime-tag-filter')?.value || '';
    const searchTerm = document.getElementById('realtime-search')?.value.toLowerCase() || '';
    const maxRows = parseInt(document.getElementById('realtime-max-rows')?.value) || 50;

    const tbody = document.querySelector('#realtime-table tbody');
    const rows = Array.from(tbody.children);

    let visibleCount = 0;
    rows.forEach(row => {
        const cells = row.children;
        const zone = cells[1]?.textContent || '';
        const location = cells[2]?.textContent || '';
        const tag = cells[3]?.textContent || '';

        let visible = true;

        // Apply filters
        if (zoneFilter && zone !== zoneFilter) visible = false;
        if (locationFilter && location !== locationFilter) visible = false;
        if (tagFilter && !tag.includes(tagFilter)) visible = false;
        if (searchTerm && !tag.toLowerCase().includes(searchTerm)) visible = false;

        // Apply max rows limit
        if (visible && visibleCount >= maxRows) visible = false;

        if (visible) visibleCount++;

        row.style.display = visible ? '' : 'none';
    });

    // Update count
    const countElement = document.getElementById('realtime-count');
    if (countElement) {
        countElement.textContent = `${visibleCount} readings`;
    }
}

function clearRealtimeFilters() {
    document.getElementById('realtime-zone-filter').value = '';
    document.getElementById('realtime-location-filter').value = '';
    document.getElementById('realtime-tag-filter').value = '';
    document.getElementById('realtime-search').value = '';
    document.getElementById('realtime-max-rows').value = '50';
    updateRealtimeLocationFilter();
    filterRealtimeData();
}

function pauseRealtimeData() {
    realtimePaused = !realtimePaused;
    const btn = document.getElementById('pause-btn');
    const table = document.getElementById('realtime-table');

    if (realtimePaused) {
        btn.innerHTML = '<i class="fas fa-play"></i> Resume';
        btn.className = 'btn btn-outline-success btn-sm ms-2';
        table.classList.add('realtime-paused');
    } else {
        btn.innerHTML = '<i class="fas fa-pause"></i> Pause';
        btn.className = 'btn btn-outline-primary btn-sm ms-2';
        table.classList.remove('realtime-paused');
    }
}

function clearRealtimeData() {
    const tbody = document.querySelector('#realtime-table tbody');
    tbody.innerHTML = '';
    realtimeData = [];

    const countElement = document.getElementById('realtime-count');
    if (countElement) {
        countElement.textContent = '0 readings';
    }
}

// ===== TABLE SORTING =====
function setupTableSorting() {
    // Add click handlers to sortable headers
    document.querySelectorAll('.sortable').forEach(header => {
        header.addEventListener('click', function() {
            const table = this.closest('table');
            const column = this.dataset.column;
            const currentSort = this.classList.contains('sort-asc') ? 'asc' :
                              this.classList.contains('sort-desc') ? 'desc' : '';

            // Remove sort classes from all headers in this table
            table.querySelectorAll('.sortable').forEach(h => {
                h.classList.remove('sort-asc', 'sort-desc');
            });

            // Determine new sort direction
            let newSort = 'asc';
            if (currentSort === 'asc') newSort = 'desc';

            // Add sort class to clicked header
            this.classList.add(`sort-${newSort}`);

            // Sort the table
            sortTable(table, column, newSort);
        });
    });
}

function sortTable(table, column, direction) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.children);

    rows.sort((a, b) => {
        const aVal = getCellValue(a, column);
        const bVal = getCellValue(b, column);

        if (direction === 'desc') {
            return bVal > aVal ? 1 : -1;
        }
        return aVal > bVal ? 1 : -1;
    });

    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
}

function getCellValue(row, column) {
    const columnMap = {
        'id': 0,
        'zone_name': 1,
        'name': 2,
        'location_name': 2,
        'tag_name': 3,
        'tag_type': 4,
        'latest_value': 6,
        'is_active': 7,
        'pump_count': 4,
        'tag_count': 5,
        'time': 0,
        'zone': 1,
        'location': 2,
        'tag': 3,
        'value': 4
    };

    const cellIndex = columnMap[column];
    if (cellIndex === undefined) return '';

    const cell = row.children[cellIndex];
    if (!cell) return '';

    let value = cell.textContent.trim();

    // Try to parse as number if it looks like one
    if (/^\d+\.?\d*$/.test(value)) {
        return parseFloat(value);
    }

    return value.toLowerCase();
}

// ===== UPDATE EXISTING FUNCTIONS TO STORE DATA =====

// This function is already defined above with authenticatedFetch - remove this duplicate

// This duplicate function is removed - the original loadTags function above already uses authenticatedFetch

// Update the existing handleRealtimeData function
const originalHandleRealtimeData = handleRealtimeData;
function handleRealtimeData(data) {
    if (realtimePaused) return;

    if (currentSection === 'realtime') {
        const tbody = document.querySelector('#realtime-table tbody');

        // Add new row at the top
        const row = document.createElement('tr');
        const timestamp = new Date(data.timestamp * 1000).toLocaleTimeString();

        row.innerHTML = `
            <td>${timestamp}</td>
            <td>${data.zoneName || '-'}</td>
            <td>${data.locationName || '-'}</td>
            <td>${data.tagName}</td>
            <td>${data.value}</td>
            <td>${data.unit || '-'}</td>
        `;

        tbody.insertBefore(row, tbody.firstChild);

        // Store data
        realtimeData.unshift({
            timestamp: timestamp,
            zone: data.zoneName || '-',
            location: data.locationName || '-',
            tag: data.tagName,
            value: data.value,
            unit: data.unit || '-'
        });

        // Apply current filters
        filterRealtimeData();
    }
}

// Zone management functions
async function editZone(id) {
    try {
        const response = await authenticatedFetch(`/api/zones/${id}`);
        if (!response) return;
        const zone = await response.json();

        // Populate edit form
        document.getElementById('editZoneId').value = zone.id;
        document.getElementById('editZoneName').value = zone.name;
        document.getElementById('editZoneDescription').value = zone.description || '';

        // Show edit modal
        const modal = new bootstrap.Modal(document.getElementById('editZoneModal'));
        modal.show();
    } catch (error) {
        console.error('Error loading zone:', error);
        showAlert('Error loading zone data', 'danger');
    }
}

async function updateZone() {
    const id = document.getElementById('editZoneId').value;
    const name = document.getElementById('editZoneName').value;
    const description = document.getElementById('editZoneDescription').value;

    try {
        const response = await authenticatedFetch(`/api/zones/${id}`, {
            method: 'PUT',
            body: JSON.stringify({ name, description })
        });

        if (!response) return;

        if (response.ok) {
            showAlert('Zone updated successfully', 'success');
            bootstrap.Modal.getInstance(document.getElementById('editZoneModal')).hide();
            loadZones();
        } else {
            const error = await response.json();
            showAlert(error.error || 'Error updating zone', 'danger');
        }
    } catch (error) {
        console.error('Error updating zone:', error);
        showAlert('Error updating zone', 'danger');
    }
}

async function deleteZone(id) {
    if (confirm('Are you sure you want to delete this zone? This will also delete all associated locations and tags.')) {
        try {
            const response = await authenticatedFetch(`/api/zones/${id}`, {
                method: 'DELETE'
            });

            if (!response) return;

            if (response.ok) {
                showAlert('Zone deleted successfully', 'success');
                loadZones();
            } else {
                const error = await response.json();
                showAlert(error.error || 'Error deleting zone', 'danger');
            }
        } catch (error) {
            console.error('Error deleting zone:', error);
            showAlert('Error deleting zone', 'danger');
        }
    }
}

// Location management functions
async function editLocation(id) {
    try {
        const response = await authenticatedFetch(`/api/locations/${id}`);
        if (!response) return;
        const location = await response.json();

        // Load zones for dropdown
        const zonesResponse = await authenticatedFetch('/api/zones');
        if (!zonesResponse) return;
        const zones = await zonesResponse.json();
        updateEditLocationZoneSelect(zones);

        // Populate edit form
        document.getElementById('editLocationId').value = location.id;
        document.getElementById('editLocationZone').value = location.zone_id;
        document.getElementById('editLocationName').value = location.name;
        document.getElementById('editLocationDescription').value = location.description || '';
        document.getElementById('editLocationLocationType').value = location.location_type || 'SWPS';
        document.getElementById('editLocationPumpCount').value = location.pump_count || 0;

        // Populate operator contact fields
        document.getElementById('editLocationOperatorName').value = location.operator_name || '';
        document.getElementById('editLocationOperatorNumber').value = location.operator_number || '';
        document.getElementById('editLocationSupervisorNumber').value = location.supervisor_number || '';
        document.getElementById('editLocationTorrentContact').value = location.torrent_contact || '';
        document.getElementById('editLocationTorrentService1').value = location.torrent_service_1 || '';
        document.getElementById('editLocationSupervisorName').value = location.supervisor_name || '';

        // Show edit modal
        const modal = new bootstrap.Modal(document.getElementById('editLocationModal'));
        modal.show();
    } catch (error) {
        console.error('Error loading location:', error);
        showAlert('Error loading location data', 'danger');
    }
}

function updateEditLocationZoneSelect(zones) {
    const select = document.getElementById('editLocationZone');
    select.innerHTML = '<option value="">Select Zone</option>';

    zones.forEach(zone => {
        const option = document.createElement('option');
        option.value = zone.id;
        option.textContent = zone.name;
        select.appendChild(option);
    });
}

async function updateLocation() {
    const id = document.getElementById('editLocationId').value;
    const zone_id = document.getElementById('editLocationZone').value;
    const name = document.getElementById('editLocationName').value;
    const description = document.getElementById('editLocationDescription').value;
    const location_type = document.getElementById('editLocationLocationType').value;
    const pump_count = document.getElementById('editLocationPumpCount').value;

    // Get operator contact information
    const operator_name = document.getElementById('editLocationOperatorName').value;
    const operator_number = document.getElementById('editLocationOperatorNumber').value;
    const supervisor_number = document.getElementById('editLocationSupervisorNumber').value;
    const torrent_contact = document.getElementById('editLocationTorrentContact').value;
    const torrent_service_1 = document.getElementById('editLocationTorrentService1').value;
    const supervisor_name = document.getElementById('editLocationSupervisorName').value;

    try {
        const response = await authenticatedFetch(`/api/locations/${id}`, {
            method: 'PUT',
            body: JSON.stringify({
                zone_id,
                name,
                description,
                location_type,
                pump_count,
                operator_name,
                operator_number,
                supervisor_number,
                torrent_contact,
                torrent_service_1,
                supervisor_name
            })
        });

        if (!response) return;

        if (response.ok) {
            showAlert('Location updated successfully', 'success');
            bootstrap.Modal.getInstance(document.getElementById('editLocationModal')).hide();
            loadLocations();
        } else {
            const error = await response.json();
            showAlert(error.error || 'Error updating location', 'danger');
        }
    } catch (error) {
        console.error('Error updating location:', error);
        showAlert('Error updating location', 'danger');
    }
}

async function deleteLocation(id) {
    if (confirm('Are you sure you want to delete this location? This will also delete all associated tags.')) {
        try {
            const response = await authenticatedFetch(`/api/locations/${id}`, {
                method: 'DELETE'
            });

            if (!response) return;

            if (response.ok) {
                showAlert('Location deleted successfully', 'success');
                loadLocations();
            } else {
                const error = await response.json();
                showAlert(error.error || 'Error deleting location', 'danger');
            }
        } catch (error) {
            console.error('Error deleting location:', error);
            showAlert('Error deleting location', 'danger');
        }
    }
}

// Tag management functions
async function editTag(id) {
    try {
        const response = await authenticatedFetch(`/api/tags/${id}`);
        if (!response) return;
        const tag = await response.json();

        // Load locations for dropdown
        const locationsResponse = await authenticatedFetch('/api/locations');
        if (!locationsResponse) return;
        const locations = await locationsResponse.json();
        updateEditTagLocationSelect(locations);

        // Populate edit form
        document.getElementById('editTagId').value = tag.id;
        document.getElementById('editTagLocation').value = tag.location_id;
        document.getElementById('editTagName').value = tag.tag_name;
        document.getElementById('editTagType').value = tag.tag_type;
        document.getElementById('editTagOpcAddress').value = tag.opc_address;
        document.getElementById('editTagMqttTopic').value = tag.mqtt_topic || '';
        document.getElementById('editTagUnit').value = tag.unit || '';
        document.getElementById('editTagMinValue').value = tag.min_value || '';
        document.getElementById('editTagMaxValue').value = tag.max_value || '';

        // Show edit modal
        const modal = new bootstrap.Modal(document.getElementById('editTagModal'));
        modal.show();
    } catch (error) {
        console.error('Error loading tag:', error);
        showAlert('Error loading tag data', 'danger');
    }
}

function updateEditTagLocationSelect(locations) {
    const select = document.getElementById('editTagLocation');
    select.innerHTML = '<option value="">Select Location</option>';

    locations.forEach(location => {
        const option = document.createElement('option');
        option.value = location.id;
        option.textContent = `${location.zone_name} - ${location.name}`;
        select.appendChild(option);
    });
}

async function updateTag() {
    const id = document.getElementById('editTagId').value;
    const location_id = document.getElementById('editTagLocation').value;
    const tag_name = document.getElementById('editTagName').value;
    const tag_type = document.getElementById('editTagType').value;
    const opc_address = document.getElementById('editTagOpcAddress').value;
    const mqtt_topic = document.getElementById('editTagMqttTopic').value;
    const unit = document.getElementById('editTagUnit').value;
    const min_value = document.getElementById('editTagMinValue').value || null;
    const max_value = document.getElementById('editTagMaxValue').value || null;

    try {
        const response = await authenticatedFetch(`/api/tags/${id}`, {
            method: 'PUT',
            body: JSON.stringify({
                location_id, tag_name, tag_type, opc_address,
                mqtt_topic, unit, min_value, max_value
            })
        });

        if (!response) return;

        if (response.ok) {
            showAlert('Tag updated successfully', 'success');
            bootstrap.Modal.getInstance(document.getElementById('editTagModal')).hide();
            loadTags();
        } else {
            const error = await response.json();
            showAlert(error.error || 'Error updating tag', 'danger');
        }
    } catch (error) {
        console.error('Error updating tag:', error);
        showAlert('Error updating tag', 'danger');
    }
}

async function deleteTag(id) {
    if (confirm('Are you sure you want to delete this tag?')) {
        try {
            const response = await authenticatedFetch(`/api/tags/${id}`, {
                method: 'DELETE'
            });

            if (!response) return;

            if (response.ok) {
                showAlert('Tag deleted successfully', 'success');
                loadTags();
            } else {
                const error = await response.json();
                showAlert(error.error || 'Error deleting tag', 'danger');
            }
        } catch (error) {
            console.error('Error deleting tag:', error);
            showAlert('Error deleting tag', 'danger');
        }
    }
}

async function disableTag(id) {
    if (confirm('Are you sure you want to disable this tag?')) {
        try {
            const response = await authenticatedFetch(`/api/tags/${id}/disable`, {
                method: 'PUT'
            });
            if (!response) return;
            if (response.ok) {
                showAlert('Tag disabled successfully', 'success');
                loadTags();
            } else {
                const error = await response.json();
                showAlert(error.error || 'Error disabling tag', 'danger');
            }
        } catch (error) {
            console.error('Error disabling tag:', error);
            showAlert('Error disabling tag', 'danger');
        }
    }
}

async function enableTag(id) {
    if (confirm('Are you sure you want to enable this tag?')) {
        try {
            const response = await authenticatedFetch(`/api/tags/${id}/enable`, {
                method: 'PUT'
            });
            if (!response) return;
            if (response.ok) {
                showAlert('Tag enabled successfully', 'success');
                loadTags();
            } else {
                const error = await response.json();
                showAlert(error.error || 'Error enabling tag', 'danger');
            }
        } catch (error) {
            console.error('Error enabling tag:', error);
            showAlert('Error enabling tag', 'danger');
        }
    }
}

// ===== MOBILE SIDEBAR FUNCTIONALITY =====
function toggleMobileSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebar-overlay');

    if (sidebar.classList.contains('show')) {
        sidebar.classList.remove('show');
        overlay.classList.remove('show');
        document.body.style.overflow = '';
    } else {
        sidebar.classList.add('show');
        overlay.classList.add('show');
        document.body.style.overflow = 'hidden';
    }
}

// Close mobile sidebar when clicking on nav links
function closeMobileSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebar-overlay');

    if (window.innerWidth < 768) {
        sidebar.classList.remove('show');
        overlay.classList.remove('show');
        document.body.style.overflow = '';
    }
}

// Handle window resize
window.addEventListener('resize', function() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebar-overlay');

    if (window.innerWidth >= 768) {
        sidebar.classList.remove('show');
        overlay.classList.remove('show');
        document.body.style.overflow = '';
    }
});

// Function to view location details in a modal
function viewLocationDetails(id) {
    authenticatedFetch(`/api/locations/${id}`)
        .then(response => response.json())
        .then(location => {
            // Create a details modal content
            const detailsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Basic Information</h6>
                        <p><strong>Zone:</strong> ${location.zone_name}</p>
                        <p><strong>Name:</strong> ${location.name}</p>
                        <p><strong>Description:</strong> ${location.description || 'N/A'}</p>
                        <p><strong>Pump Count:</strong> ${location.pump_count || 0}</p>
                        <p><strong>Tag Count:</strong> ${location.tag_count || 0}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Contact Information</h6>
                        <p><strong>Operator:</strong> ${location.operator_name || 'N/A'}</p>
                        <p><strong>Operator Number:</strong> ${location.operator_number || 'N/A'}</p>
                        <p><strong>Supervisor Number:</strong> ${location.supervisor_number || 'N/A'}</p>
                        <p><strong>Torrent Contact:</strong> ${location.torrent_contact || 'N/A'}</p>
                        <p><strong>Torrent Service 1:</strong> ${location.torrent_service_1 || 'N/A'}</p>
                        <p><strong>Supervisor Name:</strong> ${location.supervisor_name || 'N/A'}</p>
                    </div>
                </div>
            `;

            // Show details in a simple alert or create a custom modal
            showLocationDetailsModal(location.name, detailsHtml);
        })
        .catch(error => {
            console.error('Error fetching location details:', error);
            showAlert('Failed to load location details', 'danger');
        });
}

// Function to show location details in a modal
function showLocationDetailsModal(title, content) {
    // Create a temporary modal if it doesn't exist
    let detailsModal = document.getElementById('locationDetailsModal');
    if (!detailsModal) {
        const modalHtml = `
            <div class="modal fade" id="locationDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="locationDetailsModalLabel">Location Details</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" id="locationDetailsModalBody">
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        detailsModal = document.getElementById('locationDetailsModal');
    }

    // Update modal content
    document.getElementById('locationDetailsModalLabel').textContent = `Location Details: ${title}`;
    document.getElementById('locationDetailsModalBody').innerHTML = content;

    // Show modal
    const modal = new bootstrap.Modal(detailsModal);
    modal.show();
}

// Utility function to format phone numbers for display
function formatPhoneNumber(phone) {
    if (!phone) return 'N/A';
    // Simple formatting - you can enhance this as needed
    return phone.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
}

// ===== CURRENT USER FUNCTIONALITY =====

// Load current user information
async function loadCurrentUserInfo() {
    try {
        // Decode JWT token to get user info
        const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
        if (!token) return;
        
        // Simple JWT decode (just for getting user info, not for security)
        const payload = JSON.parse(atob(token.split('.')[1]));
        
        if (payload.username) {
            document.getElementById('current-username').textContent = payload.username;
            document.getElementById('user-info').style.display = 'block';
            document.getElementById('change-password-btn').style.display = 'block';
            document.getElementById('logout-btn').style.display = 'block';
            
            // Store current user ID for password changes
            window.currentUserId = payload.id;
        }
    } catch (error) {
        console.error('Error loading current user info:', error);
    }
}

// Change current user's password
function changeMyPassword() {
    if (!window.currentUserId) {
        showAlert('Unable to determine current user', 'danger');
        return;
    }
    
    // Load current user info for password change
    const username = document.getElementById('current-username').textContent;
    
    // Populate password change form
    document.getElementById('changePasswordUserId').value = window.currentUserId;
    document.getElementById('changePasswordUsername').value = username;
    
    // Clear previous form data
    document.getElementById('changePasswordForm').reset();
    document.getElementById('changePasswordUserId').value = window.currentUserId;
    document.getElementById('changePasswordUsername').value = username;
    
    // Always show current password field for admin password changes
    const currentPasswordContainer = document.getElementById('currentPasswordContainer');
    currentPasswordContainer.style.display = 'block';

    // Mark this as admin password change (admin changing their own password)
    window.isOwnPasswordChange = true;
    window.isAdminPasswordChange = true;

    // Update modal title
    document.querySelector('#changePasswordModal .modal-title').textContent = 'Change Admin Password';

    // Show password change modal
    const modal = new bootstrap.Modal(document.getElementById('changePasswordModal'));
    modal.show();
}

// ===== CURRENT USER FUNCTIONALITY =====

// Load current user information
async function loadCurrentUserInfo() {
    try {
        // Decode JWT token to get user info
        const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
        if (!token) return;
        
        // Simple JWT decode (just for getting user info, not for security)
        const payload = JSON.parse(atob(token.split('.')[1]));
        
        if (payload.username) {
            document.getElementById('current-username').textContent = payload.username;
            document.getElementById('user-info').style.display = 'block';
            document.getElementById('change-password-btn').style.display = 'block';
            document.getElementById('logout-btn').style.display = 'block';
            
            // Store current user ID for password changes
            window.currentUserId = payload.id;
        }
    } catch (error) {
        console.error('Error loading current user info:', error);
    }
}

// ===== USER MANAGEMENT FUNCTIONALITY =====

// Load users data
async function loadUsers() {
    try {
        const response = await authenticatedFetch('/api/app-users');
        if (!response) return;
        const users = await response.json();
        
        updateUsersTable(users);
        updateUserStatistics(users);
        loadZonesForUserModal();
        
        // Update count
        const countElement = document.getElementById('users-count');
        if (countElement) {
            countElement.textContent = `${users.length} users`;
        }
    } catch (error) {
        console.error('Error loading users:', error);
        showAlert('Error loading users', 'danger');
    }
}

function updateUsersTable(users) {
    const tbody = document.querySelector('#users-table tbody');
    tbody.innerHTML = '';

    users.forEach(user => {
        const row = document.createElement('tr');
        
        // Format zone access info
        let zoneAccessInfo = '';
        if (user.zone_access_type === 'all') {
            zoneAccessInfo = '<span class="badge bg-success">All Zones</span>';
        } else if (user.zone_access_type === 'specific' && user.allowed_zones) {
            try {
                const zones = JSON.parse(user.allowed_zones);
                zoneAccessInfo = zones.map(z => `<span class="badge bg-primary me-1">Zone ${z}</span>`).join('');
            } catch (e) {
                zoneAccessInfo = '<span class="badge bg-danger">Invalid</span>';
            }
        } else {
            zoneAccessInfo = '<span class="badge bg-secondary">None</span>';
        }

        // Format last login
        const lastLogin = user.last_login ? 
            new Date(user.last_login).toLocaleDateString() : 
            '<span class="text-muted">Never</span>';

        row.innerHTML = `
            <td>${user.id}</td>
            <td><strong>${user.username}</strong></td>
            <td>${user.full_name || '<span class="text-muted">-</span>'}</td>
            <td><span class="badge bg-info">${user.role}</span></td>
            <td><span class="badge bg-${user.zone_access_type === 'all' ? 'success' : user.zone_access_type === 'specific' ? 'warning' : 'secondary'}">${user.zone_access_type}</span></td>
            <td>${zoneAccessInfo}</td>
            <td><span class="badge bg-${user.is_active ? 'success' : 'danger'}">${user.is_active ? 'Active' : 'Inactive'}</span></td>
            <td>${lastLogin}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editUser(${user.id})" title="Edit User">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="changeUserPassword(${user.id})" title="Change Password">
                        <i class="fas fa-key"></i>
                    </button>
                    <button class="btn btn-outline-${user.is_active ? 'warning' : 'success'}" 
                            onclick="toggleUserStatus(${user.id}, ${!user.is_active})" 
                            title="${user.is_active ? 'Deactivate' : 'Activate'} User">
                        <i class="fas fa-${user.is_active ? 'user-slash' : 'user-check'}"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteUser(${user.id})" title="Delete User">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function updateUserStatistics(users) {
    const totalUsers = users.length;
    const superAdmins = users.filter(u => u.zone_access_type === 'all').length;
    const zoneUsers = users.filter(u => u.zone_access_type === 'specific').length;
    const activeUsers = users.filter(u => u.is_active).length;

    document.getElementById('total-users').textContent = totalUsers;
    document.getElementById('super-admins').textContent = superAdmins;
    document.getElementById('zone-users').textContent = zoneUsers;
    document.getElementById('active-users').textContent = activeUsers;
}

// Load zones for user modal
async function loadZonesForUserModal() {
    try {
        const response = await authenticatedFetch('/api/zones');
        if (!response) return;
        const zones = await response.json();
        
        // Filter only regular zones
        const regularZones = zones.filter(zone => typeof zone.id === 'number');
        
        updateZoneCheckboxes('add', regularZones);
        updateZoneCheckboxes('edit', regularZones);
    } catch (error) {
        console.error('Error loading zones for modal:', error);
    }
}

function updateZoneCheckboxes(modalType, zones) {
    const container = document.getElementById(`${modalType}UserZonesList`);
    container.innerHTML = '';

    zones.forEach(zone => {
        const checkboxDiv = document.createElement('div');
        checkboxDiv.className = 'form-check form-check-inline';
        
        checkboxDiv.innerHTML = `
            <input class="form-check-input" type="checkbox" 
                   id="${modalType}Zone${zone.id}" value="${zone.id}">
            <label class="form-check-label" for="${modalType}Zone${zone.id}">
                ${zone.name}
            </label>
        `;
        
        container.appendChild(checkboxDiv);
    });
}

// Toggle zone selection visibility
function toggleZoneSelection(modalType) {
    const accessType = document.getElementById(`${modalType}UserZoneAccessType`).value;
    const container = document.getElementById(`${modalType}UserZonesContainer`);
    
    if (accessType === 'specific') {
        container.style.display = 'block';
    } else {
        container.style.display = 'none';
    }
}

// Show add user modal
function showAddUserModal() {
    const modal = new bootstrap.Modal(document.getElementById('addUserModal'));
    document.getElementById('addUserForm').reset();
    document.getElementById('addUserZonesContainer').style.display = 'none';
    
    // Uncheck all zone checkboxes
    const checkboxes = document.querySelectorAll('#addUserZonesList input[type="checkbox"]');
    checkboxes.forEach(cb => cb.checked = false);
    
    modal.show();
}

// Save new user
async function saveUser() {
    const username = document.getElementById('addUserUsername').value;
    const fullName = document.getElementById('addUserFullName').value;
    const email = document.getElementById('addUserEmail').value;
    const password = document.getElementById('addUserPassword').value;
    const role = document.getElementById('addUserRole').value;
    const zoneAccessType = document.getElementById('addUserZoneAccessType').value;
    
    if (!username || !password || !role || !zoneAccessType) {
        showAlert('Please fill in all required fields', 'warning');
        return;
    }

    let allowedZones = null;
    if (zoneAccessType === 'specific') {
        const checkboxes = document.querySelectorAll('#addUserZonesList input[type="checkbox"]:checked');
        allowedZones = Array.from(checkboxes).map(cb => parseInt(cb.value));
        
        if (allowedZones.length === 0) {
            showAlert('Please select at least one zone for specific access', 'warning');
            return;
        }
    }

    try {
        // Hash password using bcrypt equivalent (you might need to implement this on backend)
        const response = await authenticatedFetch('/api/app-users', {
            method: 'POST',
            body: JSON.stringify({
                username,
                full_name: fullName,
                email,
                password, // Backend should hash this
                role,
                zone_access_type: zoneAccessType,
                allowed_zones: allowedZones
            })
        });

        if (!response) return;

        if (response.ok) {
            showAlert('User created successfully', 'success');
            bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
            loadUsers();
        } else {
            const error = await response.json();
            showAlert(error.error || 'Error creating user', 'danger');
        }
    } catch (error) {
        console.error('Error saving user:', error);
        showAlert('Error saving user', 'danger');
    }
}

// Edit user
async function editUser(id) {
    try {
        const response = await authenticatedFetch(`/api/app-users/${id}`);
        if (!response) return;
        const user = await response.json();

        // Populate edit form
        document.getElementById('editUserId').value = user.id;
        document.getElementById('editUserUsername').value = user.username;
        document.getElementById('editUserFullName').value = user.full_name || '';
        document.getElementById('editUserEmail').value = user.email || '';
        document.getElementById('editUserRole').value = user.role;
        document.getElementById('editUserZoneAccessType').value = user.zone_access_type;
        document.getElementById('editUserStatus').value = user.is_active ? '1' : '0';

        // Handle zone selection
        toggleZoneSelection('edit');
        
        if (user.zone_access_type === 'specific' && user.allowed_zones) {
            try {
                const allowedZones = JSON.parse(user.allowed_zones);
                const checkboxes = document.querySelectorAll('#editUserZonesList input[type="checkbox"]');
                checkboxes.forEach(cb => {
                    cb.checked = allowedZones.includes(parseInt(cb.value));
                });
            } catch (e) {
                console.error('Error parsing allowed zones:', e);
            }
        }

        // Show edit modal
        const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
        modal.show();
    } catch (error) {
        console.error('Error loading user:', error);
        showAlert('Error loading user data', 'danger');
    }
}

// Update user
async function updateUser() {
    const id = document.getElementById('editUserId').value;
    const username = document.getElementById('editUserUsername').value;
    const fullName = document.getElementById('editUserFullName').value;
    const email = document.getElementById('editUserEmail').value;
    const role = document.getElementById('editUserRole').value;
    const zoneAccessType = document.getElementById('editUserZoneAccessType').value;
    const isActive = document.getElementById('editUserStatus').value === '1';

    if (!username || !role || !zoneAccessType) {
        showAlert('Please fill in all required fields', 'warning');
        return;
    }

    let allowedZones = null;
    if (zoneAccessType === 'specific') {
        const checkboxes = document.querySelectorAll('#editUserZonesList input[type="checkbox"]:checked');
        allowedZones = Array.from(checkboxes).map(cb => parseInt(cb.value));
        
        if (allowedZones.length === 0) {
            showAlert('Please select at least one zone for specific access', 'warning');
            return;
        }
    }

    try {
        const response = await authenticatedFetch(`/api/app-users/${id}`, {
            method: 'PUT',
            body: JSON.stringify({
                username,
                full_name: fullName,
                email,
                role,
                zone_access_type: zoneAccessType,
                allowed_zones: allowedZones,
                is_active: isActive
            })
        });

        if (!response) return;

        if (response.ok) {
            showAlert('User updated successfully', 'success');
            bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
            loadUsers();
        } else {
            const error = await response.json();
            showAlert(error.error || 'Error updating user', 'danger');
        }
    } catch (error) {
        console.error('Error updating user:', error);
        showAlert('Error updating user', 'danger');
    }
}

// Toggle user status
async function toggleUserStatus(id, newStatus) {
    const action = newStatus ? 'activate' : 'deactivate';
    
    if (confirm(`Are you sure you want to ${action} this user?`)) {
        try {
            const response = await authenticatedFetch(`/api/app-users/${id}/status`, {
                method: 'PUT',
                body: JSON.stringify({ is_active: newStatus })
            });

            if (!response) return;

            if (response.ok) {
                showAlert(`User ${action}d successfully`, 'success');
                loadUsers();
            } else {
                const error = await response.json();
                showAlert(error.error || `Error ${action}ing user`, 'danger');
            }
        } catch (error) {
            console.error(`Error ${action}ing user:`, error);
            showAlert(`Error ${action}ing user`, 'danger');
        }
    }
}

// Delete user
async function deleteUser(id) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        try {
            const response = await authenticatedFetch(`/api/app-users/${id}`, {
                method: 'DELETE'
            });

            if (!response) return;

            if (response.ok) {
                showAlert('User deleted successfully', 'success');
                loadUsers();
            } else {
                const error = await response.json();
                showAlert(error.error || 'Error deleting user', 'danger');
            }
        } catch (error) {
            console.error('Error deleting user:', error);
            showAlert('Error deleting user', 'danger');
        }
    }
}

// Change user password
async function changeUserPassword(id) {
    try {
        const response = await authenticatedFetch(`/api/app-users/${id}`);
        if (!response) return;
        const user = await response.json();

        // Populate password change form
        document.getElementById('changePasswordUserId').value = user.id;
        document.getElementById('changePasswordUsername').value = user.username;
        
        // Clear previous form data
        document.getElementById('changePasswordForm').reset();
        document.getElementById('changePasswordUserId').value = user.id;
        document.getElementById('changePasswordUsername').value = user.username;
        
        // For app users management, admin never needs current password
        // Since admin is managing app_users, current password is never required
        const currentPasswordContainer = document.getElementById('currentPasswordContainer');
        currentPasswordContainer.style.display = 'none';
        
        // Mark as app user password change
        window.isOwnPasswordChange = false;
        window.isAdminPasswordChange = false;

        // Update modal title
        document.querySelector('#changePasswordModal .modal-title').textContent = 'Change App User Password';

        // Show password change modal
        const modal = new bootstrap.Modal(document.getElementById('changePasswordModal'));
        modal.show();
    } catch (error) {
        console.error('Error loading user for password change:', error);
        showAlert('Error loading user data', 'danger');
    }
}

// Save password change
async function savePasswordChange() {
    const userId = document.getElementById('changePasswordUserId').value;
    const currentPassword = document.getElementById('changePasswordCurrent').value;
    const newPassword = document.getElementById('changePasswordNew').value;
    const confirmPassword = document.getElementById('changePasswordConfirm').value;

    // Validate inputs
    if (!newPassword || newPassword.length < 6) {
        showAlert('New password must be at least 6 characters long', 'warning');
        return;
    }

    if (newPassword !== confirmPassword) {
        showAlert('New password and confirmation do not match', 'warning');
        return;
    }

    // Check if current password is required
    const isOwnPasswordChange = window.isOwnPasswordChange;
    const isAdminPasswordChange = window.isAdminPasswordChange;
    
    // For admin password changes, current password is always required
    // For app users, current password is never required since admin is managing them
    if (isAdminPasswordChange && !currentPassword) {
        showAlert('Current password is required when changing admin password', 'warning');
        return;
    } else if (isOwnPasswordChange && !isAdminPasswordChange && !currentPassword) {
        showAlert('Current password is required when changing your own password', 'warning');
        return;
    }

    try {
        const requestBody = {
            new_password: newPassword
        };
        
        // Only include current password if it's provided (for own password changes)
        if (currentPassword) {
            requestBody.current_password = currentPassword;
        }
        
        // Add admin override flag if admin is changing another user's password
        if (!isOwnPasswordChange) {
            requestBody.admin_override = true;
        }

        // Different endpoints for admin vs app user password changes
        const endpoint = window.isAdminPasswordChange 
            ? `/api/admin-profile/password`
            : `/api/app-users/${userId}/password`;
            
        const response = await authenticatedFetch(endpoint, {
            method: 'PUT',
            body: JSON.stringify(requestBody)
        });

        if (!response) return;

        if (response.ok) {
            showAlert('Password changed successfully', 'success');
            bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
            
            // Clear the form
            document.getElementById('changePasswordForm').reset();
            
            // Clear the flags
            window.isOwnPasswordChange = false;
            window.isAdminPasswordChange = false;
        } else {
            const error = await response.json();
            showAlert(error.error || 'Error changing password', 'danger');
        }
    } catch (error) {
        console.error('Error changing password:', error);
        showAlert('Error changing password', 'danger');
    }
}

