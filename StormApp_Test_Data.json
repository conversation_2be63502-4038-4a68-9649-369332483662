{"description": "Test data for StormApp API testing", "zones": [{"id": 1, "name": "South West", "description": "South West Zone"}, {"id": 2, "name": "West", "description": "West Zone"}, {"id": 3, "name": "South", "description": "South Zone"}, {"id": 4, "name": "East", "description": "East Zone"}, {"id": 5, "name": "North", "description": "North Zone"}], "sample_locations": [{"id": 1, "name": "AKHBAR NAGAR", "zone_id": 1}, {"id": 2, "name": "AMBAWADI", "zone_id": 1}, {"id": 3, "name": "BAPUNAGAR", "zone_id": 2}, {"id": 4, "name": "CHANDKHEDA", "zone_id": 2}, {"id": 5, "name": "DANILIMDA", "zone_id": 3}], "sample_tags": [{"id": 3291, "name": "LEVEL_1", "location_id": 1, "tag_type": "level"}, {"id": 3300, "name": "CURRENT_DAY_QTY", "location_id": 1, "tag_type": "quantity"}, {"id": 3531, "name": "FLOW_RATE", "location_id": 1, "tag_type": "flow"}, {"id": 3246, "name": "PUMP_1_STATUS", "location_id": 1, "tag_type": "pump"}], "test_users": [{"username": "operator1", "password": "password123", "role": "operator", "description": "Standard operator user"}, {"username": "manager1", "password": "manager123", "role": "manager", "description": "Manager user with extended permissions"}, {"username": "admin", "password": "admin123", "role": "admin", "description": "Admin user for web panel"}], "api_endpoints": {"authentication": ["POST /auth/login", "POST /auth/logout", "POST /auth/refresh"], "zones": ["GET /zones", "GET /zones/:id"], "locations": ["GET /locations", "GET /locations/:id", "GET /locations/zone/:zoneId", "GET /locations/data", "GET /locations/:id/data"], "tags": ["GET /tags", "GET /tags/:id", "GET /tags/location/:locationId"], "data": ["GET /data/latest", "GET /data/history/:tagId", "GET /data/dashboard", "GET /data/zones"]}, "test_scenarios": {"happy_path": ["Login with valid credentials", "Get all zones", "Get locations by zone", "Get location data", "Get tag details", "Get tag history", "Logout"], "error_cases": ["Login with invalid credentials", "Access protected endpoint without token", "Use expired token", "Request non-existent resource", "Invalid parameter values"]}}