# App Configuration

This directory contains centralized configuration for the StormApp Flutter application.

## Files

### `app_config.dart`
Contains all server URLs, API endpoints, and application constants in a single location.

## Usage

### Server URL Configuration

Instead of hardcoding server URLs throughout the app, use the centralized configuration:

```dart
// ❌ Don't do this
String serverUrl = 'https://amcstorm.smartdashview.com';

// ✅ Do this instead
String serverUrl = AppConfig.getDefaultServerUrl();
```

### API Endpoints

Use the helper methods to build API URLs:

```dart
// ❌ Don't do this
String apiUrl = '$serverUrl/api/zones';

// ✅ Do this instead
String apiUrl = AppConfig.buildEndpointUrl(serverUrl, AppConfig.apiZonesPath);
```

### WebSocket Configuration

Use centralized WebSocket settings:

```dart
// ❌ Don't do this
socket = IO.io(websocketUrl, 
  IO.OptionBuilder()
    .setTimeout(10000)
    .build()
);

// ✅ Do this instead
socket = IO.io(websocketUrl, 
  IO.OptionBuilder()
    .setTimeout(AppConfig.websocketTimeout)
    .build()
);
```

### Predefined Servers

Access predefined server configurations:

```dart
List<ServerConfig> servers = AppConfig.predefinedServers;
```

## Configuration Values

### Default URLs
- **Development**: `https://amcstorm.smartdashview.com`
- **Local Network**: `https://amcstorm.smartdashview.com`
- **Production**: Update `AppConfig.prodServerUrl` for your environment

### Timeouts
- **HTTP Timeout**: 10 seconds
- **WebSocket Timeout**: 10 seconds
- **Connection Timeout**: 15 seconds

### App Settings
- **Default Refresh Interval**: 5 seconds
- **Default Auto Refresh**: Enabled

## Customization

To customize the configuration for your environment:

1. Update the URLs in `AppConfig` class
2. Modify timeout values as needed
3. Add new predefined server configurations
4. Update app information constants

## Environment-Specific Configuration

You can add environment detection logic in `AppConfig.getDefaultServerUrl()`:

```dart
static String getDefaultServerUrl() {
  // Add your environment detection logic here
  if (kDebugMode) {
    return devServerUrl;
  } else {
    return prodServerUrl;
  }
}
```
