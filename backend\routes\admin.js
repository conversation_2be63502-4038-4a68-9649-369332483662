import express from 'express';
import { executeQuery } from '../config/database.js';
import bcrypt from 'bcryptjs';

const router = express.Router();

// Test route to verify admin routes are working
router.get('/test', (req, res) => {
  res.json({ message: 'Admin routes are working!', user: req.user });
});

// Get all users with zone access information
router.get('/users', async (req, res) => {
  try {
    const query = `
      SELECT 
        id,
        username,
        email,
        full_name,
        role,
        zone_access_type,
        allowed_zones,
        is_active,
        last_login,
        created_at,
        updated_at
      FROM app_users
      ORDER BY id
    `;
    
    const users = await executeQuery(query);
    res.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Get user by ID
router.get('/users/:id', async (req, res) => {
  try {
    const userId = req.params.id;
    const query = `
      SELECT 
        id,
        username,
        email,
        full_name,
        role,
        zone_access_type,
        allowed_zones,
        is_active,
        last_login,
        created_at,
        updated_at
      FROM app_users
      WHERE id = ?
    `;
    
    const users = await executeQuery(query, [userId]);
    
    if (users.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.json(users[0]);
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({ error: 'Failed to fetch user' });
  }
});

// Get all zones with location counts
router.get('/zones', async (req, res) => {
  try {
    const query = `
      SELECT 
        z.id,
        z.name,
        z.description,
        z.zone_type,
        z.created_at,
        z.updated_at,
        COUNT(l.id) as location_count
      FROM zones z
      LEFT JOIN locations l ON z.id = l.zone_id
      GROUP BY z.id
      ORDER BY z.id
    `;
    
    const zones = await executeQuery(query);
    res.json(zones);
  } catch (error) {
    console.error('Error fetching zones:', error);
    res.status(500).json({ error: 'Failed to fetch zones' });
  }
});

// Create new user
router.post('/users', async (req, res) => {
  try {
    const {
      username,
      password,
      email,
      full_name,
      role,
      zone_access_type,
      allowed_zones
    } = req.body;
    
    // Validate required fields
    if (!username || !password || !role) {
      return res.status(400).json({ error: 'Username, password, and role are required' });
    }
    
    // Validate zone_access_type
    if (!['all', 'specific', 'none'].includes(zone_access_type)) {
      return res.status(400).json({ error: 'Invalid zone access type' });
    }
    
    // Hash password
    const saltRounds = 10;
    const password_hash = await bcrypt.hash(password, saltRounds);
    
    // Prepare allowed_zones
    let zonesJson = null;
    if (zone_access_type === 'specific' && Array.isArray(allowed_zones)) {
      zonesJson = JSON.stringify(allowed_zones);
    }
    
    const query = `
      INSERT INTO app_users (
        username, password_hash, email, full_name, role,
        zone_access_type, allowed_zones, is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())
    `;
    
    const result = await executeQuery(query, [
      username, password_hash, email, full_name, role,
      zone_access_type, zonesJson
    ]);
    
    res.json({ 
      success: true, 
      message: 'User created successfully',
      userId: result.insertId
    });
  } catch (error) {
    console.error('Error creating user:', error);
    if (error.code === 'ER_DUP_ENTRY') {
      res.status(400).json({ error: 'Username already exists' });
    } else {
      res.status(500).json({ error: 'Failed to create user' });
    }
  }
});

// Update user
router.put('/users/:id', async (req, res) => {
  try {
    const userId = req.params.id;
    const {
      username,
      email,
      full_name,
      role,
      zone_access_type,
      allowed_zones,
      is_active
    } = req.body;
    
    // Validate zone_access_type
    if (!['all', 'specific', 'none'].includes(zone_access_type)) {
      return res.status(400).json({ error: 'Invalid zone access type' });
    }
    
    // Validate allowed_zones if access type is specific
    let zonesJson = null;
    if (zone_access_type === 'specific') {
      if (!Array.isArray(allowed_zones)) {
        return res.status(400).json({ error: 'allowed_zones must be an array for specific access' });
      }
      zonesJson = JSON.stringify(allowed_zones);
    }
    
    const query = `
      UPDATE app_users 
      SET username = ?, email = ?, full_name = ?, role = ?,
          zone_access_type = ?, allowed_zones = ?, is_active = ?, updated_at = NOW()
      WHERE id = ?
    `;
    
    await executeQuery(query, [
      username, email, full_name, role,
      zone_access_type, zonesJson, is_active ? 1 : 0, userId
    ]);
    
    res.json({ success: true, message: 'User updated successfully' });
  } catch (error) {
    console.error('Error updating user:', error);
    if (error.code === 'ER_DUP_ENTRY') {
      res.status(400).json({ error: 'Username already exists' });
    } else {
      res.status(500).json({ error: 'Failed to update user' });
    }
  }
});

// Update user zone access
router.put('/users/:id/zones', async (req, res) => {
  try {
    const userId = req.params.id;
    const { zone_access_type, allowed_zones } = req.body;
    
    // Validate zone_access_type
    if (!['all', 'specific', 'none'].includes(zone_access_type)) {
      return res.status(400).json({ error: 'Invalid zone access type' });
    }
    
    // Validate allowed_zones if access type is specific
    let zonesJson = null;
    if (zone_access_type === 'specific') {
      if (!Array.isArray(allowed_zones)) {
        return res.status(400).json({ error: 'allowed_zones must be an array for specific access' });
      }
      zonesJson = JSON.stringify(allowed_zones);
    }
    
    const query = `
      UPDATE app_users 
      SET zone_access_type = ?, allowed_zones = ?, updated_at = NOW()
      WHERE id = ?
    `;
    
    await executeQuery(query, [zone_access_type, zonesJson, userId]);
    
    res.json({ success: true, message: 'User zone access updated successfully' });
  } catch (error) {
    console.error('Error updating user zone access:', error);
    res.status(500).json({ error: 'Failed to update user zone access' });
  }
});

// Toggle user active status
router.put('/users/:id/status', async (req, res) => {
  try {
    const userId = req.params.id;
    const { is_active } = req.body;
    
    const query = `
      UPDATE app_users 
      SET is_active = ?, updated_at = NOW()
      WHERE id = ?
    `;
    
    await executeQuery(query, [is_active ? 1 : 0, userId]);
    
    res.json({ success: true, message: 'User status updated successfully' });
  } catch (error) {
    console.error('Error updating user status:', error);
    res.status(500).json({ error: 'Failed to update user status' });
  }
});

// Change user password
router.put('/users/:id/password', async (req, res) => {
  try {
    const userId = req.params.id;
    const { new_password, current_password, admin_override } = req.body;
    
    if (!new_password || new_password.length < 6) {
      return res.status(400).json({ error: 'New password must be at least 6 characters long' });
    }
    
    // Get the current user making the request (from JWT token)
    const requestingUserId = req.user.id; // This comes from the JWT middleware
    const requestingUserRole = req.user.role;
    
    // If changing another user's password and requester is admin, allow without current password
    const isAdminChangingOtherUser = requestingUserId != userId && 
                                   (requestingUserRole === 'manager' || admin_override);
    
    // If current_password is provided or required, verify it
    if (current_password && !isAdminChangingOtherUser) {
      const userQuery = 'SELECT password_hash FROM app_users WHERE id = ?';
      const users = await executeQuery(userQuery, [userId]);
      
      if (users.length === 0) {
        return res.status(404).json({ error: 'User not found' });
      }
      
      const isCurrentPasswordValid = await bcrypt.compare(current_password, users[0].password_hash);
      if (!isCurrentPasswordValid) {
        return res.status(400).json({ error: 'Current password is incorrect' });
      }
    } else if (!isAdminChangingOtherUser && !current_password) {
      // Require current password for self password changes
      return res.status(400).json({ error: 'Current password is required' });
    }
    
    // Hash the new password
    const saltRounds = 10;
    const password_hash = await bcrypt.hash(new_password, saltRounds);
    
    const query = `
      UPDATE app_users 
      SET password_hash = ?, updated_at = NOW()
      WHERE id = ?
    `;
    
    await executeQuery(query, [password_hash, userId]);
    
    res.json({ success: true, message: 'Password updated successfully' });
  } catch (error) {
    console.error('Error updating password:', error);
    res.status(500).json({ error: 'Failed to update password' });
  }
});

// Delete user
router.delete('/users/:id', async (req, res) => {
  try {
    const userId = req.params.id;
    
    // Don't allow deleting user ID 1 (main admin)
    if (userId == 1) {
      return res.status(400).json({ error: 'Cannot delete the main administrator account' });
    }
    
    const query = `DELETE FROM app_users WHERE id = ?`;
    const result = await executeQuery(query, [userId]);
    
    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.json({ success: true, message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ error: 'Failed to delete user' });
  }
});

// Get zone access statistics
router.get('/stats', async (req, res) => {
  try {
    const stats = {};
    
    // User statistics
    const userStatsQuery = `
      SELECT 
        zone_access_type,
        COUNT(*) as count
      FROM app_users
      WHERE is_active = 1
      GROUP BY zone_access_type
    `;
    const userStats = await executeQuery(userStatsQuery);
    stats.users = userStats.reduce((acc, row) => {
      acc[row.zone_access_type] = row.count;
      return acc;
    }, {});
    
    // Zone statistics
    const zoneStatsQuery = `
      SELECT 
        COUNT(*) as total_zones,
        SUM(CASE WHEN zone_type = 'regular' THEN 1 ELSE 0 END) as regular_zones,
        SUM(CASE WHEN zone_type = 'critical' THEN 1 ELSE 0 END) as critical_zones,
        SUM(CASE WHEN zone_type = 'tsps' THEN 1 ELSE 0 END) as tsps_zones
      FROM zones
    `;
    const zoneStats = await executeQuery(zoneStatsQuery);
    stats.zones = zoneStats[0];
    
    // Location statistics
    const locationStatsQuery = `
      SELECT 
        COUNT(*) as total_locations,
        SUM(CASE WHEN location_type = 'SWPS' THEN 1 ELSE 0 END) as swps_locations,
        SUM(CASE WHEN location_type = 'CSPS' THEN 1 ELSE 0 END) as csps_locations,
        SUM(CASE WHEN location_type = 'TSPS' THEN 1 ELSE 0 END) as tsps_locations
      FROM locations
    `;
    const locationStats = await executeQuery(locationStatsQuery);
    stats.locations = locationStats[0];
    
    res.json(stats);
  } catch (error) {
    console.error('Error fetching statistics:', error);
    res.status(500).json({ error: 'Failed to fetch statistics' });
  }
});

export default router;