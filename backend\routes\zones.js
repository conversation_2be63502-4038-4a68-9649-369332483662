import express from 'express';
import { executeQuery } from '../config/database.js';
import jwt from 'jsonwebtoken';

const router = express.Router();

// Zone Access Control Middleware
const checkZoneAccess = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    const userId = decoded.userId || decoded.id; // Support both userId and id formats
    
    //console.log('JWT decoded:', { userId, tokenPayload: decoded });

    const userQuery = 'SELECT zone_access_type, allowed_zones, role FROM app_users WHERE id = ? AND is_active = 1';
    const userResult = await executeQuery(userQuery, [userId]);
    
    //console.log('User query result:', userResult);
    
    if (userResult.length === 0) {
      return res.status(403).json({ error: 'User not found or inactive' });
    }

    const user = userResult[0];
    
    if (user.zone_access_type === 'all') {
      req.userZoneAccess = {
        type: 'all',
        allowedZones: null,
        role: user.role
      };
      return next();
    }

    if (user.zone_access_type === 'none') {
      return res.status(403).json({ error: 'User has no zone access' });
    }

    let allowedZones = [];
    try {
      allowedZones = user.allowed_zones ? JSON.parse(user.allowed_zones) : [];
    } catch (e) {
      console.error('Error parsing allowed_zones JSON:', e);
      return res.status(500).json({ error: 'Invalid zone configuration' });
    }

    req.userZoneAccess = {
      type: 'specific',
      allowedZones: allowedZones,
      role: user.role
    };

    next();
  } catch (error) {
    console.error('Zone access check error:', error);
    return res.status(500).json({ error: 'Zone access check failed' });
  }
};

// Helper functions
const canAccessZone = (zoneId, userZoneAccess) => {
  const numericZoneId = parseInt(zoneId);
  
  if (isNaN(numericZoneId) && ['CSPS', 'TSPS', 'SWPS'].includes(zoneId)) {
    return userZoneAccess.type === 'all' || 
           (userZoneAccess.type === 'specific' && userZoneAccess.allowedZones.length > 0);
  }

  if (userZoneAccess.type === 'all') {
    return true;
  }

  if (userZoneAccess.type === 'specific' && userZoneAccess.allowedZones) {
    return userZoneAccess.allowedZones.includes(numericZoneId);
  }

  return false;
};

const getZoneWhereClause = (userZoneAccess, tableAlias = 'z') => {
  if (userZoneAccess.type === 'all') {
    return { clause: '', params: [] };
  }

  if (userZoneAccess.type === 'specific' && userZoneAccess.allowedZones && userZoneAccess.allowedZones.length > 0) {
    const placeholders = userZoneAccess.allowedZones.map(() => '?').join(',');
    return {
      clause: `AND ${tableAlias}.id IN (${placeholders})`,
      params: userZoneAccess.allowedZones
    };
  }

  return { clause: 'AND 1=0', params: [] };
};

// Get all zones for sidebar (including location type categories)
router.get('/', checkZoneAccess, async (req, res) => {
  try {
    // Get zone filter for current user
    const zoneFilter = getZoneWhereClause(req.userZoneAccess, 'z');
    
    // Get regular zones
    const regularZonesQuery = `
      SELECT z.*,
             COUNT(l.id) as location_count
      FROM zones z
      LEFT JOIN locations l ON z.id = l.zone_id
      WHERE z.zone_type = 'regular' ${zoneFilter.clause}
      GROUP BY z.id
      ORDER BY z.name
    `;

    // Get count of TSPS locations (filtered by user's zone access)
    const tspsCountQuery = `
      SELECT COUNT(*) as location_count
      FROM locations l
      JOIN zones z ON l.zone_id = z.id
      WHERE l.location_type = 'TSPS' ${zoneFilter.clause}
    `;

    // Get count of CSPS locations (filtered by user's zone access)
    const cspsCountQuery = `
      SELECT COUNT(*) as location_count
      FROM locations l
      JOIN zones z ON l.zone_id = z.id
      WHERE l.location_type = 'CSPS' ${zoneFilter.clause}
    `;

    const [regularZones, tspsCount, cspsCount] = await Promise.all([
      executeQuery(regularZonesQuery, zoneFilter.params),
      executeQuery(tspsCountQuery, zoneFilter.params),
      executeQuery(cspsCountQuery, zoneFilter.params)
    ]);

    // Create special zone objects for location types
    const specialZones = [];

    // Add Critical Pumping Station if there are CSPS locations
    if (cspsCount[0].location_count > 0) {
      specialZones.push({
        id: 'CSPS',
        name: 'Critical Pumping Station',
        description: 'Critical Pumping Station locations',
        zone_type: 'critical',
        location_count: cspsCount[0].location_count,
        created_at: null,
        updated_at: null
      });
    }

    // Add Terminal SPS if there are TSPS locations
    if (tspsCount[0].location_count > 0) {
      specialZones.push({
        id: 'TSPS',
        name: 'Terminal SPS',
        description: 'Terminal SPS locations',
        zone_type: 'tsps',
        location_count: tspsCount[0].location_count,
        created_at: null,
        updated_at: null
      });
    }

    // Combine special zones first, then regular zones
    const allZones = [...specialZones, ...regularZones];

    res.json(allZones);
  } catch (error) {
    console.error('Error fetching zones:', error);
    res.status(500).json({ error: 'Failed to fetch zones' });
  }
});

// Get zone aggregated data
router.get('/data', checkZoneAccess, async (req, res) => {
  try {
    // Get zone filter for current user
    const zoneFilter = getZoneWhereClause(req.userZoneAccess, 'z');
    
    // Get data for regular zones
    const regularZonesQuery = `
      SELECT
        z.id as zone_id,
        z.name as zone_name,
        z.zone_type,
        COALESCE(SUM(CASE
          WHEN t.tag_name LIKE '%CURRENT_DAY_1%'
          THEN td.value/1000
          ELSE 0
        END), 0) as total_current_day_qty,
        COALESCE(SUM(CASE
          WHEN t.tag_name LIKE '%PREVIOUS_DAY_TOTALIZER%'
          THEN td.value/1000
          ELSE 0
        END), 0) as total_previous_day_qty,
        COALESCE(SUM(CASE
          WHEN t.tag_name LIKE '%SEWAGE_TOTALIZER%'
          THEN td.value/1000
          ELSE 0
        END), 0) as total_till_today_qty,
        -- Current day quantities by location type
        COALESCE(SUM(CASE
          WHEN t.tag_name LIKE '%CURRENT_DAY_1%' AND l.location_type = 'CSPS'
          THEN td.value/1000
          ELSE 0
        END), 0) as csps_current_day_qty,
        COALESCE(SUM(CASE
          WHEN t.tag_name LIKE '%CURRENT_DAY_1%' AND l.location_type = 'SWPS'
          THEN td.value/1000
          ELSE 0
        END), 0) as swps_current_day_qty,
        COALESCE(SUM(CASE
          WHEN t.tag_name LIKE '%CURRENT_DAY_1%' AND l.location_type = 'TSPS'
          THEN td.value/1000
          ELSE 0
        END), 0) as tsps_current_day_qty,
        COALESCE(MAX(td.timestamp), UNIX_TIMESTAMP()) as last_updated,
        COUNT(DISTINCT l.id) as location_count
      FROM zones z
      LEFT JOIN locations l ON z.id = l.zone_id
      LEFT JOIN tags t ON l.id = t.location_id AND t.is_active = TRUE
      LEFT JOIN (
        SELECT tag_id, value, timestamp,
               ROW_NUMBER() OVER (PARTITION BY tag_id ORDER BY timestamp DESC) as rn
        FROM tag_data
      ) td ON t.id = td.tag_id AND td.rn = 1
      WHERE z.zone_type = 'regular' ${zoneFilter.clause}
      GROUP BY z.id, z.name, z.zone_type
      ORDER BY z.name
    `;

    // Get data for TSPS locations (filtered by user's zone access)
    const tspsQuery = `
      SELECT
        'TSPS' as zone_id,
        'Terminal SPS' as zone_name,
        'tsps' as zone_type,
        COALESCE(SUM(CASE
          WHEN t.tag_name LIKE '%CURRENT_DAY_1%'
          THEN td.value/1000
          ELSE 0
        END), 0) as total_current_day_qty,
        COALESCE(SUM(CASE
          WHEN t.tag_name LIKE '%PREVIOUS_DAY_TOTALIZER%'
          THEN td.value/1000
          ELSE 0
        END), 0) as total_previous_day_qty,
        COALESCE(SUM(CASE
          WHEN t.tag_name LIKE '%SEWAGE_TOTALIZER%'
          THEN td.value/1000
          ELSE 0
        END), 0) as total_till_today_qty,
        COALESCE(MAX(td.timestamp), UNIX_TIMESTAMP()) as last_updated,
        COUNT(DISTINCT l.id) as location_count
      FROM locations l
      JOIN zones z ON l.zone_id = z.id
      LEFT JOIN tags t ON l.id = t.location_id AND t.is_active = TRUE
      LEFT JOIN (
        SELECT tag_id, value, timestamp,
               ROW_NUMBER() OVER (PARTITION BY tag_id ORDER BY timestamp DESC) as rn
        FROM tag_data
      ) td ON t.id = td.tag_id AND td.rn = 1
      WHERE l.location_type = 'TSPS' ${zoneFilter.clause}
    `;

    // Get data for CSPS locations (filtered by user's zone access)
    const cspsQuery = `
      SELECT
        'CSPS' as zone_id,
        'Critical Pumping Station' as zone_name,
        'critical' as zone_type,
        COALESCE(SUM(CASE
          WHEN t.tag_name LIKE '%CURRENT_DAY_1%'
          THEN td.value/1000
          ELSE 0
        END), 0) as total_current_day_qty,
        COALESCE(SUM(CASE
          WHEN t.tag_name LIKE '%PREVIOUS_DAY_TOTALIZER%'
          THEN td.value/1000
          ELSE 0
        END), 0) as total_previous_day_qty,
        COALESCE(SUM(CASE
          WHEN t.tag_name LIKE '%SEWAGE_TOTALIZER%'
          THEN td.value/1000
          ELSE 0
        END), 0) as total_till_today_qty,
        COALESCE(MAX(td.timestamp), UNIX_TIMESTAMP()) as last_updated,
        COUNT(DISTINCT l.id) as location_count
      FROM locations l
      JOIN zones z ON l.zone_id = z.id
      LEFT JOIN tags t ON l.id = t.location_id AND t.is_active = TRUE
      LEFT JOIN (
        SELECT tag_id, value, timestamp,
               ROW_NUMBER() OVER (PARTITION BY tag_id ORDER BY timestamp DESC) as rn
        FROM tag_data
      ) td ON t.id = td.tag_id AND td.rn = 1
      WHERE l.location_type = 'CSPS' ${zoneFilter.clause}
    `;

    // Get data for SWPS locations (filtered by user's zone access)
    const swpsQuery = `
      SELECT
        'SWPS' as zone_id,
        'Storm Water Pumping Station' as zone_name,
        'swps' as zone_type,
        COALESCE(SUM(CASE
          WHEN t.tag_name LIKE '%CURRENT_DAY_1%'
          THEN td.value/1000
          ELSE 0
        END), 0) as total_current_day_qty,
        COALESCE(SUM(CASE
          WHEN t.tag_name LIKE '%PREVIOUS_DAY_TOTALIZER%'
          THEN td.value/1000
          ELSE 0
        END), 0) as total_previous_day_qty,
        COALESCE(SUM(CASE
          WHEN t.tag_name LIKE '%SEWAGE_TOTALIZER%'
          THEN td.value/1000
          ELSE 0
        END), 0) as total_till_today_qty,
        COALESCE(MAX(td.timestamp), UNIX_TIMESTAMP()) as last_updated,
        COUNT(DISTINCT l.id) as location_count
      FROM locations l
      JOIN zones z ON l.zone_id = z.id
      LEFT JOIN tags t ON l.id = t.location_id AND t.is_active = TRUE
      LEFT JOIN (
        SELECT tag_id, value, timestamp,
               ROW_NUMBER() OVER (PARTITION BY tag_id ORDER BY timestamp DESC) as rn
        FROM tag_data
      ) td ON t.id = td.tag_id AND td.rn = 1
      WHERE l.location_type = 'SWPS' ${zoneFilter.clause}
    `;

    const [regularZones, tspsData, cspsData, swpsData] = await Promise.all([
      executeQuery(regularZonesQuery, zoneFilter.params),
      executeQuery(tspsQuery, zoneFilter.params),
      executeQuery(cspsQuery, zoneFilter.params),
      executeQuery(swpsQuery, zoneFilter.params)
    ]);

    // Combine all data
    const allZoneData = [...regularZones, ...tspsData, ...cspsData, ...swpsData];

    // Calculate totals for each category
    const totals = {
      regular: {
        total_current_day_qty: 0,
        total_previous_day_qty: 0,
        total_till_today_qty: 0,
        location_count: 0
      },
      tsps: {
        total_current_day_qty: 0,
        total_previous_day_qty: 0,
        total_till_today_qty: 0,
        location_count: 0
      },
      critical: {
        total_current_day_qty: 0,
        total_previous_day_qty: 0,
        total_till_today_qty: 0,
        location_count: 0
      },
      swps: {
        total_current_day_qty: 0,
        total_previous_day_qty: 0,
        total_till_today_qty: 0,
        location_count: 0
      }
    };

    // Calculate totals
    allZoneData.forEach(zone => {
      if (zone.zone_type === 'regular') {
        totals.regular.total_current_day_qty += zone.total_current_day_qty;
        totals.regular.total_previous_day_qty += zone.total_previous_day_qty;
        totals.regular.total_till_today_qty += zone.total_till_today_qty;
        totals.regular.location_count += zone.location_count;
      } else if (zone.zone_type === 'tsps') {
        totals.tsps.total_current_day_qty += zone.total_current_day_qty;
        totals.tsps.total_previous_day_qty += zone.total_previous_day_qty;
        totals.tsps.total_till_today_qty += zone.total_till_today_qty;
        totals.tsps.location_count += zone.location_count;
      } else if (zone.zone_type === 'critical') {
        totals.critical.total_current_day_qty += zone.total_current_day_qty;
        totals.critical.total_previous_day_qty += zone.total_previous_day_qty;
        totals.critical.total_till_today_qty += zone.total_till_today_qty;
        totals.critical.location_count += zone.location_count;
      } else if (zone.zone_type === 'swps') {
        totals.swps.total_current_day_qty += zone.total_current_day_qty;
        totals.swps.total_previous_day_qty += zone.total_previous_day_qty;
        totals.swps.total_till_today_qty += zone.total_till_today_qty;
        totals.swps.location_count += zone.location_count;
      }
    });

    res.json({
      zones: allZoneData,
      totals
    });
  } catch (error) {
    console.error('Error fetching zone data:', error);
    res.status(500).json({ error: 'Failed to fetch zone data' });
  }
});

// Get zone by ID with locations or special location types
router.get('/:id', checkZoneAccess, async (req, res) => {
  try {
    const zoneId = req.params.id;

    // Check if user can access this zone
    if (!canAccessZone(zoneId, req.userZoneAccess)) {
      return res.status(403).json({ error: 'Access denied to this zone' });
    }

    // Handle special cases for location types
    if (zoneId === 'TSPS') {
      // Get zone filter for current user
      const zoneFilter = getZoneWhereClause(req.userZoneAccess, 'z');
      
      // Get locations with TSPS type (filtered by user's zone access)
      const locationsQuery = `
        SELECT l.*, z.name as zone_name,
               COUNT(t.id) as tag_count
        FROM locations l
        JOIN zones z ON l.zone_id = z.id
        LEFT JOIN tags t ON l.id = t.location_id AND t.is_active = TRUE
        WHERE l.location_type = 'TSPS' ${zoneFilter.clause}
        GROUP BY l.id
        ORDER BY l.name
      `;
      const locations = await executeQuery(locationsQuery, zoneFilter.params);

      res.json({
        id: 'TSPS',
        name: 'Terminal SPS',
        zone_type: 'tsps',
        description: 'Terminal SPS locations',
        locations: locations
      });
      return;
    }

    if (zoneId === 'CSPS') {
      // Get zone filter for current user
      const zoneFilter = getZoneWhereClause(req.userZoneAccess, 'z');
      
      // Get locations with CSPS type (filtered by user's zone access)
      const locationsQuery = `
        SELECT l.*, z.name as zone_name,
               COUNT(t.id) as tag_count
        FROM locations l
        JOIN zones z ON l.zone_id = z.id
        LEFT JOIN tags t ON l.id = t.location_id AND t.is_active = TRUE
        WHERE l.location_type = 'CSPS' ${zoneFilter.clause}
        GROUP BY l.id
        ORDER BY l.name
      `;
      const locations = await executeQuery(locationsQuery, zoneFilter.params);

      res.json({
        id: 'CSPS',
        name: 'Critical Pumping Station',
        zone_type: 'critical',
        description: 'Critical Pumping Station locations',
        locations: locations
      });
      return;
    }

    if (zoneId === 'SWPS') {
      // Get zone filter for current user
      const zoneFilter = getZoneWhereClause(req.userZoneAccess, 'z');
      
      // Get locations with SWPS type (filtered by user's zone access)
      const locationsQuery = `
        SELECT l.*, z.name as zone_name,
               COUNT(t.id) as tag_count
        FROM locations l
        JOIN zones z ON l.zone_id = z.id
        LEFT JOIN tags t ON l.id = t.location_id AND t.is_active = TRUE
        WHERE l.location_type = 'SWPS' ${zoneFilter.clause}
        GROUP BY l.id
        ORDER BY z.name, l.name
      `;
      const locations = await executeQuery(locationsQuery, zoneFilter.params);

      res.json({
        id: 'SWPS',
        name: 'Storm Water Pumping Station',
        zone_type: 'regular',
        description: 'Storm Water Pumping Station locations',
        locations: locations
      });
      return;
    }

    // Regular zone handling
    const zoneQuery = 'SELECT * FROM zones WHERE id = ?';
    const zones = await executeQuery(zoneQuery, [zoneId]);

    if (zones.length === 0) {
      return res.status(404).json({ error: 'Zone not found' });
    }

    // Get locations in this zone
    const locationsQuery = `
      SELECT l.*,
             COUNT(t.id) as tag_count
      FROM locations l
      LEFT JOIN tags t ON l.id = t.location_id AND t.is_active = TRUE
      WHERE l.zone_id = ?
      GROUP BY l.id
      ORDER BY l.name
    `;
    const locations = await executeQuery(locationsQuery, [zoneId]);

    const zone = zones[0];
    zone.locations = locations;

    res.json(zone);
  } catch (error) {
    console.error('Error fetching zone:', error);
    res.status(500).json({ error: 'Failed to fetch zone' });
  }
});

// Create new zone
router.post('/', async (req, res) => {
  try {
    const { name, description, zone_type } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Zone name is required' });
    }

    const query = 'INSERT INTO zones (name, description, zone_type) VALUES (?, ?, ?)';
    const result = await executeQuery(query, [name, description || null, zone_type || 'regular']);

    res.status(201).json({
      id: result.insertId,
      name,
      description,
      zone_type: zone_type || 'regular',
      message: 'Zone created successfully'
    });
  } catch (error) {
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(400).json({ error: 'Zone name already exists' });
    }
    console.error('Error creating zone:', error);
    res.status(500).json({ error: 'Failed to create zone' });
  }
});

// Update zone
router.put('/:id', async (req, res) => {
  try {
    const zoneId = req.params.id;
    const { name, description, zone_type } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Zone name is required' });
    }

    const query = 'UPDATE zones SET name = ?, description = ?, zone_type = ? WHERE id = ?';
    const result = await executeQuery(query, [name, description || null, zone_type || 'regular', zoneId]);

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Zone not found' });
    }

    res.json({ message: 'Zone updated successfully' });
  } catch (error) {
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(400).json({ error: 'Zone name already exists' });
    }
    console.error('Error updating zone:', error);
    res.status(500).json({ error: 'Failed to update zone' });
  }
});

// Delete zone
router.delete('/:id', async (req, res) => {
  try {
    const zoneId = req.params.id;

    // Check if zone has locations
    const locationCheck = await executeQuery(
      'SELECT COUNT(*) as count FROM locations WHERE zone_id = ?',
      [zoneId]
    );

    if (locationCheck[0].count > 0) {
      return res.status(400).json({
        error: 'Cannot delete zone with existing locations. Please delete locations first.'
      });
    }

    const query = 'DELETE FROM zones WHERE id = ?';
    const result = await executeQuery(query, [zoneId]);

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Zone not found' });
    }

    res.json({ message: 'Zone deleted successfully' });
  } catch (error) {
    console.error('Error deleting zone:', error);
    res.status(500).json({ error: 'Failed to delete zone' });
  }
});

export default router;
