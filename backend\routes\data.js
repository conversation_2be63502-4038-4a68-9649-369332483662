import express from 'express';
import { executeQuery } from '../config/database.js';

const router = express.Router();

// Get latest data for all active tags
router.get('/latest', async (req, res) => {
  try {
    const query = `
      SELECT t.id, t.tag_name, t.tag_type, t.unit,
             l.name as location_name,l.location_type as location_type, z.name as zone_name,
             td.value, td.timestamp, td.received_at
      FROM tags t
      JOIN locations l ON t.location_id = l.id
      JOIN zones z ON l.zone_id = z.id
      LEFT JOIN (
        SELECT tag_id, value, timestamp, received_at,
               ROW_NUMBER() OVER (PARTITION BY tag_id ORDER BY timestamp DESC) as rn
        FROM tag_data
      ) td ON t.id = td.tag_id AND td.rn = 1
      WHERE t.is_active = TRUE
      ORDER BY z.name, l.name, t.tag_type, t.tag_name
    `;
    const data = await executeQuery(query);
    res.json(data);
  } catch (error) {
    console.error('Error fetching latest data:', error);
    res.status(500).json({ error: 'Failed to fetch latest data' });
  }
});

// Get latest data for specific location
router.get('/latest/location/:locationId', async (req, res) => {
  try {
    const locationId = req.params.locationId;
    const query = `
      SELECT t.id, t.tag_name, t.tag_type, t.unit, t.min_value, t.max_value,
             td.value, td.timestamp, td.received_at
      FROM tags t
      LEFT JOIN (
        SELECT tag_id, value, timestamp, received_at,
               ROW_NUMBER() OVER (PARTITION BY tag_id ORDER BY timestamp DESC) as rn
        FROM tag_data
      ) td ON t.id = td.tag_id AND td.rn = 1
      WHERE t.location_id = ? AND t.is_active = TRUE
      ORDER BY t.tag_type, t.tag_name
    `;
    const data = await executeQuery(query, [locationId]);
    res.json(data);
  } catch (error) {
    console.error('Error fetching location data:', error);
    res.status(500).json({ error: 'Failed to fetch location data' });
  }
});

// Get historical data for a specific tag
router.get('/history/:tagId', async (req, res) => {
  try {
    const tagId = req.params.tagId;
    const { hours = 24, limit = 100 } = req.query;

    const hoursAgo = Math.floor(Date.now() / 1000) - (hours * 60 * 60);

    const query = `
      SELECT value, timestamp, received_at
      FROM tag_data
      WHERE tag_id = ? AND timestamp > ?
      ORDER BY timestamp DESC
      LIMIT ?
    `;
    const data = await executeQuery(query, [tagId, hoursAgo, parseInt(limit)]);
    res.json(data);
  } catch (error) {
    console.error('Error fetching historical data:', error);
    res.status(500).json({ error: 'Failed to fetch historical data' });
  }
});

// Get aggregated data for dashboard
router.get('/dashboard', async (req, res) => {
  try {
    // Get zone summary
    const zoneQuery = `
      SELECT z.id, z.name,
             COUNT(DISTINCT l.id) as location_count,
             COUNT(DISTINCT t.id) as tag_count,
             COUNT(DISTINCT CASE WHEN td.value IS NOT NULL THEN t.id END) as active_tags
      FROM zones z
      LEFT JOIN locations l ON z.id = l.zone_id
      LEFT JOIN tags t ON l.id = t.location_id AND t.is_active = TRUE
      LEFT JOIN (
        SELECT tag_id, value,
               ROW_NUMBER() OVER (PARTITION BY tag_id ORDER BY timestamp DESC) as rn
        FROM tag_data
        WHERE timestamp > ?
      ) td ON t.id = td.tag_id AND td.rn = 1
      GROUP BY z.id, z.name
      ORDER BY z.name
    `;

    const oneHourAgo = Math.floor(Date.now() / 1000) - (60 * 60);
    const zones = await executeQuery(zoneQuery, [oneHourAgo]);

    // Get recent alerts (values outside min/max range)
    const alertQuery = `
      SELECT t.id, t.tag_name, t.min_value, t.max_value,
             l.name as location_name, z.name as zone_name,
             td.value, td.timestamp
      FROM tags t
      JOIN locations l ON t.location_id = l.id
      JOIN zones z ON l.zone_id = z.id
      JOIN (
        SELECT tag_id, value, timestamp,
               ROW_NUMBER() OVER (PARTITION BY tag_id ORDER BY timestamp DESC) as rn
        FROM tag_data
        WHERE timestamp > ?
      ) td ON t.id = td.tag_id AND td.rn = 1
      WHERE t.is_active = TRUE
        AND ((t.min_value IS NOT NULL AND td.value < t.min_value)
             OR (t.max_value IS NOT NULL AND td.value > t.max_value))
      ORDER BY td.timestamp DESC
      LIMIT 20
    `;
    const alerts = await executeQuery(alertQuery, [oneHourAgo]);

    // Get system statistics
    const statsQuery = `
      SELECT
        (SELECT COUNT(*) FROM zones) as total_zones,
        (SELECT COUNT(*) FROM locations) as total_locations,
        (SELECT COUNT(*) FROM tags WHERE is_active = TRUE) as total_tags,
        (SELECT COUNT(DISTINCT tag_id) FROM tag_data WHERE timestamp > ?) as active_tags_last_hour
    `;
    const stats = await executeQuery(statsQuery, [oneHourAgo]);

    res.json({
      zones,
      alerts,
      statistics: stats[0]
    });
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard data' });
  }
});

// Get data for specific time range
router.get('/range/:tagId', async (req, res) => {
  try {
    const tagId = req.params.tagId;
    const { start, end, interval = 'hour' } = req.query;

    if (!start || !end) {
      return res.status(400).json({ error: 'Start and end timestamps are required' });
    }

    let groupBy = '';
    switch (interval) {
      case 'minute':
        groupBy = 'FROM_UNIXTIME(timestamp - (timestamp % 60))';
        break;
      case 'hour':
        groupBy = 'FROM_UNIXTIME(timestamp - (timestamp % 3600))';
        break;
      case 'day':
        groupBy = 'FROM_UNIXTIME(timestamp - (timestamp % 86400))';
        break;
      default:
        groupBy = 'FROM_UNIXTIME(timestamp - (timestamp % 3600))';
    }

    const query = `
      SELECT
        ${groupBy} as time_group,
        AVG(value) as avg_value,
        MIN(value) as min_value,
        MAX(value) as max_value,
        COUNT(*) as data_points
      FROM tag_data
      WHERE tag_id = ? AND timestamp BETWEEN ? AND ?
      GROUP BY time_group
      ORDER BY time_group
    `;

    const data = await executeQuery(query, [tagId, start, end]);
    res.json(data);
  } catch (error) {
    console.error('Error fetching range data:', error);
    res.status(500).json({ error: 'Failed to fetch range data' });
  }
});



export default router;
