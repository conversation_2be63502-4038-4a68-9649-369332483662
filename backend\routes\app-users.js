import express from 'express';
import { executeQuery } from '../config/database.js';
import bcrypt from 'bcryptjs';

const router = express.Router();

// Get all app users
router.get('/', async (req, res) => {
  try {
    const query = `
      SELECT 
        id,
        username,
        email,
        full_name,
        role,
        zone_access_type,
        allowed_zones,
        is_active,
        last_login,
        created_at,
        updated_at
      FROM app_users
      ORDER BY id
    `;
    
    const users = await executeQuery(query);
    res.json(users);
  } catch (error) {
    console.error('Error fetching app users:', error);
    res.status(500).json({ error: 'Failed to fetch app users' });
  }
});

// Get app user by ID
router.get('/:id', async (req, res) => {
  try {
    const userId = req.params.id;
    const query = `
      SELECT 
        id,
        username,
        email,
        full_name,
        role,
        zone_access_type,
        allowed_zones,
        is_active,
        last_login,
        created_at,
        updated_at
      FROM app_users
      WHERE id = ?
    `;
    
    const users = await executeQuery(query, [userId]);
    
    if (users.length === 0) {
      return res.status(404).json({ error: 'App user not found' });
    }
    
    res.json(users[0]);
  } catch (error) {
    console.error('Error fetching app user:', error);
    res.status(500).json({ error: 'Failed to fetch app user' });
  }
});

// Create new app user
router.post('/', async (req, res) => {
  try {
    const {
      username,
      password,
      email,
      full_name,
      role,
      zone_access_type,
      allowed_zones
    } = req.body;
    
    // Validate required fields
    if (!username || !password || !role) {
      return res.status(400).json({ error: 'Username, password, and role are required' });
    }
    
    // Validate zone_access_type
    if (!['all', 'specific', 'none'].includes(zone_access_type)) {
      return res.status(400).json({ error: 'Invalid zone access type' });
    }
    
    // Hash password
    const saltRounds = 10;
    const password_hash = await bcrypt.hash(password, saltRounds);
    
    // Prepare allowed_zones
    let zonesJson = null;
    if (zone_access_type === 'specific' && Array.isArray(allowed_zones)) {
      zonesJson = JSON.stringify(allowed_zones);
    }
    
    const query = `
      INSERT INTO app_users (
        username, password_hash, email, full_name, role,
        zone_access_type, allowed_zones, is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())
    `;
    
    const result = await executeQuery(query, [
      username, password_hash, email, full_name, role,
      zone_access_type, zonesJson
    ]);
    
    res.json({ 
      success: true, 
      message: 'App user created successfully',
      userId: result.insertId
    });
  } catch (error) {
    console.error('Error creating app user:', error);
    if (error.code === 'ER_DUP_ENTRY') {
      res.status(400).json({ error: 'Username already exists' });
    } else {
      res.status(500).json({ error: 'Failed to create app user' });
    }
  }
});

// Update app user
router.put('/:id', async (req, res) => {
  try {
    const userId = req.params.id;
    const {
      username,
      email,
      full_name,
      role,
      zone_access_type,
      allowed_zones,
      is_active
    } = req.body;
    
    // Validate zone_access_type
    if (!['all', 'specific', 'none'].includes(zone_access_type)) {
      return res.status(400).json({ error: 'Invalid zone access type' });
    }
    
    // Validate allowed_zones if access type is specific
    let zonesJson = null;
    if (zone_access_type === 'specific') {
      if (!Array.isArray(allowed_zones)) {
        return res.status(400).json({ error: 'allowed_zones must be an array for specific access' });
      }
      zonesJson = JSON.stringify(allowed_zones);
    }
    
    const query = `
      UPDATE app_users 
      SET username = ?, email = ?, full_name = ?, role = ?,
          zone_access_type = ?, allowed_zones = ?, is_active = ?, updated_at = NOW()
      WHERE id = ?
    `;
    
    await executeQuery(query, [
      username, email, full_name, role,
      zone_access_type, zonesJson, is_active ? 1 : 0, userId
    ]);
    
    res.json({ success: true, message: 'App user updated successfully' });
  } catch (error) {
    console.error('Error updating app user:', error);
    if (error.code === 'ER_DUP_ENTRY') {
      res.status(400).json({ error: 'Username already exists' });
    } else {
      res.status(500).json({ error: 'Failed to update app user' });
    }
  }
});

// Toggle app user active status
router.put('/:id/status', async (req, res) => {
  try {
    const userId = req.params.id;
    const { is_active } = req.body;
    
    const query = `
      UPDATE app_users 
      SET is_active = ?, updated_at = NOW()
      WHERE id = ?
    `;
    
    await executeQuery(query, [is_active ? 1 : 0, userId]);
    
    res.json({ success: true, message: 'App user status updated successfully' });
  } catch (error) {
    console.error('Error updating app user status:', error);
    res.status(500).json({ error: 'Failed to update app user status' });
  }
});

// Change app user password (admin can change without current password)
router.put('/:id/password', async (req, res) => {
  try {
    const userId = req.params.id;
    const { new_password, admin_override } = req.body;
    
    if (!new_password || new_password.length < 6) {
      return res.status(400).json({ error: 'New password must be at least 6 characters long' });
    }
    
    // Admin is always changing app user passwords, so no current password required
    // This is different from admin changing their own admin password
    
    // Hash the new password
    const saltRounds = 10;
    const password_hash = await bcrypt.hash(new_password, saltRounds);
    
    const query = `
      UPDATE app_users 
      SET password_hash = ?, updated_at = NOW()
      WHERE id = ?
    `;
    
    await executeQuery(query, [password_hash, userId]);
    
    res.json({ success: true, message: 'App user password updated successfully' });
  } catch (error) {
    console.error('Error updating app user password:', error);
    res.status(500).json({ error: 'Failed to update app user password' });
  }
});

// Delete app user
router.delete('/:id', async (req, res) => {
  try {
    const userId = req.params.id;
    
    const query = `DELETE FROM app_users WHERE id = ?`;
    const result = await executeQuery(query, [userId]);
    
    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'App user not found' });
    }
    
    res.json({ success: true, message: 'App user deleted successfully' });
  } catch (error) {
    console.error('Error deleting app user:', error);
    res.status(500).json({ error: 'Failed to delete app user' });
  }
});

export default router;